"use client";

import AsyncSelect from "react-select/async";
import { useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import { useSession } from "next-auth/react";
import Select, { SingleValue } from "react-select";

// Define the types for the options used in Select and AsyncSelect
interface Option {
  value: string;
  label: string;
}

interface Message {
  id?: string;
  title?: string;
  details?: string;
  templateId?: string;
  type?: string;
  recipients?: string[];
  groups?: string[];
  channels?: string[];
  upload?: File;
}

export default function AdminMessagesCreateForm({
  storeMessage,
}: {
  storeMessage: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const messageRef = useClickOutside(() => setCreating(false));
  const { data: session } = useSession();

  // Include 'type' and 'recipients' in the form data type
  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<Message & { upload: File; channels: string[]; title: string; type: string; recipients: string[] }>();

  const broadcast = watch(); // Watch the form data to conditionally render fields

  const loadTemplates = async (inputValue: string) => {
    const response = await fetch(`/api/messages/templates?s=${inputValue}`);
    const data = await response.json();
    return data;
  };

  const loadRecipientsOrGroups = async (inputValue: string, type: string) => {
    const endpoint =
      type === "individual"
        ? `/api/users?q=${inputValue}`
        : `/api/groups?q=${inputValue}`;
    const response = await fetch(endpoint);
    const data = await response.json();
    return data.map((item: any) => ({ label: item.name, value: item.id }));
  };

  const createMessage: SubmitHandler<
    Message & { upload: File; channels: string[]; title: string }
  > = (message) => {
    const data = new FormData();

    data.append("title", message.title || "");
    data.append("details", message.details || "");
    data.append("templateId", message.templateId || "");
    data.append("type", message.type || "");

    if (message.channels) {
      message.channels.forEach((c, i) => {
        data.append(`channels[${i}]`, c);
      });
    }

    if (message.upload) {
      data.append("image", message.upload);
    }

    // Handle recipients for individual customers
    if (message.type === "individual" && message.recipients) {
      message.recipients.forEach((id, index) => {
        data.append(`recipients[${index}]`, id);
      });
    }

    // Handle groups for group messages
    if (message.type === "groups" && message.groups) {
      message.groups.forEach((id, index) => {
        data.append(`groups[${index}]`, id);
      });
    }

    toast.promise(
      storeMessage(data),
      {
        loading: "Creating message...",
        success: "Message has been saved 👌",
        error: "Could not save message 🤯",
      },
      {
        position: "bottom-center",
      }
    );

    reset();
    setCreating(false);
  };

  // console log the data of the i

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createMessageButton"
        className="flex items-center justify-center rounded-lg bg-primary px-6 py-2 text-white hover:bg-default-800 dark:bg-primary"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>
        <span className="ml-2">New Message</span>
      </button>

      {creating && (
        <div
          ref={messageRef}
          id="drawer-create-message-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 shadow-sm transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Message
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>

          <form onSubmit={handleSubmit(createMessage)}>
            <div className="space-y-4">
              {/* Broadcast Type Selection */}
              <div>
                <label className="my-4 block text-sm font-medium text-default-900 dark:text-white">
                  Select type
                </label>
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={[
                        { value: "all", label: "All customers" },
                        { value: "groups", label: "Customer groups" },
                        { value: "individual", label: "Individual customers" },
                      ]}
                      value={
                        field.value
                          ? {
                              value: field.value,
                              label:
                                field.value === "all"
                                  ? "All customers"
                                  : field.value === "groups"
                                  ? "Customer groups"
                                  : "Individual customers",
                            }
                          : null
                      }
                      onChange={(selectedOption: SingleValue<Option>) =>
                        setValue("type", selectedOption?.value || "")
                      }
                      placeholder="Select broadcast type"
                    />
                  )}
                />
              </div>

              {/* Recipient Selection */}
              {broadcast.type === "individual" && (
                <div>
                  <label className="my-4 block text-sm font-medium text-default-900 dark:text-white">
                    Select recipient(s)
                  </label>
                  <Controller
                    name="recipients" // Correct field name
                    control={control}
                    render={({ field }) => (
                      <AsyncSelect
                        {...field}
                        isMulti
                        loadOptions={(inputValue) =>
                          loadRecipientsOrGroups(inputValue, "individual")
                        }
                        placeholder="Search for customers"
                      />
                    )}
                  />
                </div>
              )}

              {broadcast.type === "groups" && (
                <div>
                  <label className="my-4 block text-sm font-medium text-default-900 dark:text-white">
                    Select group(s)
                  </label>
                  <Controller
                    name="groups"
                    control={control}
                    render={({ field }) => (
                      <AsyncSelect
                        {...field}
                        isMulti
                        loadOptions={(inputValue) =>
                          loadRecipientsOrGroups(inputValue, "groups")
                        }
                        placeholder="Search for groups"
                      />
                    )}
                  />
                </div>
              )}

              {/* Template Selection */}
              <div>
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Template
                </label>
                <Controller
                  name="templateId"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      isClearable
                      isSearchable
                      loadOptions={loadTemplates}
                      getOptionLabel={(option: MessageTemplate) => option.name}
                      getOptionValue={(option) => option.id}
                      onChange={(option) => {
                        setValue("templateId", option?.id!);
                        setValue("details", option?.content!);
                      }}
                      placeholder="Select template"
                      required
                    />
                  )}
                />
              </div>

              {/* Description */}
              <div>
                <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={12}
                  {...register("details")}
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Enter message description here"
                />
              </div>

              {/* Channels */}
              <div>
                <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">
                  Channels
                </label>
                <div className="grid grid-cols-2 gap-4">
                  {["database", "sms", "fcm", "email"].map((m) => (
                    <div className="flex items-center gap-4" key={m}>
                      <input
                        type="checkbox"
                        id={m}
                        value={m}
                        {...register("channels")}
                        className="relative h-7 w-[3.25rem] cursor-pointer appearance-none rounded-full border-2 border-transparent bg-default-200 transition-colors duration-200 ease-in-out before:inline-block before:h-6 before:w-6 before:translate-x-0 before:transform before:rounded-full before:bg-white before:shadow before:transition before:duration-200 before:ease-in-out checked:!bg-primary checked:bg-none checked:before:translate-x-full focus:ring-0 focus:ring-transparent"
                      />
                      <label
                        className="block text-sm capitalize text-default-600"
                        htmlFor={m}
                      >
                        {m === "fcm" ? "InApp" : m}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Submit Button */}
              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800"
                >
                  Save message details
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
