"use client";

import AsyncSelect from "react-select/async";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import { useSession } from "next-auth/react";
import Select, { SingleValue } from "react-select";
import { api } from "@/lib/api";


// Define the types for the options used in Select and AsyncSelect
interface Option {
  value: string;
  label: string;
}

interface TemplateOption extends Option {
  content: string;
}

export default function AdminMessagesCreateForm({
  storeMessage,
}: {
  storeMessage: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const messageRef = useClickOutside(() => setCreating(false));
  const { data: session } = useSession();

  const {
    setValue,
    watch,
    handleSubmit,
    register,
    reset,
    control,
    formState: { isSubmitting },
  } = useForm<Message & { upload: File; channels: string[]; title: string; type: string; receipients: string[]; groups: string[]; message: string; branchId: string; vendorId: string }>({
    defaultValues: {
      type: "",         // Broadcast type (all, groups, individual)
      receipients: [],  // For individual customers
      groups: [],       // For customer groups
      channels: [],     // Channels like "InApp", "SMS", etc.
      templateId: "",   // Selected template
      message: "",      // The message body/content
      branchId: session?.branch?.id || "",
      vendorId: session?.vendor?.id || "",
      userId: session?.user?.id,
    },
  });

  const broadcast = watch();  // Watch the form data

  // Load available message templates using api
  const loadTemplates = async (): Promise<TemplateOption[]> => {
    const response = await api.get<PaginatedData<MessageTemplate>>("/message-templates");
    return response?.data?.map((template) => ({
      label: template.name,
      value: template.id,
      content: template.content,
    })) || [];
  };

  // Load recipients (users) or groups based on the broadcast type using api
  const loadRecipientsOrGroups = async (inputValue: string, type: string): Promise<Option[]> => {
    const endpoint = type === "individual" ? `/users?q=${inputValue}` : `/groups?q=${inputValue}`;
    const response = await api.get<PaginatedData<any>>(endpoint);
    return response?.data?.map((item) => ({ label: item.name, value: item.id })) || [];
  };

  const createMessage = async (message: {
    title: string;
    details: string;
    templateId: string;
    type: string;
    receipients: string[];
    groups: string[];
    channels: string[];
    upload?: File;
  }) => {
    const data = new FormData();
    data.append("title", message.title);
    data.append("details", message.details);
    data.append("templateId", message.templateId!);
    data.append("type", message.type);

    // Append the recipients or groups if necessary
    if (message.type === "individual") {
      message.receipients.forEach((id, index) => {
        data.append(`receipients[${index}]`, id);
      });
    } else if (message.type === "groups") {
      message.groups.forEach((id, index) => {
        data.append(`groups[${index}]`, id);
      });
    }

    message.channels.forEach((c, i) => {
      data.append(`channels[${i}]`, c);
    });

    if (message.upload) {
      data.append("image", message.upload!);
    }

    try {
      await storeMessage(data);
      toast.success("Message has been saved 👌");
      reset();
      setCreating(false);
    } catch (error) {
      toast.error("Could not save message 🤯");
    }
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createMessageButton"
        className="flex items-center justify-center rounded-lg bg-primary px-6 py-2 text-white hover:bg-default-800 dark:bg-primary"
        type="button"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          fill="currentColor"
          className="bi bi-plus-lg h-5 w-5"
          viewBox="0 0 16 16"
        >
          <path
            fillRule="evenodd"
            d="M8 2a.5.5 0 0 1 .5.5v5h5a.5.5 0 0 1 0 1h-5v5a.5.5 0 0 1-1 0v-5h-5a.5.5 0 0 1 0-1h5v-5A.5.5 0 0 1 8 2Z"
          />
        </svg>
        <span className="ml-2">New Message</span>
      </button>

      {creating && (
        <div
          ref={messageRef}
          id="drawer-create-message-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 shadow-sm transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Message
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-create-message-default"
            aria-controls="drawer-create-message-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>
          <form onSubmit={handleSubmit(createMessage)}>
            <div className="space-y-4">
              
              {/* Broadcast type */}
              <div>
                <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">Select type</label>
                <Controller
                  name="type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={[
                        { value: "all", label: "All customers" } as Option,
                        { value: "groups", label: "Customer groups" } as Option,
                        { value: "individual", label: "Individual customers" } as Option,
                      ]}
                      onChange={(option: SingleValue<Option>) => setValue("type", option?.value || "")}
                      value={field.value ? { value: field.value, label: field.value } : null}
                      placeholder="Select broadcast type"
                    />
                  )}
                />
              </div>

              {/* Show recipients or groups based on broadcast type */}
              {broadcast.type === "individual" && (
                <div>
                  <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">Select recipient(s)</label>
                  <Controller
                    name="receipients"
                    control={control}
                    render={({ field }) => (
                      <AsyncSelect
                        {...field}
                        isMulti
                        // ignore
                        
                        loadOptions={(inputValue) => loadRecipientsOrGroups(inputValue, "individual")}
                        placeholder="Search for customers"
                      />
                    )}
                  />
                </div>
              )}

              {broadcast.type === "groups" && (
                <div>
                  <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">Select group(s)</label>
                  <Controller
                    name="groups"
                    control={control}
                    render={({ field }) => (
                      <AsyncSelect
                        {...field}
                        isMulti
                        loadOptions={(inputValue) => loadRecipientsOrGroups(inputValue, "groups")}
                        placeholder="Search for groups"
                      />
                    )}
                  />
                </div>
              )}

              {/* Message Template */}
              <div>
                <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">Select a message template</label>
                <Controller
                  name="templateId"
                  control={control}
                  render={({ field }) => (
                    <AsyncSelect
                      {...field}
                      loadOptions={loadTemplates}
                      onChange={(option: SingleValue<TemplateOption>) => {
                        setValue("templateId", option?.value || "");
                        setValue("message", option?.content || "");
                      }}
                      placeholder="Select message template"
                    />
                  )}
                />
              </div>

              {/* Channels */}
              <div>
                <label className="mb-2 block text-sm font-medium text-default-900 dark:text-white">Select channel(s)</label>
                <div className="grid grid-cols-1 gap-2">
                  {["In App", "SMS", "Email"].map((channel) => (
                    <div key={channel} className="flex items-center">
                      <input
                        type="checkbox"
                        value={channel.toLowerCase()}
                        {...register("channels")}
                        className="mr-2"
                      />
                      <label className="text-sm text-default-600">{channel}</label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Submit button */}
              <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800"
                >
                  Send Message
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
