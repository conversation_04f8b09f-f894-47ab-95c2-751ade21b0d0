// pages/api/addRole.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { api } from "@/lib/api"; // Adjust path as necessary

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { user_id, role } = req.body;

    try {
      const response = await api.post("auth/addroletouser", { user_id, role });
      res.status(200).json(response.data);
    } catch (error) {
      console.error("Error assigning role:", error);
      res.status(500).json({ message: 'Error assigning role', error });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
