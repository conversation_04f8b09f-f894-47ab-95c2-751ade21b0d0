/* eslint-disable @next/next/no-async-client-component */
"use client";

import AsyncSelect from "react-select/async";
import makeAnimated from "react-select/animated";
import { useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import PhoneInput from "react-phone-number-input";
import { Branch, User } from "@/types"; // Adjust the import path if needed

export default function BranchStaffCreateForm({
  branch,
  storeStaff,
}: {
  branch: Branch;
  storeStaff: (data: FormData) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const ref = useClickOutside(() => setCreating(false));
  const animatedComponents = makeAnimated();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<User & { identifier: string; userId?: string; vendorId: string; role: string }>({
    defaultValues: {
      identifier: "",
      vendorId: branch.vendorId,
      role: "employee",
    },
  });

  const staff = watch();

  const fetchUsers = async (s: string) => {
    if (s.length > 3) {
      const users: User[] = await fetch(`/api/users?s=${s}`).then((r) => r.json());
      return users;
    }
    return [];
  };

  const createStaff: SubmitHandler<User & { identifier: string; userId?: string; vendorId: string; role: string }> = async (
    employee
  ) => {
    const data = new FormData();

    data.append("identifier", employee.identifier);
    data.append("role", employee.role);

    if (employee.userId) {
      data.append("userId", employee.userId);
    } else {
      data.append("firstName", employee.firstName);
      data.append("lastName", employee.lastName);
      data.append("email", employee.email);
      data.append("phone", employee.phone);
      data.append("idpass", employee.idpass);
    }

    try {
      await toast.promise(storeStaff(data), {
        loading: "Creating staff...",
        success: "Staff has been saved 👌",
        error: "Could not save staff 🤯",
      });

      if (employee.userId) {
        const roleData = { user_id: employee.userId, role: employee.role };

        await toast.promise(
          fetch('/api/addRole', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(roleData),
          }).then((res) => {
            if (!res.ok) throw new Error("Failed to assign role");
            return res.json();
          }),
          {
            loading: "Assigning role...",
            success: "Role assigned successfully 👌",
            error: "Failed to assign role 🤯",
          }
        );
      }
    } catch (e) {
      console.error(e);
      toast.error("Error saving staff or assigning role");
    }

    reset();
    setCreating(false);
  };

  return (
    <form onSubmit={handleSubmit(createStaff)}>
      <div className="space-y-4">
        <div className="mb-6 grid grid-cols-12">
          <label htmlFor="userId" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
            Select user
          </label>
          <Controller
            name="userId"
            control={control}
            render={({ field }) => (
              <AsyncSelect
                isClearable
                isSearchable
                loadOptions={fetchUsers}
                getOptionLabel={(user: any) => user.name}
                getOptionValue={(user: any) => user.id}
                onChange={(user: any) => setValue("userId", user.id)}
                components={animatedComponents}
                placeholder="Select user"
                className="col-span-7"
              />
            )}
          />
        </div>

        {!staff.userId && (
          <>
            {/* Staff Name Fields */}
            <div className="grid grid-cols-12">
              <label htmlFor="name" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff name
              </label>
              <div className="col-span-7 flex">
                <input
                  type="text"
                  {...register("firstName")}
                  id="name"
                  placeholder="First name"
                  required
                  className="block w-full rounded-l-lg border border-r-0 border-default-300 bg-default-50 p-3 text-sm"
                />
                <input
                  type="text"
                  {...register("lastName")}
                  placeholder="Last name"
                  required
                  className="block w-full rounded-r-lg border border-default-300 bg-default-50 p-3 text-sm"
                />
              </div>
            </div>

            {/* Email and Phone Fields */}
            <div className="grid grid-cols-12">
              <label htmlFor="email" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff email
              </label>
              <input
                type="email"
                {...register("email")}
                id="email"
                placeholder="Type staff email"
                required
                className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm"
              />
            </div>

            <div className="grid grid-cols-12">
              <label htmlFor="phone" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
                Staff phone
              </label>
              <div className="col-span-7">
                <Controller
                  name="phone"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <PhoneInput placeholder="Enter phone number" value={value} onChange={onChange} defaultCountry="KE" />
                  )}
                />
              </div>
            </div>
          </>
        )}

        <div className="grid grid-cols-12">
          <label htmlFor="role" className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white">
            Select Role
          </label>
          <select
            {...register("role")}
            id="role"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm"
          >
            <option value="employee">Employee</option>
            <option value="student">Student</option>
            <option value="accounts">Accounts</option>
            {/* Add other roles as needed */}
          </select>
        </div>

        <div className="flex w-full justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white"
          >
            Save staff details
          </button>
        </div>
      </div>
    </form>
  );
}
