"use client";

import AsyncSelect from "react-select/async";
import makeAnimated from "react-select/animated";
import { useState } from "react";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { useClickOutside } from "@/hooks/useClickOutside";
import PhoneInput from "react-phone-number-input";

export default function BranchStaffCreateForm({
  branch,
  storeStaff,
  addRoleToUser,
}: {
  branch: Branch;
  storeStaff: (data: FormData) => Promise<void>;
  addRoleToUser: (
    userId: string,
    role: string,
    firstName: string,
    lastName: string,
    phone: string,
    email: string,
    password?: string
  ) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);

  const ref = useClickOutside(() => setCreating(false));

  const animatedComponents = makeAnimated();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    setValue,
    control,
    formState: { errors, isSubmitting },
  } = useForm<User & { identifier: string; userId?: string; vendorId: string; role: string }>(
    {
      defaultValues: {
        identifier: "",
        vendorId: branch.vendorId,
        role: "staff", // Default role
      },
    },
  );

  const staff = watch();

  const fetchUsers = async (s: string) => {
    if (s.length > 3) {
      //@ts-ignore
      const users: User[] = await fetch(`/api/users?s=${s}`).then((r) =>
        r.json(),
      );

      return users;
    }

    return [];
  };

  const createStaff: SubmitHandler<
    User & { identifier: string; userId?: string; vendorId: string; role: string }
  > = async (
    employee: User & {
      identifier: string;
      userId?: string;
      vendorId: string;
      role: string;
    },
  ) => {
    const data = new FormData();

    data.append("identifier", employee.identifier);
    data.append("role", employee.role);

    if (employee.userId) {
      data.append("userId", employee.userId);
    } else {
      data.append("firstName", employee.firstName);
      data.append("lastName", employee.lastName);
      data.append("email", employee.email);
      data.append("phone", employee.phone);
      data.append("idpass", employee.idpass);
    }

    try {
      await toast.promise(
        storeStaff(data),
        {
          loading: "Creating staff...",
          success: "Staff has been saved 👌",
          error: "Could not save staff 🤯",
        },
        {
          position: "bottom-center",
        },
      );

      if (!employee.userId) {
        await addRoleToUser(
          employee.identifier,
          employee.role,
          employee.firstName,
          employee.lastName,
          employee.phone,
          employee.email
        );
      }
    } catch (error) {
      console.error("Error in staff creation or role assignment:", error);
      toast.error("Could not complete staff creation or role assignment.");
    }

    reset();
    setCreating(false);
  };

  return (
    <form onSubmit={handleSubmit(createStaff)}>
      <div className="space-y-4">
        <div className="mb-6 grid grid-cols-12">
          <label
            htmlFor="userId"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Select user
          </label>
          <Controller
            name="userId"
            control={control}
            rules={{ required: false }}
            render={({ field }) => (
              <AsyncSelect
                isClearable
                isSearchable
                loadOptions={fetchUsers}
                getOptionLabel={(user: any) => user.name}
                getOptionValue={(user: any) => user.id}
                onChange={(user: any) => setValue("userId", user.id)}
                components={animatedComponents}
                placeholder="Select user"
                className="col-span-7"
                classNames={{
                  control: () => "!py-[1px] !rounded-lg",
                  menu: () => "py-1",
                }}
              />
            )}
          />
        </div>

        {!staff.userId && (
          <>
            <div className="grid grid-cols-12">
              <label
                htmlFor="name"
                className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Staff name
              </label>
              <div className="col-span-7 flex">
                <input
                  type="text"
                  {...register("firstName")}
                  id="name"
                  className="block w-full rounded-l-lg border border-r-0 border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="First name"
                  required
                />
                <input
                  type="text"
                  {...register("lastName")}
                  id="name"
                  className="block w-full rounded-r-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder="Last name"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-12">
              <label
                htmlFor="name"
                className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Staff email
              </label>
              <input
                type="email"
                {...register("email")}
                id="email"
                className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Type staff email"
                required
              />
            </div>

            <div className="grid grid-cols-12">
  <label
    htmlFor="phone"
    className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
  >
    Staff phone
  </label>
  <div className="col-span-7">
    <div className="form-input relative w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
      <Controller
        name="phone"
        control={control}
        render={({ field: { onChange, value } }) => (
          <PhoneInput
            placeholder="Enter phone number"
            value={value}
            onChange={onChange}
            defaultCountry="KE"
            className="w-full"
          />
        )}
      />
    </div>
  </div>
</div>

            <div className="grid grid-cols-12">
              <label
                htmlFor="idpass"
                className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Staff Passport/National ID
              </label>
              <input
                type="text"
                {...register("idpass")}
                id="idpass"
                className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Type national ID/Passport number"
                required
              />
            </div>
          </>
        )}

        <div className="grid grid-cols-12">
          <label
            htmlFor="role"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Select Role
          </label>
          <select
            {...register("role")}
            id="role"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            defaultValue="employee"
          >
            <option value="employee">Employee</option>
            <option value="accounts">Accounts</option>
            <option value="customer">Customer</option>
            <option value="student">Student</option>
            <option value="unknown">Unknown</option>
            <option value="rider">Rider</option>
            <option value="waiter">Waiter</option>
            <option value="cashier">Cashier</option>
            <option value="bartender">Bartender</option>
            <option value="chef">Chef</option>
          </select>
        </div>

        <div className="grid grid-cols-12">
          <label
            htmlFor="name"
            className="col-span-5 mb-2 block text-sm font-medium text-default-900 dark:text-white"
          >
            Staff ID
          </label>
          <input
            type="text"
            {...register("identifier")}
            id="identifier"
            className="col-span-7 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Type staff ID"
            required
          />
        </div>

        <div className="flex w-full justify-end">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-60 justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
          >
            Save staff details
          </button>
        </div>
      </div>
    </form>
  );
}
