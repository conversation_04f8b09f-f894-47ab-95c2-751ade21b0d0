// pages/api/addRole.ts
import { NextApiRequest, NextApiResponse } from 'next';
import { api } from "@/lib/api"; // Assuming `api` is configured for your Axios instance

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    const { user_id, role } = req.body;

    try {
      // Make the call to the backend API
      const response = await api.post("auth/addroletouser", { user_id, role });
      
      // Send the backend response back to the client
      res.status(200).json(response.data);
    } catch (error) {
      console.error("Error assigning role:", error);
      res.status(500).json({ message: 'Error assigning role', error });
    }
  } else {
    // Handle invalid HTTP methods
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}
