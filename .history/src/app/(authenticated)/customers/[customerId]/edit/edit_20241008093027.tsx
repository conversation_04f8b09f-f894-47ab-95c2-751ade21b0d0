"use client";

import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import Datepicker from "react-tailwindcss-datepicker";

export default function EditCustomerForm({
  defaultValues,
  updateUser,
}: {
  defaultValues: any;
  updateUser: (data: FormData) => Promise<void>;
}) {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<User>({ defaultValues });

  const user = watch();

  const onSubmit = async (payload: User) => {
    const data = new FormData();

    data.append("firstName", payload.firstName);
    data.append("lastName", payload.lastName);
    data.append("email", payload.email);
    data.append("phone", payload.phone);
    // data.append("dob", payload.dob);

    toast.promise(updateUser(data), {
      loading: "Updating user...",
      success: "User updated!",
      error: "Error updating user",
    });
  };

  return (
    <div className="col-span-2">
      <div className="mb-4 rounded-lg border border-default-200 bg-white p-4 shadow-sm dark:border-default-700 dark:bg-default-800 sm:p-6 2xl:col-span-2">
        <h3 className="mb-4 text-xl font-semibold dark:text-white">
          General information
        </h3>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-6 gap-6">
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="first-name"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                First Name
              </label>
              <input
                type="text"
                {...register("firstName", { required: true })}
                id="first-name"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="Bonnie"
                required
              />
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="last-name"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Last Name
              </label>
              <input
                type="text"
                {...register("lastName", { required: true })}
                id="last-name"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="Green"
                required
              />
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="email"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Email
              </label>
              <input
                type="email"
                {...register("email", { required: true })}
                id="email"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="phone-number"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Phone Number
              </label>
              <input
                type="number"
                {...register("phone", { required: true })}
                id="phone-number"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="e.g. +(12)3456 789"
                required
              />
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="birthday"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Birthday
              </label>
              <Datepicker
                useRange={false}
                asSingle={true}
                value={{ startDate: user.dob, endDate: null }}
                onChange={(d) => {
                  if (d && d.startDate) {
                    setValue("dob", d.startDate.toString());
                  }
                }}
                containerClassName="flex"
                inputClassName="shadow-sm bg-default-50 border border-default-300 text-default-900 sm:text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500"
              />
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="organization"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Organization
              </label>
              <input
                type="text"
                name="organization"
                id="organization"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="Company Name"
                required
              />
            </div>
            <div className="sm:col-full col-span-6">
              <button
                className="rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                type="submit"
              >
                Save all
              </button>
            </div>
          </div>
        </form>
      </div>
      <div className="mb-4 rounded-lg border border-default-200 bg-white p-4 shadow-sm dark:border-default-700 dark:bg-default-800 sm:p-6 2xl:col-span-2">
        <h3 className="mb-4 text-xl font-semibold dark:text-white">
          Password information
        </h3>
        <form>
          <div className="grid grid-cols-6 gap-6">
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="current-password"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Current password
              </label>
              <input
                type="text"
                name="current-password"
                id="current-password"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="••••••••"
                required
              />
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="password"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                New password
              </label>
              <input
                data-popover-target="popover-password"
                data-popover-placement="bottom"
                type="password"
                id="password"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                placeholder="••••••••"
                required
              />
              <div
                data-popover=""
                id="popover-password"
                role="tooltip"
                className="position: absolute; inset: auto auto 0px 0px; margin: 0px; transform: translate(439px, -1788px); invisible absolute z-10 inline-block w-72 rounded-lg border border-default-200 bg-white text-sm font-light text-default-500 opacity-0 shadow-sm transition-opacity duration-300 dark:border-default-600 dark:bg-default-800 dark:text-default-400"
                data-popper-reference-hidden=""
                data-popper-escaped=""
                data-popper-placement="top"
              >
                <div className="space-y-2 p-3">
                  <h3 className="font-semibold text-default-900 dark:text-white">
                    Must have at least 6 characters
                  </h3>
                  <div className="grid grid-cols-4 gap-2">
                    <div className="h-1 bg-orange-300 dark:bg-orange-400"></div>
                    <div className="h-1 bg-orange-300 dark:bg-orange-400"></div>
                    <div className="h-1 bg-default-200 dark:bg-default-600"></div>
                    <div className="h-1 bg-default-200 dark:bg-default-600"></div>
                  </div>
                  <p>It’s better to have:</p>
                  <ul>
                    <li className="mb-1 flex items-center">
                      <svg
                        className="mr-2 h-4 w-4 text-green-400 dark:text-green-500"
                        aria-hidden="true"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      Upper &amp; lower case letters
                    </li>
                    <li className="mb-1 flex items-center">
                      <svg
                        className="mr-2 h-4 w-4 text-default-300 dark:text-default-400"
                        aria-hidden="true"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      A symbol (#$&amp;)
                    </li>
                    <li className="flex items-center">
                      <svg
                        className="mr-2 h-4 w-4 text-default-300 dark:text-default-400"
                        aria-hidden="true"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                          clipRule="evenodd"
                        ></path>
                      </svg>
                      A longer password (min. 12 chars.)
                    </li>
                  </ul>
                </div>
                <div
                  data-popper-arrow=""
                  className="position: absolute; left: 0px; transform: translate(139px, 0px);"
                ></div>
              </div>
            </div>
            <div className="col-span-6 sm:col-span-3">
              <label
                htmlFor="confirm-password"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Confirm password
              </label>
              <input
                type="text"
                name="confirm-password"
                id="confirm-password"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-default-900 shadow-sm focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600 sm:text-sm"
                placeholder="••••••••"
                required
              />
            </div>
            <div className="sm:col-full col-span-6">
              <button
                className="rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
                type="submit"
              >
                Save all
              </button>
            </div>
          </div>
        </form>
      </div>
      <div className="mb-4 rounded-lg border border-default-200 bg-white p-4 shadow-sm dark:border-default-700 dark:bg-default-800 sm:p-6 2xl:col-span-2">
        <div className="flow-root">
          <h3 className="text-xl font-semibold dark:text-white">Sessions</h3>
          <ul className="divide-y divide-default-200 dark:divide-default-700">
            {user?.devices?.map((device) => (
              <li className="py-4" key={device.id}>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-6 w-6 dark:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                      ></path>
                    </svg>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-base font-semibold text-default-900 dark:text-white">
                      California 123.123.123.123
                    </p>
                    <p className="truncate text-sm font-normal text-default-500 dark:text-default-400">
                      Chrome on macOS
                    </p>
                  </div>
                  <div className="inline-flex items-center">
                    <a
                      href="#"
                      className="mb-3 mr-3 rounded-lg border border-default-300 bg-white px-3 py-2 text-center text-sm font-medium text-default-900 hover:bg-default-100 focus:ring-4 focus:ring-default-300 dark:border-default-600 dark:bg-default-800 dark:text-default-400 dark:hover:bg-default-700 dark:hover:text-white"
                    >
                      Revoke
                    </a>
                  </div>
                </div>
              </li>
            ))}
          </ul>
          <div>
            <button className="rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800">
              See more
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
