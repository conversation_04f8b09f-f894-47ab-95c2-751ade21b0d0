import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("Durations API: Session found for user:", session.user.email);

    // Get query parameters from the request
    const searchParams = request.nextUrl.searchParams;
    const active = searchParams.get("active") || "true";
    const limit = searchParams.get("limit") || "100";

    console.log("Durations API: Making backend call with params:", { active, limit });

    // Call the backend API
    const response = await api.get("service-options/durations", {
      active,
      limit
    });

    console.log("Durations API: Backend response received:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json(response);
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Durations API error:", error);
    
    // Return detailed error information
    return NextResponse.json({ 
      success: false,
      error: "Failed to fetch durations",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
