import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
	const searchParams = request.nextUrl.searchParams;
	const s = searchParams.get("s") || "";
	const per = searchParams.get("per") || "10";
	const page = searchParams.get("page") || "1";

	const res = await api.get<PaginatedData<Product>>("products", { s, per, page });

	return NextResponse.json(res?.data);
}
