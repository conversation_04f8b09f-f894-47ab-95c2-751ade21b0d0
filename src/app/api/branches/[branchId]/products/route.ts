import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { branchId: string } }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const s = searchParams.get("s") || "";
    const per = searchParams.get("per") || "10";
    const page = searchParams.get("page") || "1";

    const queryParams: Record<string, string> = { s, per, page };

    const res = await api.get<PaginatedData<Product>>(
      `branches/${params.branchId}/products`,
      queryParams
    );

    return NextResponse.json(res?.data || []);
  } catch (error) {
    console.error("Error fetching branch products:", error);
    return NextResponse.json({ error: "Failed to fetch branch products" }, { status: 500 });
  }
}
