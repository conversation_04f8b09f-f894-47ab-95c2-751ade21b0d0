import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { branchId: string } }
) {
  const searchParams = request.nextUrl.searchParams;
  const per = searchParams.get("per") || "10";
  const page = searchParams.get("page") || "1";
  const order = searchParams.get("order") || "createdAt";
  const sort = searchParams.get("sort") || "asc";

  try {
    const response = await api.get(
      `branches/${params.branchId}/groups`,
      { per, page, order, sort }
    );
    return NextResponse.json(response);
  } catch (error: any) {
    console.error("Error fetching groups:", error);
    return NextResponse.json(
      { error: error.message || "Failed to fetch groups" },
      { status: error.status || 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { branchId: string } }
) {
  try {
    const body = await request.json();
    const response = await api.post(
      `branches/${params.branchId}/groups`,
      body
    );
    return NextResponse.json(response);
  } catch (error: any) {
    console.error("Error creating group:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create group" },
      { status: error.status || 500 }
    );
  }
} 