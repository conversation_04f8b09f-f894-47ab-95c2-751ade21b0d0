import { NextResponse } from 'next/server';
import { auth } from '@/auth';
import { api } from '@/lib/api';

export async function GET(
  request: Request,
  { params }: { params: { branchId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per') || '20');

    const response = await api.get<{
      data: any[];
      meta: {
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
      };
    }>(`branches/${params.branchId}/customers`, {
      page,
      per: perPage
    });

    if (!response) {
      return NextResponse.json({ error: 'Failed to fetch customers' }, { status: 500 });
    }

    const normalizedResponse = {
      ...response,
      meta: {
        ...response.meta,
        perPage: response.meta.per_page,
        currentPage: response.meta.current_page,
        lastPage: response.meta.last_page,
        firstPage: 1,
        firstPageUrl: null,
        lastPageUrl: null,
        nextPageUrl: response.meta.current_page < response.meta.last_page ? `?page=${response.meta.current_page + 1}` : null,
        previousPageUrl: response.meta.current_page > 1 ? `?page=${response.meta.current_page - 1}` : null,
      }
    };

    return NextResponse.json(normalizedResponse);
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: Request,
  { params }: { params: { branchId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const response = await api.post(`branches/${params.branchId}/customers`, body);

    if (!response) {
      return NextResponse.json({ error: 'Failed to create customer' }, { status: 500 });
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error creating customer:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
} 