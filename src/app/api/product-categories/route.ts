import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const s = searchParams.get("s") || "";
    const per = searchParams.get("per") || "20";
    const page = searchParams.get("page") || "1";

    const res = await api.get<PaginatedData<ProductCategory>>("product-categories", { 
      s, 
      per, 
      page 
    });

    return NextResponse.json(res || { data: [], meta: {} });
  } catch (error) {
    console.error("Error fetching product categories:", error);
    return NextResponse.json({ error: "Failed to fetch product categories" }, { status: 500 });
  }
}
