import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Admin Durations API: Fetching durations for admin:", session.user.email);

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const category = searchParams.get("category");
    const active = searchParams.get("active");
    const page = searchParams.get("page") || "1";
    const limit = searchParams.get("limit") || "10";

    // Build query parameters for backend API
    const queryParams: Record<string, string> = { page, limit };
    if (category) queryParams.category = category;
    if (active) queryParams.active = active;

    console.log("Admin Durations API: Query params:", queryParams);

    // Call the backend API
    const response = await api.get("service-options/durations", queryParams);

    console.log("Admin Durations API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json(response);
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Admin Durations API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to fetch durations",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Admin Durations API: Creating duration for admin:", session.user.email);

    // Get the request body
    const body = await request.json();
    
    console.log("Admin Durations API: Request body:", body);

    // Validate required fields
    if (!body.name || !body.minutes || !body.category) {
      return NextResponse.json({ 
        error: "Missing required fields",
        details: "Name, minutes, and category are required"
      }, { status: 400 });
    }

    // Validate category
    const validCategories = ["short", "medium", "long", "full-day"];
    if (!validCategories.includes(body.category)) {
      return NextResponse.json({ 
        error: "Invalid category",
        details: `Category must be one of: ${validCategories.join(", ")}`
      }, { status: 400 });
    }

    // Validate minutes
    if (body.minutes <= 0) {
      return NextResponse.json({ 
        error: "Invalid duration",
        details: "Duration must be greater than 0 minutes"
      }, { status: 400 });
    }

    // Call the backend API to create the duration
    const response = await api.post("service-options/durations", body);

    console.log("Admin Durations API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json({
        success: true,
        message: "Duration created successfully",
        data: response
      });
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Admin Durations API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to create duration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
