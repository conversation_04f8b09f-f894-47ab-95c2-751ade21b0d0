import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Admin Duration API: Fetching duration:", params.id);

    // Call the backend API to get the specific duration
    const response = await api.get(`service-options/durations/${params.id}`);

    console.log("Admin Duration API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json(response);
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Admin Duration API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to fetch duration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Admin Duration API: Updating duration:", params.id);

    // Get the request body
    const body = await request.json();
    
    console.log("Admin Duration API: Request body:", body);

    // Validate required fields
    if (!body.name || !body.minutes || !body.category) {
      return NextResponse.json({ 
        error: "Missing required fields",
        details: "Name, minutes, and category are required"
      }, { status: 400 });
    }

    // Validate category
    const validCategories = ["short", "medium", "long", "full-day"];
    if (!validCategories.includes(body.category)) {
      return NextResponse.json({ 
        error: "Invalid category",
        details: `Category must be one of: ${validCategories.join(", ")}`
      }, { status: 400 });
    }

    // Validate minutes
    if (body.minutes <= 0) {
      return NextResponse.json({ 
        error: "Invalid duration",
        details: "Duration must be greater than 0 minutes"
      }, { status: 400 });
    }

    // Call the backend API to update the duration
    const response = await api.put(`service-options/durations/${params.id}`, body);

    console.log("Admin Duration API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json({
        success: true,
        message: "Duration updated successfully",
        data: response
      });
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Admin Duration API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to update duration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Admin Duration API: Deleting duration:", params.id);

    // Call the backend API to delete the duration
    const response = await api.delete(`service-options/durations/${params.id}`);

    console.log("Admin Duration API: Backend response:", {
      success: response?.success,
      hasData: !!response?.data
    });

    if (response?.success) {
      return NextResponse.json({
        success: true,
        message: "Duration deleted successfully"
      });
    } else {
      throw new Error("Backend API returned unsuccessful response");
    }

  } catch (error) {
    console.error("Admin Duration API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to delete duration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
