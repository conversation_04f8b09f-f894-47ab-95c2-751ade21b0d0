import { NextResponse } from "next/server";
import { auth } from "@/auth";

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const session = await auth();

    // Check if user is authenticated and has admin role
    if (!session || !session.user.roles.some(role => role.name === "admin")) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // TODO: Replace with actual database queries
    const deliveryRates = {
      "CustomerPaymentReceived_fcm": {
        total: 200,
        delivered: 180,
        failed: 20,
        rate: 90.0
      },
      "CustomerPaymentReceived_sms": {
        total: 150,
        delivered: 145,
        failed: 5,
        rate: 96.67
      },
      "OrderStatusUpdate_mail": {
        total: 300,
        delivered: 295,
        failed: 5,
        rate: 98.33
      },
      "SystemAlert_database": {
        total: 100,
        delivered: 100,
        failed: 0,
        rate: 100.0
      }
    };

    return NextResponse.json(deliveryRates);
  } catch (error) {
    console.error("Error in delivery rates:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 