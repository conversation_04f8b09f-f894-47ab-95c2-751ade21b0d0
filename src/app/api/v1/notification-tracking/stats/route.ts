import { NextResponse } from "next/server";
import { auth } from "@/auth";

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    const session = await auth();

    // Check if user is authenticated and has admin role
    if (!session || !session.user.roles.some(role => role.name === "admin")) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    // TODO: Replace with actual database queries
    const stats = {
      active_count: 150,
      failed_last_hour: 5,
      alerts: [
        {
          id: "1",
          title: "High Failure Rate",
          message: "SMS delivery failure rate is above threshold",
          created_at: new Date().toISOString(),
        },
        {
          id: "2",
          title: "Service Warning",
          message: "FCM service experiencing delays",
          created_at: new Date(Date.now() - 3600000).toISOString(),
        },
      ],
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error in notification stats:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 