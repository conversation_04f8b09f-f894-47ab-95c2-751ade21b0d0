import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
    }

    // Return the session data that client components need
    return NextResponse.json({
      user: {
        id: session.user.id,
        roles: session.user.roles,
      },
      branch: session.branch,
      vendor: session.vendor,
      branchId: session.branch?.id,
      vendorId: session.vendor?.id || session.branch?.vendorId,
      isAdmin: session.user.roles?.some((role: any) => role.name === "admin") || false
    });
  } catch (error) {
    console.error("Error fetching session info:", error);
    return NextResponse.json({ error: "Failed to fetch session info" }, { status: 500 });
  }
}
