import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function DELETE(
  request: NextRequest,
  { params }: { params: { vendorId: string; specialityId: string } }
) {
  try {
    const { vendorId, specialityId } = params;
    // Call the backend API to remove the specialization
    await api.destroy(specialityId, `vendors/${vendorId}/specialities`);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing specialization from vendor:", error);
    return NextResponse.json(
      { error: "Failed to remove specialization from vendor" },
      { status: 500 }
    );
  }
} 