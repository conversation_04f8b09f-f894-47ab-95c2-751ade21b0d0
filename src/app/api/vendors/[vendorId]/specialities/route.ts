import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";
import { PaginatedData, Speciality } from "@/types";

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { vendorId } = params;
    const searchParams = request.nextUrl.searchParams;
    const s = searchParams.get("s") || "";

    const specialities = await api.get<PaginatedData<Speciality>>(`vendors/${vendorId}/specialities`, { s });
    return NextResponse.json(specialities?.data || []);
  } catch (error) {
    console.error("Error fetching vendor specialities:", error);
    return NextResponse.json(
      { error: "Failed to fetch vendor specialities" },
      { status: 500 }
    );
  }
}

// POST method removed - now handled by server actions in actions/vendors.ts
