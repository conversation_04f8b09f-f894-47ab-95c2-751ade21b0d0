import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { vendorId } = params;
    const searchParams = request.nextUrl.searchParams;
    
    // Build query parameters
    const queryParams: Record<string, string> = {};
    for (const [key, value] of searchParams.entries()) {
      queryParams[key] = value;
    }

    const vendor = await api.get(`vendors/${vendorId}`, queryParams);
    return NextResponse.json(vendor);
  } catch (error) {
    console.error("Error fetching vendor:", error);
    return NextResponse.json(
      { error: "Failed to fetch vendor" },
      { status: 500 }
    );
  }
}

// PUT method removed - now handled by server actions in actions/vendors.ts

export async function DELETE(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const { vendorId } = params;
    
    await api.destroy(vendorId, "vendors");
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting vendor:", error);
    return NextResponse.json(
      { error: "Failed to delete vendor" },
      { status: 500 }
    );
  }
}
