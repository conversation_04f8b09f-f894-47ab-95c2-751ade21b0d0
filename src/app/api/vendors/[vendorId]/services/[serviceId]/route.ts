import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function DELETE(
  request: NextRequest,
  { params }: { params: { vendorId: string; serviceId: string } }
) {
  try {
    const { vendorId, serviceId } = params;
    
    console.log(`Attempting to detach service ${serviceId} from vendor ${vendorId}`);
    console.log(`Calling backend API: vendors/${vendorId}/services/${serviceId}`);

    // Call the backend API to detach the service
    try {
      await api.destroy(serviceId, `vendors/${vendorId}/services`);
      console.log(`Successfully detached service ${serviceId} from vendor ${vendorId}`);
      return NextResponse.json({ success: true });
    } catch (apiError) {
      console.error("API error details:", apiError);
      return NextResponse.json(
        { error: `API error: ${apiError instanceof Error ? apiError.message : String(apiError)}` },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error detaching service from vendor:", error);
    return NextResponse.json(
      { error: `Server error: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { vendorId: string; serviceId: string } }
) {
  try {
    const { vendorId, serviceId } = params;
    const body = await request.json();
    const { active } = body;
    
    console.log(`Attempting to update service ${serviceId} status for vendor ${vendorId}`);
    console.log(`New status: ${active}`);

    // Call the backend API to update the service status
    try {
      await api.put(`vendors/${vendorId}/services/${serviceId}`, { active });
      console.log(`Successfully updated service ${serviceId} status for vendor ${vendorId}`);
      return NextResponse.json({ success: true });
    } catch (apiError) {
      console.error("API error details:", apiError);
      return NextResponse.json(
        { error: `API error: ${apiError instanceof Error ? apiError.message : String(apiError)}` },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error updating service status:", error);
    return NextResponse.json(
      { error: `Server error: ${error instanceof Error ? error.message : String(error)}` },
      { status: 500 }
    );
  }
} 