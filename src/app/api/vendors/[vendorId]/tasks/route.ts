import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";
import { PaginatedData, Task } from "@/types";

export async function GET(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const s = searchParams.get("s") || "";

    const res = await api.get<PaginatedData<Task>>(
      `vendors/${params.vendorId}/tasks`,
      { s }
    );

    return NextResponse.json(res?.data || []);
  } catch (error) {
    console.error("Error fetching vendor tasks:", error);
    return NextResponse.json({ error: "Failed to fetch vendor tasks" }, { status: 500 });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { vendorId: string } }
) {
  try {
    const body = await request.json();
    const { tasks, active } = body;

    if (!tasks || !Array.isArray(tasks) || tasks.length === 0) {
      return NextResponse.json(
        { error: "Tasks array is required and must not be empty" },
        { status: 400 }
      );
    }

    // Call the backend API to attach tasks
    await api.post(`vendors/${params.vendorId}/tasks`, {
      tasks,
      active: active !== undefined ? active : true
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error attaching tasks to vendor:", error);
    return NextResponse.json(
      { error: "Failed to attach tasks to vendor" },
      { status: 500 }
    );
  }
} 