import { api } from "@/lib/api";
import { NextRequest, NextResponse } from "next/server";

export async function PUT(
  request: NextRequest,
  { params }: { params: { vendorId: string; taskId: string } }
) {
  try {
    const { vendorId, taskId } = params;
    const body = await request.json();
    const { active } = body;
    
    console.log(`Updating task ${taskId} status for vendor ${vendorId} to ${active}`);

    // Call the backend API to update the task status
    const result = await api.put(`vendors/${vendorId}/tasks/${taskId}`, { active });
    
    return NextResponse.json(result);
  } catch (error) {
    console.error("Error updating vendor task status:", error);
    return NextResponse.json(
      { error: "Failed to update vendor task status" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { vendorId: string; taskId: string } }
) {
  try {
    const { vendorId, taskId } = params;
    
    console.log(`Detaching task ${taskId} from vendor ${vendorId}`);

    // Call the backend API to detach the task
    await api.destroy(taskId, `vendors/${vendorId}/tasks`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error detaching task from vendor:", error);
    return NextResponse.json(
      { error: "Failed to detach task from vendor" },
      { status: 500 }
    );
  }
}
