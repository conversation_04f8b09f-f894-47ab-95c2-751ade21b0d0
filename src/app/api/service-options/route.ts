import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("Service Options API: Creating service option for user:", session.user.email);

    // Get the request body
    const body = await request.json();
    
    console.log("Service Options API: Request body:", body);

    // Validate required fields
    if (!body.name || !body.type) {
      return NextResponse.json({ 
        error: "Missing required fields",
        details: "Name and type are required"
      }, { status: 400 });
    }

    // If type is duration, durationId is required
    if (body.type === "duration" && !body.durationId) {
      return NextResponse.json({ 
        error: "Missing duration ID",
        details: "Duration ID is required when type is 'duration'"
      }, { status: 400 });
    }

    // Call the backend API to create the service option
    const response = await api.post("service-options/direct-options", body);

    console.log("Service Options API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json({
        success: true,
        message: "Service option created successfully",
        data: response
      });
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Service Options API error:", error);
    
    // Return detailed error information
    return NextResponse.json({ 
      success: false,
      error: "Failed to create service option",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("Service Options API: Fetching service options for user:", session.user.email);

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const vendorId = searchParams.get("vendorId");
    const type = searchParams.get("type");
    const active = searchParams.get("active") || "true";

    // Build query parameters for backend API
    const queryParams: Record<string, string> = { active };
    if (vendorId) queryParams.vendorId = vendorId;
    if (type) queryParams.type = type;

    console.log("Service Options API: Query params:", queryParams);

    // Call the backend API
    const response = await api.get("service-options/direct-options", queryParams);

    console.log("Service Options API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json(response);
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Service Options API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to fetch service options",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
