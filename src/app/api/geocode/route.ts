import { NextRequest, NextResponse } from "next/server";
import { Client } from "@googlemaps/google-maps-services-js";

// Mark this route as dynamic
export const dynamic = 'force-dynamic';
export const revalidate = 0;

const client = new Client({});

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const placeId = searchParams.get("placeId");

  if (!placeId) {
    return NextResponse.json({ error: "Place ID is required" }, { status: 400 });
  }

  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    return NextResponse.json(
      { error: "Google Maps API key is not configured" },
      { status: 500 }
    );
  }

  try {
    console.log("Making request to Google Places API with placeId:", placeId);

    const response = await client.placeDetails({
      params: {
        place_id: placeId,
        key: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
        fields: ["formatted_address", "geometry", "address_components"],
      },
    });

    if (!response.data.result) {
      return NextResponse.json(
        { error: "No result returned from Google Places API" },
        { status: 404 }
      );
    }

    return NextResponse.json(response.data.result);
  } catch (error: any) {
    console.error("Geocoding error:", {
      message: error.message,
      code: error.code,
      status: error.status,
      response: error.response?.data,
    });
    
    return NextResponse.json(
      { 
        error: "Failed to fetch place details",
        details: error.message,
        code: error.code,
        status: error.status
      },
      { status: error.status || 500 }
    );
  }
} 