import { NextRequest, NextResponse } from "next/server";
import { api } from "@/lib/api";

interface ApiResponse<T> {
  data: T;
}

interface MessageAction {
  id: string;
  type_id: string;
  config: Record<string, any>;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
}

// GET /api/messages/[messageId]/actions
export async function GET(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    const response = await api.get<ApiResponse<MessageAction[]>>(
      `/api/v1/messages/${params.messageId}/actions`
    );
    return NextResponse.json(response?.data);
  } catch (error) {
    console.error('Error fetching message actions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch message actions' },
      { status: 500 }
    );
  }
}

// POST /api/messages/[messageId]/actions
export async function POST(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  try {
    const body = await request.json();
    const response = await api.post<ApiResponse<MessageAction>>(
      `/api/v1/messages/${params.messageId}/actions`,
      body
    );
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error creating message action:', error);
    return NextResponse.json(
      { error: 'Failed to create message action' },
      { status: 500 }
    );
  }
}

// PUT /api/messages/[messageId]/actions/[actionId]
export async function PUT(
  request: NextRequest,
  { params }: { params: { messageId: string; actionId: string } }
) {
  try {
    const body = await request.json();
    const response = await api.put<ApiResponse<MessageAction>>(
      `/api/v1/messages/${params.messageId}/actions/${params.actionId}`,
      body
    );
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error updating message action:', error);
    return NextResponse.json(
      { error: 'Failed to update message action' },
      { status: 500 }
    );
  }
}

// DELETE /api/messages/[messageId]/actions/[actionId]
export async function DELETE(
  request: NextRequest,
  { params }: { params: { messageId: string; actionId: string } }
) {
  try {
    await api.delete(
      `/api/v1/messages/${params.messageId}/actions/${params.actionId}`
    );
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting message action:', error);
    return NextResponse.json(
      { error: 'Failed to delete message action' },
      { status: 500 }
    );
  }
} 