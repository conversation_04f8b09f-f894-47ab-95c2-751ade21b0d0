import { NextResponse } from "next/server";
import { api } from "@/lib/api";
import { auth } from "@/auth";

interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
    firstPage: number;
    firstPageUrl: string;
    lastPageUrl: string;
    nextPageUrl: string | null;
    previousPageUrl: string | null;
  };
}

interface GroupMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  name: string;
  avatarUrl: string;
  meta: {
    pivot_group_id: string;
    pivot_user_id: string;
    pivot_created_at: string;
    pivot_updated_at: string;
  };
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.branch?.id) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = searchParams.get("page") || "1";
    const per = searchParams.get("per") || "10";
    const order = searchParams.get("order") || "firstName";
    const sort = searchParams.get("sort") || "desc";

    const response = await api.get<PaginatedResponse<GroupMember>>(
      `groups/${params.id}/members`,
      {
        page,
        per,
        order,
        sort
      }
    );

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error loading members:", error);
    return new NextResponse("Internal Server Error", { status: 500 });
  }
} 