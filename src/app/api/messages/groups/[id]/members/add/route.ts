import { NextRequest, NextResponse } from "next/server";
import { api } from "@/lib/api";
import { auth } from "@/auth";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.branch?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const memberId = formData.get("memberId");

    if (!memberId || typeof memberId !== "string") {
      return NextResponse.json(
        { error: "Member ID is required" },
        { status: 400 }
      );
    }

    const response = await api.post(`groups/${params.id}/members`, {
      userId: memberId
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error adding member:", error);
    return NextResponse.json(
      { error: "Failed to add member" },
      { status: 500 }
    );
  }
} 