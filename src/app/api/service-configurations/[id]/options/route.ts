import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { api } from '@/lib/api';

// GET /api/service-configurations/[id]/options - Get configuration options
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Configuration Options API: Fetching options for configuration:', params.id);
    
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Call the backend API
    const response = await api.get(`service-options/service-configurations/${params.id}/options`);
    
    console.log('Configuration Options API: Backend response:', {
      hasData: !!response,
      optionCount: Array.isArray(response) ? response.length : 0
    });

    if (response) {
      return NextResponse.json({
        success: true,
        data: response || []
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to fetch configuration options' },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error('Configuration Options API error:', error);
    
    // Handle specific error cases
    if (error.response?.status === 404) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/service-configurations/[id]/options - Create new option
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Configuration Options API: Creating option for configuration:', params.id);
    
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json(
        { success: false, error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log('Configuration Options API: Request body:', body);

    // Call the backend API
    const response = await api.post(`service-options/service-configurations/${params.id}/options`, body);
    
    console.log('Configuration Options API: Backend response:', {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json({
        success: true,
        data: response
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to create option' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Configuration Options API error:', error);
    
    // Handle specific error cases
    if (error.response?.status === 404) {
      return NextResponse.json(
        { success: false, error: 'Configuration not found' },
        { status: 404 }
      );
    }
    
    if (error.response?.status === 400) {
      return NextResponse.json(
        { success: false, error: error.response.data?.message || 'Bad request' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
