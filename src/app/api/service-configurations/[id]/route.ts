import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Service Configuration API: Fetching configuration:", params.id);

    // Call the backend API
    const response = await api.get(`service-options/service-configurations/${params.id}`);

    console.log("Service Configuration API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json(response);
    } else {
      return NextResponse.json({
        success: false,
        error: "Configuration not found"
      }, { status: 404 });
    }

  } catch (error: any) {
    console.error("Service Configuration API error:", error);

    // Handle specific error cases
    if (error.response?.status === 404) {
      return NextResponse.json({
        success: false,
        error: "Configuration not found"
      }, { status: 404 });
    }

    return NextResponse.json({
      success: false,
      error: "Failed to fetch service configuration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Service Configuration API: Updating configuration:", params.id);

    // Get the request body
    const body = await request.json();
    
    console.log("Service Configuration API: Request body:", body);

    // Call the backend API
    const response = await api.put(`service-options/service-configurations/${params.id}`, body);

    console.log("Service Configuration API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json({
        success: true,
        message: "Service configuration updated successfully",
        data: response
      });
    } else {
      throw new Error("Backend API returned unsuccessful response");
    }

  } catch (error) {
    console.error("Service Configuration API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to update service configuration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Service Configuration API: Deleting configuration:", params.id);

    // Call the backend API
    const response = await api.delete(`service-options/service-configurations/${params.id}`);

    console.log("Service Configuration API: Backend response:", {
      success: response?.success
    });

    if (response?.success) {
      return NextResponse.json({
        success: true,
        message: "Service configuration deleted successfully"
      });
    } else {
      throw new Error("Backend API returned unsuccessful response");
    }

  } catch (error) {
    console.error("Service Configuration API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to delete service configuration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
