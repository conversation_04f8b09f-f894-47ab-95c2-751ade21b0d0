import { api } from "@/lib/api";
import { auth } from "@/auth";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Service Configurations Health Check: Starting validation");

    // Get a sample of configurations to validate
    const response = await api.get("service-options/service-configurations", { 
      limit: "10",
      include: "options" 
    });

    if (!response) {
      return NextResponse.json({
        success: false,
        error: "Backend API is not responding properly",
        status: "unhealthy"
      }, { status: 503 });
    }

    const configurations = Array.isArray(response) ? response : [];
    const healthIssues: string[] = [];
    let healthyCount = 0;
    let totalCount = configurations.length;

    // Validate each configuration
    for (const config of configurations) {
      try {
        // Check for data integrity issues
        if (config.options) {
          for (const option of config.options) {
            if (option.priceAdjustment !== null && 
                option.priceAdjustment !== undefined && 
                typeof option.priceAdjustment !== 'number') {
              healthIssues.push(`Configuration ${config.id} has invalid priceAdjustment in option ${option.id}`);
            }
          }
        }
        healthyCount++;
      } catch (error) {
        healthIssues.push(`Configuration ${config.id} validation failed: ${error}`);
      }
    }

    const healthStatus = healthIssues.length === 0 ? "healthy" : "degraded";

    return NextResponse.json({
      success: true,
      status: healthStatus,
      data: {
        totalConfigurations: totalCount,
        healthyConfigurations: healthyCount,
        issues: healthIssues,
        timestamp: new Date().toISOString(),
        recommendations: healthIssues.length > 0 ? [
          "Run data migration to fix priceAdjustment fields",
          "Add backend validation for numeric fields",
          "Consider adding database constraints"
        ] : []
      }
    });

  } catch (error: any) {
    console.error("Service Configurations Health Check error:", error);
    
    return NextResponse.json({
      success: false,
      error: "Health check failed",
      status: "unhealthy",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
