import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { api } from "@/lib/api";

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Service Configurations API: Creating configuration for user:", session.user.email);

    // Get the request body
    const body = await request.json();
    
    console.log("Service Configurations API: Request body:", body);

    // Validate required fields
    if (!body.name || !body.serviceId) {
      return NextResponse.json({ 
        error: "Missing required fields",
        details: "Name and serviceId are required"
      }, { status: 400 });
    }

    // Call the backend API to create the service configuration
    const response = await api.post("service-options/service-configurations", body);

    console.log("Service Configurations API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json({
        success: true,
        message: "Service configuration created successfully",
        data: response
      });
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Service Configurations API error:", error);
    
    // Return detailed error information
    return NextResponse.json({ 
      success: false,
      error: "Failed to create service configuration",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has admin role
    if (!session.user.roles.some(role => role.name === "admin")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    console.log("Service Configurations API: Fetching configurations for user:", session.user.email);

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const serviceId = searchParams.get("serviceId");
    const active = searchParams.get("active");
    const search = searchParams.get("search");
    const page = searchParams.get("page") || "1";
    const limit = searchParams.get("limit") || "20";

    // Build query parameters for backend API
    const queryParams: Record<string, string> = { page, limit };
    if (serviceId) queryParams.serviceId = serviceId;
    if (active) queryParams.active = active;
    if (search) queryParams.search = search;

    console.log("Service Configurations API: Query params:", queryParams);

    // Call the backend API
    const response = await api.get("service-options/service-configurations", queryParams);

    console.log("Service Configurations API: Backend response:", {
      hasData: !!response
    });

    if (response) {
      return NextResponse.json(response);
    } else {
      throw new Error("Backend API returned no response");
    }

  } catch (error) {
    console.error("Service Configurations API error:", error);
    
    return NextResponse.json({ 
      success: false,
      error: "Failed to fetch service configurations",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
