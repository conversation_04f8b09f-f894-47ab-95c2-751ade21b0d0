import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { Modifier, UpdateModifierRequest } from "@/types/modifiers";

// Use a fallback value if the environment variable is not set
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:44334/v1";

// GET /api/modifiers/[id] - Get a specific modifier
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const id = params.id;
  
  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const url = vendorId 
    ? `${API_URL}/vendors/${vendorId}/modifier-options/${id}`
    : `${API_URL}/modifiers/${id}`;

  try {
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch modifier: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Exception in GET /api/modifiers/[id]:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/modifiers/[id] - Update a specific modifier
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const id = params.id;
  
  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const url = vendorId 
    ? `${API_URL}/vendors/${vendorId}/modifier-options/${id}`
    : `${API_URL}/modifiers/${id}`;

  try {
    const body = await request.json();
    
    // Transform the data to match the API's expected format
    const apiData: Record<string, any> = {};
    
    if (body.name !== undefined) apiData.name = body.name;
    if (body.type !== undefined) apiData.type = body.type;
    if (body.description !== undefined) apiData.description = body.description;
    // Always include default_price_adjustment, default to 0 if not provided
    apiData.default_price_adjustment = body.default_price_adjustment !== undefined ? Number(body.default_price_adjustment) : 0;
    if (body.active !== undefined) apiData.active = body.active;

    console.log("Updating modifier with data:", apiData);

    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.accessToken}`,
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to update modifier: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Exception in PUT /api/modifiers/[id]:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/modifiers/[id] - Delete a specific modifier
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const id = params.id;
  
  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const url = vendorId 
    ? `${API_URL}/vendors/${vendorId}/modifier-options/${id}`
    : `${API_URL}/modifiers/${id}`;

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to delete modifier: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Exception in DELETE /api/modifiers/[id]:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 