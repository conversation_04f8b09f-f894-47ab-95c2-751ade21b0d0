import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { Modifier, CreateModifierRequest } from "@/types/modifiers";

// Use a fallback value if the environment variable is not set
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:44334/v1";

// GET /api/modifiers - Get all modifiers
export async function GET(request: NextRequest) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const url = vendorId 
    ? `${API_URL}/vendors/${vendorId}/modifier-options`
    : `${API_URL}/modifiers`;

  try {
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${session.accessToken}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to fetch modifiers: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Exception in GET /api/modifiers:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/modifiers - Create a new modifier
export async function POST(request: NextRequest) {
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Get vendor ID from session if available
  const vendorId = session.vendor?.id;
  
  // Construct the URL based on whether we have a vendor ID
  const url = vendorId 
    ? `${API_URL}/vendors/${vendorId}/modifier-options`
    : `${API_URL}/modifiers`;

  try {
    const body = await request.json();
    
    // Transform the data to match the API's expected format
    const apiData = {
      name: body.name,
      type: body.type,
      description: body.description,
      default_price_adjustment: body.default_price_adjustment !== undefined ? Number(body.default_price_adjustment) : 0,
      active: body.active
    };

    console.log("Creating modifier with data:", apiData);

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${session.accessToken}`,
      },
      body: JSON.stringify(apiData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API error: ${response.status} ${response.statusText}`);
      console.error(`Error details: ${errorText}`);
      return NextResponse.json(
        { error: `Failed to create modifier: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Exception in POST /api/modifiers:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 