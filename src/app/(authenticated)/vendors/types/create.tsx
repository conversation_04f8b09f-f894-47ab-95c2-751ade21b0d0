"use client";

import { useState, useRef, ChangeEvent } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Controller, useForm } from "react-hook-form";
import makeAnimated from "react-select/animated";
import AsyncSelect from "react-select/async";
import Select from "react-select";
import { toast } from "react-hot-toast";
import Image from "next/image";

export default function AdminVendorTypesCreateForm({
  tasks,
  storeVendorType,
  taskId = "",
  serviceId = "",
}: {
  storeVendorType: (data: FormData) => Promise<void>;
  tasks?: Task[];
  taskId?: string;
  serviceId?: string;
}) {
  const [creating, setCreating] = useState(false);
  const [preview, setPreview] = useState<string>();

  const animatedComponents = makeAnimated();

  const {
    watch,
    handleSubmit,
    register,
    reset,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<
    Partial<
      VendorType & {
        taskId: { value: string };
        serviceId: { value: string };
        upload: File;
      }
    >
  >({
    defaultValues: {
      name: "",
      image: null,
      details: "",
      taskId: { value: taskId },
      serviceId: { value: serviceId },
    },
  });

  const values = watch();

  const imageInputRef = useRef<HTMLInputElement | null>();

  const { ref: registerRef, ...rest } = register("image");

  const handleUploadedFile = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;

    if (files) {
      const file = files[0];
      setValue("upload", file);
      const urlImage = URL.createObjectURL(file);

      setPreview(urlImage);
    }
  };

  const fetchServices = async (s: string) => {
    if (s.length > 3) {
      const services: Service[] = await fetch(
        `/api/tasks/${values.taskId?.value}/services`,
      ).then((r) => r.json());

      return services?.map((service) => ({
        value: service.id,
        label: service.name,
      }));
    }

    return [];
  };

  const createVendorType: SubmitHandler<
    Partial<VendorType & { upload: File }>
  > = (type: Partial<VendorType & { upload: File }>) => {
    const data = new FormData();

    data.append("name", type.name!);
    data.append("details", type.details!);
    //@ts-ignore
    data.append("serviceId", type.serviceId?.value);
    data.append("image", type.upload!);

    toast.promise(
      storeVendorType(data),
      {
        loading: "Creating vendor type...",
        success: "VendorType has been saved 👌",
        error: "Could not save type 🤯",
      },
      {
        position: "bottom-center",
      },
    );

    reset();

    setCreating(false);
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        id="createVendorTypeButton"
        className="rounded-lg bg-primary px-5 py-3 text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
        type="button"
        data-drawer-target="drawer-create-type-default"
        data-drawer-show="drawer-create-type-default"
        aria-controls="drawer-create-type-default"
        data-drawer-placement="right"
      >
        Add new vendor type
      </button>

      {creating && (
        <div
          id="drawer-create-type-default"
          className="fixed right-0 top-0 z-40 h-screen w-full max-w-xs overflow-y-auto bg-white p-4 transition-transform dark:bg-default-800"
          tabIndex={-1}
          aria-labelledby="drawer-label"
          aria-hidden="true"
        >
          <h5
            id="drawer-label"
            className="mb-6 inline-flex items-center text-sm font-semibold uppercase text-default-500 dark:text-default-400"
          >
            New Vendor Type
          </h5>
          <button
            onClick={() => setCreating(!creating)}
            type="button"
            data-drawer-dismiss="drawer-create-type-default"
            aria-controls="drawer-create-type-default"
            className="absolute right-2.5 top-2.5 inline-flex items-center rounded-lg bg-transparent p-1.5 text-sm text-default-400 hover:bg-default-200 hover:text-default-900 dark:hover:bg-default-600 dark:hover:text-white"
          >
            <svg
              aria-hidden="true"
              className="h-5 w-5"
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              ></path>
            </svg>
            <span className="sr-only">Close menu</span>
          </button>
          <form
            action="#"
            onSubmit={handleSubmit(createVendorType)}
            className="space-y-4"
          >
            <div>
              <label
                htmlFor="name"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Name
              </label>
              <input
                type="text"
                id="name"
                {...register("name")}
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Vendor type"
                required
              />
            </div>

            {tasks && (
              <div className="mb-6">
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Select task
                </label>
                <Controller
                  name="taskId"
                  control={control}
                  render={({ field }) => (
                    <Select
                      {...field}
                      options={tasks.map((task) => ({
                        value: task.id,
                        label: task.name,
                      }))}
                      components={animatedComponents}
                      placeholder="Select task"
                      required
                      isSearchable
                      classNames={{
                        control: () =>
                          "py-[1px] !bg-default-50 !border !border-default-300 !text-default-900 !text-sm !rounded-lg focus:ring-default-600 focus:border-default-600 block w-full px-2.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500",
                      }}
                    />
                  )}
                />
              </div>
            )}

            {values.taskId && !serviceId && (
              <div className="mb-6">
                <label
                  htmlFor="name"
                  className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                >
                  Select service
                </label>
                <Controller
                  name="serviceId"
                  control={control}
                  rules={{ required: true }}
                  render={({ field: { onChange, value } }) => (
                    <AsyncSelect
                      isClearable
                      isSearchable
                      // cacheOptions
                      //@ts-ignore
                      loadOptions={fetchServices}
                      components={animatedComponents}
                      placeholder="Select services"
                      required
                      classNames={{
                        control: () =>
                          "py-[1px] !bg-default-50 !border !border-default-300 !text-default-900 !text-sm !rounded-lg focus:ring-default-600 focus:border-default-600 block w-full px-2.5 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500",
                      }}
                      onChange={onChange}
                      value={value}
                    />
                  )}
                />
              </div>
            )}

            <div>
              <label
                htmlFor="image"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Image or icon
              </label>
              <label className="flex h-40 w-full items-center justify-center rounded-lg border border-dashed border-default-300 bg-default-50 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600">
                <input
                  type="file"
                  name="image"
                  id="image"
                  className="hidden"
                  onChange={handleUploadedFile}
                />
                {preview ? (
                  <Image
                    src={preview}
                    alt="preview"
                    width={100}
                    height={100}
                    className="w-1/2"
                  />
                ) : (
                  <p>Click to select file</p>
                )}
              </label>
            </div>

            <div>
              <label
                htmlFor="description"
                className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
              >
                Description
              </label>
              <textarea
                id="description"
                rows={4}
                {...register("details")}
                className=".5 block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder="Enter event description here"
              />
            </div>

            <div className="bottom-0 left-0 flex w-full justify-center space-x-4 pb-4 md:absolute md:px-4">
              <button
                type="submit"
                className="w-full justify-center rounded-lg bg-primary px-5 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
              >
                Save type details
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
