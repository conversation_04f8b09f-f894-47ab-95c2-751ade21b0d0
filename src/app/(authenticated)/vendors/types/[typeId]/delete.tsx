"use client";

import toast from "react-hot-toast";

export default function AdminVendorTypesDelete({
  deleteVendorType,
  typeId,
}: {
  deleteVendorType: (typeId: string) => Promise<void>;
  typeId: string;
}) {
  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    toast.promise(
      deleteVendorType(typeId),
      {
        loading: "Deleting vendor type...",
        success: "Vendor type has been deleted 👌",
        error: "Could not delete vendor type 🤯",
      },
      {
        position: "bottom-center",
      },
    );
  };

  return (
    <form method="POST" onSubmit={onSubmit}>
      <button className="text-red-600 hover:text-red-900" type="submit">
        Delete
      </button>
    </form>
  );
}
