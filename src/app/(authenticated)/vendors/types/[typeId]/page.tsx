import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { Metadata } from "next";
import { cache } from "react";
import AdminVendorCategoryCreateForm from "../../categories/create";
import VendorCategoriesTable from "@/components/tables/vendor-categories-table";

const fetchType = cache((typeId: string) =>
  api.get<Task>(`vendor-types/${typeId}`),
);

export const generateMetadata = async ({
  params,
}: {
  params: { typeId: string };
}): Promise<Metadata> => {
  const type = await fetchType(params.typeId);

  return {
    title: type?.name,
    description: type?.details,
  };
};

export default async function AdminVendorTypeIndex({
  params,
}: {
  params: { typeId: string };
}) {
  const type = await fetchType(params.typeId);

  const categories = await api.get<PaginatedData<VendorCategory>>(
    `vendor-types/${params.typeId}/categories`,
  );

  const storeVendorCategory = async (vendorcategory: FormData) => {
    "use server";

    await api.post("vendor-categories", vendorcategory);

    revalidatePath(`vendors/types/${params.typeId}`);
  };

  const updateVendorCategory = async (data: FormData) => {
    "use server";

    await api.put(`vendor-categories/${data.get("id")}`, data);

    revalidatePath(`vendors/types/${params.typeId}`);
  };

  const deleteVendorCategory = async (data: FormData) => {
    "use server";

    await api.destroy(data.get("id") as string, "vendor-categories");

    revalidatePath(`vendors/types/${params.typeId}`);
  };

  return (
    <div className="py-4">
      {categories && (
        <VendorCategoriesTable
          data={categories.data}
          meta={categories.meta}
          typeDetails={type?.details || ""}
          storeVendorCategory={storeVendorCategory}
          deleteVendorCategory={deleteVendorCategory}
          updateVendorCategory={updateVendorCategory}
          typeId={params.typeId}
          CreateForm={AdminVendorCategoryCreateForm}
        />
      )}
    </div>
  );
}
