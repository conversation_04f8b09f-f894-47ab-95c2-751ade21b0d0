import { api, imagePath } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import AdminVendorCategorysCreateForm from "./create";
import AdminVendorCategorysEditForm from "./edit";
import { revalidatePath } from "next/cache";
import Image from "next/image";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

export const metadata: Metadata = {
  title: "Vendor Types",
};

export default async function AdminVendorCategoryIndex() {
  const categorys = await api.get<PaginatedData<VendorCategory>>(
    "vendor-categories",
    { per: 30 },
  );

  const storeVendorCategory = async (category: FormData) => {
    "use server";

    await api.post("vendor-categories", category);
    revalidatePath("/vendors/categories");
  };

  const updateVendorCategory = async (category: FormData) => {
    "use server";

    await api.put(`vendor-categories/${category.get("id")}`, category);
    revalidatePath("/vendors/categories");
  };

  return (
    <div className="flex flex-1 flex-col px-4 pt-8">
      <div className="grid flex-1 grid-cols-5 gap-3">
        {categorys?.data.map((category) => (
          <div
            className="rounded-md border border-default-200 p-4"
            key={category.id}
          >
            <div className="flex space-x-1">
              <Link href={`/vendors/categories/${category.id}`}>
                <Image
                  src={imagePath(category.image?.url)}
                  alt={category.name}
                  width={100}
                  height={100}
                  className="size-16 rounded-full"
                />
              </Link>
              <div className="w-3/5">
                <div className="flex justify-end">
                  <AdminVendorCategorysEditForm
                    defaultValues={category}
                    updateVendorCategory={updateVendorCategory}
                  />
                </div>

                <Link href={`/vendors/categories/${category.id}`}>
                  <h3>{category.name}</h3>
                </Link>
              </div>
            </div>

            <article>{category.details}</article>
          </div>
        ))}
      </div>

      <AdminVendorCategorysCreateForm
        storeVendorCategory={storeVendorCategory}
      />
    </div>
  );
}
