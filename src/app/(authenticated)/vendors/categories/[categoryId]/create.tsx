"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, Controller, SubmitHandler } from "react-hook-form";
import makeAnimated from "react-select/animated";
import AsyncSelect from "react-select/async";
import Select from "react-select";
import { toast } from "react-hot-toast";
import PhoneInput from "react-phone-number-input";

export default function AdminVendorCategoryCreateVendorForm({
  tasks,
  storeVendor,
}: {
  tasks: Task[];
  storeVendor: (data: any) => Promise<void>;
}) {
  const [creating, setCreating] = useState(false);
  const [step, setStep] = useState(0);

  const animatedComponents = makeAnimated();

  const {
    register,
    watch,
    setValue,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<{
    firstName: string;
    lastName: string;
    phone: string;
    idpass: string;
    email: string;
    password: string;
    confirm_password: string;

    vendor: {
      name: string;
      email: string;
      phone: string;
      website: string;
      logo: File | null;
      reg: string;
      kra: string;
    };

    taskId: string;
    serviceId: string;
    vendorTypeId: string;
    vendorCategoryId: string;
  }>({
    defaultValues: {
      firstName: "",
      lastName: "",
      phone: "",
      idpass: "",
      email: "",
      password: "",
      confirm_password: "",

      vendor: {
        name: "",
        email: "",
        phone: "",
        website: "",
        logo: null,
        reg: "",
        kra: "",
      },

      taskId: "",
      serviceId: "",
      vendorTypeId: "",
      vendorCategoryId: "",
    },
  });

  const onboard = watch();

  const steps = [
    "Admin account",
    "Vendor information",
    "Vendor grouping",
    "Review & confirm",
  ];

  const fetchServices = async (s: string) => {
    if (s.length > 3) {
      const services: Service[] = await fetch(
        //@ts-ignore
        `/api/tasks/${onboard.taskId.value}/services?s=${s}`,
      ).then((r) => r.json());

      return services?.map((service) => ({
        value: service.id,
        label: service.name,
      }));
    }

    return [];
  };

  const fetchVendorTypes = async (s: string) => {
    if (s.length > 3) {
      const types: VendorType[] = await fetch(
        //@ts-ignore
        `/api/services/${onboard.serviceId.value}/vendor-types?s=${s}`,
      ).then((r) => r.json());

      return types?.map((t) => ({ value: t.id, label: t.name }));
    }

    return [];
  };

  const fetchVendorCategories = async (s: string) => {
    if (s.length > 3) {
      const categories: VendorCategory[] = await fetch(
        //@ts-ignore
        `/api/vendor-types/${onboard.vendorTypeId.value}/categories?s=${s}`,
      ).then((r) => r.json());

      return categories?.map((category) => ({
        value: category.id,
        label: category.name,
      }));
    }

    return [];
  };

  const handleLogoUpload = (e: any) => {
    const file = e.target.files[0];

    setValue("vendor.logo", file);

    if (file) {
      const reader = new FileReader();

      reader.onload = function (e) {
        const img = document.createElement("img");

        img.src = e.target?.result as string;

        img.onload = function () {
          const vendor = onboard.vendor;

          onboard.vendor = vendor;
        };
      };

      reader.readAsDataURL(file);
    }
  };

  const createVendor: SubmitHandler<any> = (onb: any) => {
    const data = new FormData();

    data.append("firstName", onb.firstName);
    data.append("lastName", onb.lastName);
    data.append("phone", onb.phone);
    data.append("idpass", onb.idpass);
    data.append("email", onb.email);
    data.append("password", onb.password);

    data.append("vendor[name]", onb.vendor.name);
    data.append("vendor[email]", onb.vendor.email);
    data.append("vendor[phone]", onb.vendor.phone);
    data.append("vendor[website]", onb.vendor.website);
    data.append("vendor[logo]", onb.vendor.logo);
    data.append("vendor[reg]", onb.vendor.reg);
    data.append("vendor[kra]", onb.vendor.kra);

    data.append("task_id", onb.taskId.value);
    data.append("service_id", onb.serviceId.value);
    data.append("vendor_type_id", onb.vendorTypeId.value);
    data.append("vendor_category_id", onb.vendorCategoryId.value);

    toast.promise(storeVendor(data), {
      loading: "Creating vendor...",
      success: (response) => `Vendor created successfully`,
      error: (err) => `Failed to create vendor: ${err.message}`,
    });
  };

  return (
    <>
      <button
        onClick={() => setCreating(!creating)}
        className="flex items-center justify-center rounded-lg bg-primary px-6 py-2 text-white"
      >
        {creating ? "Close" : "Add new vendor"}
      </button>

      {creating && (
        <div className="fixed bottom-0 left-0 z-50 h-max w-full border bg-white py-14">
          <div className="flex space-x-2 px-4">
            <ol className="w-1/4 space-y-4">
              {steps.map((s, i) => (
                <li key={s}>
                  <button
                    onClick={() => setStep(i)}
                    className={
                      step === i
                        ? "w-full rounded border border-blue-300 bg-default-100 p-4 text-primary dark:border-primary dark:bg-default-800 dark:text-blue-400"
                        : step > i
                          ? "w-full rounded border border-green-300 bg-green-50 p-4 text-green-700 dark:border-green-800 dark:bg-default-800 dark:text-green-400"
                          : "w-full rounded border border-default-300 bg-default-50 p-4 text-default-700 dark:border-default-800 dark:bg-default-800 dark:text-default-400"
                    }
                    role="alert"
                  >
                    <div className="flex items-center justify-between">
                      <span className="sr-only">{steps[i]}</span>

                      <h3 className="font-medium">
                        {i + 1}. {steps[i]}
                      </h3>
                      {step > i && (
                        <svg
                          className="h-4 w-4"
                          aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 16 12"
                        >
                          <path
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M1 5.917 5.724 10.5 15 1.5"
                          />
                        </svg>
                      )}

                      {step === i && (
                        <svg
                          className="h-4 w-4"
                          aria-hidden="true"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 14 10"
                        >
                          <path
                            stroke="currentColor"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M1 5h12m0 0L9 1m4 4L9 9"
                          />
                        </svg>
                      )}
                    </div>
                  </button>
                </li>
              ))}
            </ol>

            <form className="flex-1 px-4" onSubmit={handleSubmit(createVendor)}>
              <div className="flex justify-end">
                <button
                  onClick={() => setCreating(!creating)}
                  className="flex items-center justify-center rounded-lg bg-primary px-6 py-1 text-white"
                >
                  {creating ? "Close" : "Add new vendor"}
                </button>
              </div>

              {step === 0 && (
                <div>
                  <h2 className="mb-5 text-2xl font-bold">{steps[step]}</h2>
                  <div className="mb-6 grid w-full gap-6 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="firstName"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        First name
                      </label>
                      <input
                        type="text"
                        {...register("firstName", {
                          required: true,
                        })}
                        id="firstName"
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="John"
                        required
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="lastName"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Last name
                      </label>
                      <input
                        type="text"
                        {...register("lastName", {
                          required: true,
                        })}
                        id="lastName"
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="Doe"
                        required
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Phone number
                      </label>
                      <div className="form-input relative w-full max-w-full rounded-lg border border-default-200 bg-white px-4 py-3 dark:bg-default-50">
                        <Controller
                          name="phone"
                          control={control}
                          render={({ field: { onChange, value } }) => (
                            <PhoneInput
                              placeholder="Enter phone number"
                              value={value}
                              onChange={onChange}
                              defaultCountry="KE"
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div>
                      <label
                        htmlFor="idpass"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        ID number
                      </label>
                      <input
                        type="url"
                        {...register("idpass", { required: true })}
                        id="idpass"
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="flowbite.com"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-6">
                    <label
                      htmlFor="email"
                      className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                    >
                      Email address
                    </label>
                    <input
                      type="email"
                      {...register("email", { required: true })}
                      id="email"
                      className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>

                  <div className="mb-6">
                    <label
                      htmlFor="password"
                      className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                    >
                      Password
                    </label>
                    <input
                      type="password"
                      {...register("password", { required: true })}
                      id="password"
                      className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                      placeholder="•••••••••"
                      required
                    />
                  </div>

                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => setStep(step + 1)}
                      className="w-full rounded bg-default-700 px-14 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-default-600 dark:hover:bg-default-700 dark:focus:ring-primary sm:w-auto"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}

              {step === 1 && (
                <div>
                  <h2 className="mb-5 text-2xl font-bold">{steps[step]}</h2>

                  <div className="mb-6 flex w-full gap-6">
                    <div className="w-2/3">
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Vendor name
                      </label>
                      <input
                        type="text"
                        id="name"
                        {...register("vendor.name", {
                          required: true,
                        })}
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="Enter name of vendor"
                        required
                      />
                    </div>

                    <div className="w-1/3">
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Registration number
                      </label>
                      <input
                        type="text"
                        id="name"
                        {...register("vendor.reg", {
                          required: true,
                        })}
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="Enter vendor registration number"
                        required
                      />
                    </div>

                    <div className="w-1/3">
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        KRA number
                      </label>
                      <input
                        type="text"
                        id="name"
                        {...register("vendor.kra", {
                          required: true,
                        })}
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="Enter vendor kRA number"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-6 grid w-full gap-6 md:grid-cols-2">
                    <div>
                      <label
                        htmlFor="email"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Email address
                      </label>
                      <input
                        type="type"
                        id="email"
                        {...register("vendor.email", {
                          required: true,
                        })}
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="Flowbite"
                        required
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="phone"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Phone number
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        {...register("vendor.phone", {
                          required: true,
                        })}
                        className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                        placeholder="123-45-678"
                        pattern="[0-9]{3}-[0-9]{2}-[0-9]{3}"
                        required
                      />
                    </div>
                  </div>

                  <div className="mb-6">
                    <label
                      htmlFor="website"
                      className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                    >
                      Website URL
                    </label>
                    <input
                      type="url"
                      id="website"
                      {...register("vendor.website", {
                        required: true,
                      })}
                      className="block w-full rounded border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-blue-500 focus:ring-blue-500 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                      placeholder="flowbite.com"
                      required
                    />
                  </div>

                  <div className="mb-6">
                    <label
                      htmlFor="logo"
                      className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                    >
                      Logo
                    </label>
                    <Controller
                      control={control}
                      name={"vendor.logo"}
                      rules={{ required: "Recipe logo is required" }}
                      render={({ field: { value, onChange } }) => (
                        <label className="flex h-14 w-full items-center justify-center rounded border-2 border-dashed">
                          Click here to upload logo
                          <input
                            onChange={handleLogoUpload}
                            type="file"
                            id="logo"
                            className="hidden"
                          />
                        </label>
                      )}
                    />
                  </div>

                  {/* <div className="flex items-start mb-6">
						<div className="flex items-center h-5">
							<input id="remember" type="checkbox" value="" className="w-4 h-4 border border-default-300 rounded bg-default-50 focus:ring-3 focus:ring-blue-300 dark:bg-default-700 dark:border-default-600 dark:focus:ring-default-600 dark:ring-offset-default-800" required />
						</div>
						<label htmlFor="remember" className="ml-2 text-sm font-medium text-default-900 dark:text-default-300">I agree with the <a href="#" className="text-default-600 hover:underline dark:text-blue-500">terms and conditions</a>.</label>
					</div> */}

                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => setStep(step + 1)}
                      className="w-full rounded bg-default-700 px-14 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-default-600 dark:hover:bg-default-700 dark:focus:ring-primary sm:w-auto"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}

              {step === 2 && (
                <div>
                  <h2 className="mb-5 text-2xl font-bold">{steps[step]}</h2>

                  <div className="mb-6">
                    <label
                      htmlFor="name"
                      className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                    >
                      Select task
                    </label>
                    <Controller
                      name="taskId"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          //@ts-ignore
                          options={tasks.map((task) => ({
                            value: task.id,
                            label: task.name,
                          }))}
                          components={animatedComponents}
                          placeholder="Select task"
                          required
                          isSearchable
                          classNames={{
                            control: () => "!py-[1px] !rounded-lg",
                            menu: () => "py-1",
                          }}
                        />
                      )}
                    />
                  </div>

                  {onboard.taskId && (
                    <div className="mb-6">
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Select services
                      </label>
                      <Controller
                        name="serviceId"
                        control={control}
                        rules={{ required: true }}
                        render={({ field: { onChange, value } }) => (
                          <AsyncSelect
                            isClearable
                            isSearchable
                            // cacheOptions
                            //@ts-ignore
                            loadOptions={fetchServices}
                            components={animatedComponents}
                            placeholder="Select services"
                            required
                            classNames={{
                              control: () => "!py-[1px] !rounded-lg",
                            }}
                            onChange={onChange}
                            value={value}
                          />
                        )}
                      />
                    </div>
                  )}

                  {onboard.serviceId && (
                    <div className="mb-6">
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Select vendor type
                      </label>
                      <Controller
                        name="vendorTypeId"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <AsyncSelect
                            {...field}
                            isClearable
                            isSearchable
                            // cacheOptions
                            //@ts-ignore
                            loadOptions={fetchVendorTypes}
                            components={animatedComponents}
                            placeholder="Select types"
                            required
                            classNames={{
                              control: () => "!py-[1px] !rounded-lg",
                              menu: () => "py-1 z-50 mb-5",
                            }}
                          />
                        )}
                      />
                    </div>
                  )}

                  {onboard.vendorTypeId && (
                    <div className="mb-6">
                      <label
                        htmlFor="name"
                        className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
                      >
                        Select vendor category
                      </label>
                      <Controller
                        name="vendorCategoryId"
                        control={control}
                        rules={{ required: true }}
                        render={({ field }) => (
                          <AsyncSelect
                            {...field}
                            isClearable
                            // cacheOptions
                            isSearchable
                            //@ts-ignore
                            loadOptions={fetchVendorCategories}
                            components={animatedComponents}
                            placeholder="Select category"
                            required
                            classNames={{
                              control: () => "!py-[1px] !rounded-lg",
                              menu: () => "py-1",
                            }}
                          />
                        )}
                      />
                    </div>
                  )}

                  <div className="flex justify-end">
                    <button
                      type="button"
                      onClick={() => setStep(step + 1)}
                      className="w-full rounded bg-default-700 px-14 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-default-600 dark:hover:bg-default-700 dark:focus:ring-primary sm:w-auto"
                    >
                      Next
                    </button>
                  </div>
                </div>
              )}

              {step === 3 && (
                <div>
                  <h2 className="mb-5 text-2xl font-bold">{steps[step]}</h2>

                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="w-full rounded bg-default-700 px-14 py-3 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-blue-300 dark:bg-default-600 dark:hover:bg-default-700 dark:focus:ring-primary sm:w-auto"
                    >
                      Save vendor details
                    </button>
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      )}
    </>
  );
}
