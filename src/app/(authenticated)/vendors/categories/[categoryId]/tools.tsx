"use client";

import Link from "next/link";

export default function VendorCategoryTools({ vendor }: { vendor: Vendor }) {
  return (
    <ul className="osen-tb-actions gx-1">
      <li className="osen-tb-action-hidden">
        <a
          href="#"
          className="btn-icon flex items-center justify-center bg-default-500 px-4 py-3 text-white"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          aria-label="Send Email"
          data-bs-original-title="Send Email"
        >
          <em className="icon ni ni-mail-fill" />
        </a>
      </li>
      <li className="osen-tb-action-hidden">
        <a
          href="#"
          className="btn-icon flex items-center justify-center bg-default-500 px-4 py-3 text-white"
          data-bs-toggle="tooltip"
          data-bs-placement="top"
          aria-label="Suspend"
          data-bs-original-title="Suspend"
        >
          <em className="icon ni ni-user-cross-fill" />
        </a>
      </li>
      <li>
        <div className="dropdown">
          <a
            href="#"
            className="dropdown-toggle btn-icon flex items-center justify-center bg-default-500 px-4 py-3 text-white"
            data-bs-toggle="dropdown"
          >
            <em className="icon ni ni-more-h" />
          </a>
          <div className="dropdown-menu dropdown-menu-end">
            <ul className="link-list-opt no-bdr">
              <li>
                <Link href={`/vendors/${vendor.id}`}>
                  <em className="icon ni ni-eye" />
                  <span>View Details</span>
                </Link>
              </li>
              <li>
                <a href="#">
                  <em className="icon ni ni-activity-round" />
                  <span>Activities</span>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </li>
    </ul>
  );
}
