import { api } from "@/lib/api";
import { Card, CardBody } from "@nextui-org/react";
import { cache } from "react";
import BackBtn from "@/components/back";
import BulkUploadForm from "@/components/products/BulkUploadForm";

interface Vendor {
  id: string;
  name: string;
  email: string;
  phone: string;
  reg: string;
  kra: string;
  active: boolean;
}

const fetchVendor = cache((id: string) => api.get<Vendor>(`vendors/${id}`));

export const generateMetadata = async ({
  params,
}: {
  params: {
    vendorId: string;
  };
}): Promise<Record<string, any>> => {
  const vendor = await fetchVendor(params.vendorId);

  return {
    title: `Bulk Upload Products - ${vendor?.name}`,
    description: "Upload products in bulk for vendor",
    keywords: ["vendor", "products", "bulk upload"],
  };
};

export default async function BulkUploadPage({
  params: { vendorId },
}: {
  params: { vendorId: string };
}) {
  const vendor = await fetchVendor(vendorId);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <BackBtn />
        <h1 className="text-2xl font-bold">Bulk Upload Products</h1>
      </div>

      <Card>
        <CardBody>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-lg font-medium">Upload Products CSV</h2>
                <p className="text-sm text-default-500">
                  Upload a CSV file containing product details for {vendor?.name}
                </p>
              </div>
              <a
                href="/templates/products-template.csv"
                className="text-sm text-primary hover:underline"
                download
              >
                Download Template
              </a>
            </div>

            <BulkUploadForm vendorId={vendorId} />
          </div>
        </CardBody>
      </Card>
    </div>
  );
} 