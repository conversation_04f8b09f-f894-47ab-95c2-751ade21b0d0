"use client";

import { useState, useEffect } from "react";
import { Button } from "@nextui-org/react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import toast from "react-hot-toast";
import AsyncSelect from "react-select/async";
import { Controller, useForm, useWatch } from "react-hook-form";
import { Speciality } from "@/types";

interface VendorSpecializationSectionProps {
  vendorId: string;
  defaultSpecializations?: Speciality[];
  updateSpecialization: (data: { specialization: string[] }) => Promise<void>;
}

interface Option {
  label: string;
  value: string;
}

export default function VendorSpecializationSection({
  vendorId,
  defaultSpecializations = [],
  updateSpecialization,
}: VendorSpecializationSectionProps) {
  const [specializations, setSpecializations] = useState<Speciality[]>(defaultSpecializations);
  const [isLoading, setIsLoading] = useState(false);
  // Store all options that have been loaded or selected
  const [allOptions, setAllOptions] = useState<Option[]>(
    defaultSpecializations.map(s => ({ label: s.name, value: s.id }))
  );

  const { control, handleSubmit, setValue } = useForm<{
    specialization: string[];
  }>({
    defaultValues: {
      specialization: defaultSpecializations.map(s => s.id),
    },
  });

  // Watch selected specialization IDs
  const selectedIds = useWatch({ control, name: "specialization" }) || [];

  // Map selected IDs to option objects for AsyncSelect value
  const selectedOptions = allOptions.filter(opt => selectedIds.includes(opt.value));

  // Fetch options for AsyncSelect
  const fetchSpecialities = async (inputValue: string) => {
    if (inputValue.length > 2) {
      const response = await fetch(`/api/specialities?s=${inputValue}`);
      const data = await response.json();
      const options = data.map((s: Speciality) => ({
        value: s.id,
        label: s.name,
      }));
      // Merge with allOptions to keep selected options available
      setAllOptions(prev => {
        const merged = [...prev];
        options.forEach((opt: Option) => {
          if (!merged.find(o => o.value === opt.value)) merged.push(opt);
        });
        return merged;
      });
      return options;
    }
    return [];
  };

  const onSubmit = async (data: { specialization: string[] }) => {
    try {
      setIsLoading(true);
      await updateSpecialization(data);
      toast.success("Specializations updated successfully");
    } catch (error) {
      console.error("Error updating specializations:", error);
      toast.error("Failed to update specializations");
    } finally {
      setIsLoading(false);
    }
  };

  const removeSpecialization = async (specialityId: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/vendors/${vendorId}/specialities/${specialityId}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to remove specialization');
      }
      
      setSpecializations(prev => prev.filter(s => s.id !== specialityId));
      toast.success("Specialization removed successfully");
      // Also remove from form state and options
      setValue("specialization", selectedIds.filter(id => id !== specialityId));
      setAllOptions(prev => prev.filter(opt => opt.value !== specialityId));
    } catch (error) {
      console.error("Error removing specialization:", error);
      toast.error("Failed to remove specialization");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="rounded-lg border border-default-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-md font-medium">Specializations</h4>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Controller
              name="specialization"
              control={control}
              render={({ field }) => (
                <AsyncSelect
                  {...field}
                  isMulti
                  cacheOptions
                  defaultOptions={allOptions}
                  loadOptions={fetchSpecialities}
                  value={selectedOptions}
                  className="react-select-container"
                  classNamePrefix="react-select"
                  placeholder="Search and select specializations..."
                  onChange={(selected) => {
                    const values = selected?.map((s: any) => s.value) || [];
                    field.onChange(values);
                    setValue("specialization", values);
                    // Add any new selected options to allOptions
                    if (selected) {
                      setAllOptions(prev => {
                        const merged = [...prev];
                        (selected as {label: string; value: string}[]).forEach((opt) => {
                          if (!merged.find(o => o.value === opt.value)) merged.push(opt);
                        });
                        return merged;
                      });
                    }
                  }}
                />
              )}
            />
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              color="primary"
              isLoading={isLoading}
            >
              Save Specializations
            </Button>
          </div>
        </form>

        <div className="mt-6 space-y-4">
          <h5 className="text-sm font-medium">Current Specializations</h5>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {specializations.map((specialization) => (
              <motion.div
                key={specialization.id}
                className="flex items-center justify-between rounded-lg border border-default-200 p-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
              >
                <div className="flex items-center space-x-3">
                  {specialization.image && (
                    <div className="h-10 w-10 overflow-hidden rounded-md">
                      <Image
                        src={specialization.image.url}
                        alt={specialization.name}
                        width={40}
                        height={40}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  )}
                  <div>
                    <p className="font-medium">{specialization.name}</p>
                    {specialization.details && (
                      <p className="text-xs text-default-500 line-clamp-1">
                        {specialization.details}
                      </p>
                    )}
                  </div>
                </div>
                <Button
                  size="sm"
                  variant="light"
                  color="danger"
                  onClick={() => removeSpecialization(specialization.id)}
                  isLoading={isLoading}
                >
                  Remove
                </Button>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
} 