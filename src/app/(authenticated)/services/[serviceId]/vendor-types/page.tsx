import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import ServiceVendorTypesTable from "@/components/tables/services-vendor-types-table";

export const metadata = {
  title: "Vendor Types",
};

export default async function page({
  params: { serviceId },
  searchParams,
}: {
  params: { serviceId: string };
  searchParams: Record<string, string>;
}) {
  const types = await api.get<PaginatedData<VendorType>>(
    `services/${serviceId}/vendor-types`,
    searchParams,
  );

  const storeVendorType = async (data: FormData) => {
    "use server";

    await api.post(`services/${serviceId}/vendor-types`, data);

    revalidatePath(`/services/${serviceId}/vendor-types`);
  };

  const updateVendorType = async (data: FormData) => {
    "use server";

    const id = data.get("id") as string;
    await api.put(`vendor-types/${id}`, data);

    revalidatePath(`/services/${serviceId}/vendor-types`);
  };

  const deleteVendorType = async (data: FormData) => {
    "use server";

    await api.destroy(data.get("id") as string, "vendor-types");

    revalidatePath(`/services/${serviceId}/vendor-types`);
  };

  return (
    <div className="flex bg-white p-10">
      {types && (
        <ServiceVendorTypesTable
          data={types.data}
          meta={types.meta}
          serviceId={serviceId}
          action={storeVendorType}
          updateAction={updateVendorType}
          deleteVendorType={deleteVendorType}
        />
      )}
    </div>
  );
}
