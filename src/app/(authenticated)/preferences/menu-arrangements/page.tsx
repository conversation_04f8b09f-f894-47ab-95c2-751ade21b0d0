import { auth } from "@/auth";
import { api } from "@/lib/api";
import { Metadata } from "next";
import { revalidatePath } from "next/cache";
import MenuArrangementsTable from "./menu-arrangements-table";

export const metadata: Metadata = {
  title: "Menu Arrangements",
  description: "Organize and arrange menu categories for your branch",
};

export default async function MenuArrangementsPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  let categories: { data: ProductCategory[]; meta: PaginationMeta } | undefined;

  if (!session?.branch?.id) {
    return (
      <div className="py-8 text-center text-slate-500">
        <p>No branch selected. Please select a branch to manage menu arrangements.</p>
      </div>
    );
  }

  // Ensure consistent page size handling
  const perPage = searchParams.per || "20";
  const page = searchParams.page || "1";
  const order = searchParams.order || "name";
  const sort = searchParams.sort || "asc";

  try {
    // Fetch categories for the current branch using the public API
    categories = await api.get<{ data: ProductCategory[]; meta: PaginationMeta }>(
      `public/branches/${session.branch.id}/categories`,
      {
        service: "01jrsjk1hj25wabb8ap26g57qa", // food-drinks service ID
        per: perPage,
        page: page,
        ...searchParams,
      }
    );
  } catch (error) {
    console.error("Failed to fetch branch categories:", error);
    categories = undefined;
  }

  // Define the updateCategoryOrder function
  const updateCategoryOrder = async (data: FormData) => {
    "use server";

    try {
      const categoryId = String(data.get("categoryId"));
      const newOrder = Number(data.get("order"));
      const action = String(data.get("action"));

      if (session?.branch?.id) {
        await api.put(`branches/${session.branch.id}/categories/${categoryId}`, {
          order: newOrder,
          action: action,
        });
        revalidatePath("/preferences/menu-arrangements");
      }
    } catch (error) {
      console.error("Failed to update category order:", error);
      throw error;
    }
  };

  // Define the toggleCategoryVisibility function
  const toggleCategoryVisibility = async (data: FormData) => {
    "use server";

    try {
      const categoryId = String(data.get("categoryId"));
      const isVisible = String(data.get("isVisible")) === "true";

      if (session?.branch?.id) {
        await api.put(`branches/${session.branch.id}/categories/${categoryId}`, {
          isVisible: !isVisible,
        });
        revalidatePath("/preferences/menu-arrangements");
      }
    } catch (error) {
      console.error("Failed to toggle category visibility:", error);
      throw error;
    }
  };

  return (
    <div>
      <div className="border-b pb-8 pt-4">
        <h1 className="text-2xl font-semibold text-slate-700">Menu Arrangements</h1>
        <p className="font- text-slate-600 mt-2">
          Organize and arrange the order of product categories in your branch menu. 
          You can reorder categories, toggle their visibility, and manage how they appear to customers.
        </p>
      </div>

      <div className="mt-6">
        {categories && categories.data ? (
          <MenuArrangementsTable
            data={categories.data}
            meta={categories.meta}
            updateCategoryOrder={updateCategoryOrder}
            toggleCategoryVisibility={toggleCategoryVisibility}
            branchId={session.branch.id}
          />
        ) : (
          <div className="flex items-center justify-center p-8">
            <div className="text-center">
              <p className="text-lg text-gray-600">No categories found</p>
              <p className="text-sm text-gray-400 mt-2">
                {categories === undefined
                  ? "Failed to load categories data"
                  : "No categories available for this branch"}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
