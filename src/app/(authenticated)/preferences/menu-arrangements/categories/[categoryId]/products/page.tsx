import { auth } from "@/auth";
import { api } from "@/lib/api";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { revalidatePath } from "next/cache";
import Link from "next/link";

import { Icon } from "@/components/icon";
import CategoryProductsTable from "./products-table";

// Type definitions
interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  details: string;
  image: any;
  productTypeId: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginationMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
}

// Menu API Product interface that matches the actual API response
interface MenuProduct {
  id: string;
  name: string;
  details: string;
  price: number;
  discounted: number | null;
  image: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  createdAt: string;
  category: {
    id: string;
    name: string;
    slug: string;
    details: string;
    image: any;
    productTypeId: string;
    createdAt: string;
    updatedAt: string;
  };
  vendor: any;
  service: any;
  modifiers: any;
  accompaniments: any[];
  upsells: any[];
  packagingOptions: any[];
}

// Menu API Product interface that matches the actual API response
interface MenuProduct {
  id: string;
  name: string;
  details: string;
  price: number;
  discounted: number | null;
  image: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  createdAt: string;
  category: {
    id: string;
    name: string;
    slug: string;
    details: string;
    image: any;
    productTypeId: string;
    createdAt: string;
    updatedAt: string;
  };
  vendor: any;
  service: any;
  modifiers: any;
  accompaniments: any[];
  upsells: any[];
  packagingOptions: any[];
  orderNumber?: number;
}

interface CategoryProductsPageProps {
  params: { categoryId: string };
  searchParams: Record<string, string>;
}

export async function generateMetadata({
  params,
}: {
  params: { categoryId: string };
}): Promise<Metadata> {
  try {
    const category = await api.get<ProductCategory>(`product-categories/${params.categoryId}`);
    return {
      title: `${category?.name} - Products`,
      description: `Manage products in the ${category?.name} category`,
    };
  } catch {
    return {
      title: "Category Products",
      description: "Manage products in this category",
    };
  }
}

export default async function CategoryProductsPage({
  params,
  searchParams,
}: CategoryProductsPageProps) {
  const session = await auth();

  if (!session?.branch?.id) {
    return (
      <div className="py-8 text-center text-slate-500">
        <p>No branch selected. Please select a branch to manage products.</p>
      </div>
    );
  }

  let category: ProductCategory | undefined;
  let products: { data: MenuProduct[]; meta: PaginationMeta } | undefined;

  // Define server actions for product management
  const updateProductOrder = async (data: FormData) => {
    "use server";

    try {
      const productId = String(data.get("productId"));
      const action = String(data.get("action"));

      if (session?.branch?.id) {
        // TODO: Implement actual API call for product reordering
        await api.put(`branches/${session.branch.id}/products/${productId}/order`, {
          action: action,
        });
        revalidatePath(`/preferences/menu-arrangements/categories/${params.categoryId}/products`);
      }
    } catch (error) {
      console.error("Failed to update product order:", error);
      throw error;
    }
  };

  const toggleProductVisibility = async (data: FormData) => {
    "use server";

    try {
      const productId = String(data.get("productId"));
      const isVisible = String(data.get("isVisible")) === "true";

      if (session?.branch?.id) {
        // TODO: Implement actual API call for product visibility toggle
        await api.put(`branches/${session.branch.id}/products/${productId}/visibility`, {
          isVisible: !isVisible,
        });
        revalidatePath(`/preferences/menu-arrangements/categories/${params.categoryId}/products`);
      }
    } catch (error) {
      console.error("Failed to toggle product visibility:", error);
      throw error;
    }
  };

  try {
    // First, get the category details
    category = await api.get<ProductCategory>(`product-categories/${params.categoryId}`);

    if (!category) {
      notFound();
    }

    // Fetch products for this branch and category using the menu endpoint
    const page = searchParams.page || "1";
    const perPage = searchParams.per || "20";
    const search = searchParams.s || "";

    // Use the public menu endpoint to fetch products
    const menuData = await api.get<{ data: MenuProduct[]; meta: any }>(
      `public/branches/${session.branch.id}/menu`,
      {
        service: "food-drinks",
        ...(params.categoryId && { category: params.categoryId }),
        include_modifiers: "true",
        per: perPage,
        page: page,
        ...(search && { s: search }),
      }
    );

    if (!menuData || !menuData.data) {
      products = { data: [], meta: { total: 0, per_page: 20, current_page: 1, last_page: 1 } };
    } else {
      // Transform the API meta to match our expected format and add order numbers
      products = {
        data: menuData.data.map((product, index) => ({
          ...product,
          orderNumber: ((parseInt(page) - 1) * parseInt(perPage)) + index + 1
        })),
        meta: {
          total: menuData.meta.total,
          per_page: menuData.meta.perPage || menuData.meta.per_page,
          current_page: menuData.meta.currentPage || menuData.meta.current_page,
          last_page: menuData.meta.lastPage || menuData.meta.last_page,
        }
      };
    }
  } catch (error) {
    console.error("Failed to fetch category or products:", error);
    notFound();
  }



  return (
    <div>
      <div className="border-b pb-8 pt-4">
        <div className="flex items-center gap-4 mb-4">
          <Link
            href="/preferences/menu-arrangements"
            className="inline-flex items-center justify-center w-8 h-8 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100"
          >
            <Icon name="icon-[heroicons--arrow-left-20-solid]" classNames="h-4 w-4" />
          </Link>
          <div>
            <h1 className="text-2xl font-semibold text-slate-700">
              {category?.name || "Category"} - Products
            </h1>
            <p className="text-slate-600 mt-2">
              {category?.details || "Manage products in this category"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-sm text-slate-600">
            <span className="font-medium">Category:</span> {category?.name || "Loading..."}
          </div>
          <div className="text-sm text-slate-600">
            <span className="font-medium">Total Products:</span> {products?.meta.total || 0}
          </div>
        </div>
      </div>

      <div className="mt-6">
        {products && products.data.length > 0 ? (
          <CategoryProductsTable
            data={products.data.map((product, index) => ({
              ...product,
              orderNumber: product.orderNumber || index + 1
            }))}
            meta={products.meta}
            categoryId={params.categoryId}
            branchId={session?.branch?.id || ""}
            updateProductOrder={updateProductOrder}
            toggleProductVisibility={toggleProductVisibility}
          />
        ) : (
          <div className="py-8 text-center text-slate-500">
            <Icon name="icon-[heroicons--cube-20-solid]" classNames="h-12 w-12 mx-auto mb-4 text-slate-300" />
            <p className="text-lg font-medium mb-2">No products found</p>
            <p>This category doesn't have any products yet.</p>
          </div>
        )}
      </div>
    </div>
  );
}
