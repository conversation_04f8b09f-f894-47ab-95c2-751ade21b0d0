"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { <PERSON>ton, Image, Switch } from "@nextui-org/react";
import Link from "next/link";
import { useState } from "react";
import toast from "react-hot-toast";

interface PaginationMeta {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
}

// Menu API Product interface that matches the actual API response
interface MenuProduct {
  id: string;
  name: string;
  details: string;
  price: number;
  discounted: number | null;
  image: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  createdAt: string;
  category: {
    id: string;
    name: string;
    slug: string;
    details: string;
    image: any;
    productTypeId: string;
    createdAt: string;
    updatedAt: string;
  };
  vendor: any;
  service: any;
  modifiers: any;
  accompaniments: any[];
  upsells: any[];
  packagingOptions: any[];
  orderNumber?: number; // Added for display purposes
}

interface CategoryProductsTableProps {
  data: MenuProduct[];
  meta: PaginationMeta;
  categoryId: string;
  branchId: string;
  updateProductOrder: (formData: FormData) => Promise<void>;
  toggleProductVisibility: (formData: FormData) => Promise<void>;
}

export default function CategoryProductsTable({
  data,
  meta,
  categoryId,
  branchId,
  updateProductOrder,
  toggleProductVisibility,
}: CategoryProductsTableProps) {
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  const handleStatusToggle = async (productId: string, currentVisible: boolean) => {
    setIsUpdating(productId);
    const formData = new FormData();
    formData.append("productId", productId);
    formData.append("isVisible", String(currentVisible));

    try {
      await toast.promise(toggleProductVisibility(formData), {
        loading: `${currentVisible ? "Hiding" : "Showing"} product...`,
        success: `Product ${currentVisible ? "hidden" : "shown"} successfully!`,
        error: "Failed to update product visibility",
      });
    } finally {
      setIsUpdating(null);
    }
  };



  const handleReorder = async (productId: string, direction: "up" | "down") => {
    setIsUpdating(productId);
    const formData = new FormData();
    formData.append("productId", productId);
    formData.append("action", direction);

    try {
      await toast.promise(updateProductOrder(formData), {
        loading: `Moving product ${direction}...`,
        success: `Product moved ${direction} successfully!`,
        error: "Failed to reorder product",
      });
    } finally {
      setIsUpdating(null);
    }
  };

  return (
    <CustomTable
      title="Category Products"
      columns={[
        {
          name: "#",
          uid: "number",
          renderCell: (product: MenuProduct) => (
            <span className="text-sm font-medium text-slate-600">
              {product.orderNumber || 0}
            </span>
          ),
        },
        {
          name: "Order",
          uid: "order",
          renderCell: (product: MenuProduct) => {
            const index = data.findIndex(p => p.id === product.id);
            return (
            <div className="flex items-center gap-1">
              <Button
                size="sm"
                variant="light"
                isIconOnly
                isDisabled={index === 0 || isUpdating === product.id}
                onPress={() => handleReorder(product.id, "up")}
              >
                <Icon name="icon-[heroicons--chevron-up-20-solid]" classNames="h-4 w-4 text-default-600" />
              </Button>
              <Button
                size="sm"
                variant="light"
                isIconOnly
                isDisabled={index === data.length - 1 || isUpdating === product.id}
                onPress={() => handleReorder(product.id, "down")}
              >
                <Icon name="icon-[heroicons--chevron-down-20-solid]" classNames="h-4 w-4 text-default-600" />
              </Button>
            </div>
            );
          },
        },
        {
          name: "Product",
          uid: "product",
          renderCell: (product: MenuProduct) => (
            <div className="flex items-center space-x-3">
              <Image
                src={imagePath(product.image?.url)}
                alt={product.name}
                width={50}
                height={50}
                className="rounded-lg object-cover"
                fallbackSrc="/assets/images/placeholder.png"
              />
              <div>
                <Link
                  href={`/products/${product.id}`}
                  className="font-semibold text-default-700 hover:text-primary"
                >
                  {product.name}
                </Link>
                <p className="text-sm text-default-500">ID: {product.id.slice(-8)}</p>
                {product.details && (
                  <p className="text-xs text-default-400 truncate max-w-xs">
                    {product.details}
                  </p>
                )}
              </div>
            </div>
          ),
        },
        {
          name: "Price",
          uid: "price",
          renderCell: (product: MenuProduct) => (
            <div>
              <div className="font-semibold">
                KSh {product.price.toLocaleString()}
              </div>
              {product.discounted && product.discounted > 0 && (
                <div className="text-sm text-red-500">
                  Discounted: KSh {product.discounted.toLocaleString()}
                </div>
              )}
            </div>
          ),
        },
        {
          name: "Category",
          uid: "category",
          renderCell: (product: MenuProduct) => (
            <div>
              <span className="text-sm font-medium">{product.category.name}</span>
            </div>
          ),
        },
        {
          name: "Visibility",
          uid: "visibility",
          renderCell: (product: MenuProduct) => (
            <div className="flex items-center gap-2">
              <Switch
                size="sm"
                isSelected={true}
                isDisabled={isUpdating === product.id}
                onValueChange={() => handleStatusToggle(product.id, true)}
              />
              <span className="text-xs text-default-500">
                Visible
              </span>
            </div>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (product: MenuProduct) => (
            <div className="flex items-center gap-2">
              <Button
                as={Link}
                href={`/products/${product.id}`}
                size="sm"
                variant="light"
                isIconOnly
              >
                <Icon name="icon-[heroicons--eye-20-solid]" classNames="h-4 w-4 text-primary" />
              </Button>
              <Button
                as={Link}
                href={`/products/${product.id}/edit`}
                size="sm"
                variant="light"
                isIconOnly
              >
                <Icon name="icon-[heroicons--pencil-20-solid]" classNames="h-4 w-4 text-default-600" />
              </Button>
              <Button
                size="sm"
                variant="light"
                isIconOnly
                color="danger"
                onPress={() => {
                  // TODO: Implement delete functionality
                  console.log("Delete product:", product.id);
                }}
              >
                <Icon name="icon-[heroicons--trash-20-solid]" classNames="h-4 w-4" />
              </Button>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={[
        {
          column: "status",
          displayName: "Status",
          values: [
            { name: "Published", value: "Published" },
            { name: "Draft", value: "Draft" },
            { name: "Archived", value: "Archived" },
          ],
        },
        {
          column: "availability",
          displayName: "Availability",
          values: [
            { name: "In Stock", value: "In Stock" },
            { name: "Out of Stock", value: "Out of Stock" },
            { name: "Pre Order", value: "Pre Order" },
          ],
        },
      ]}
    />
  );
}
