"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { imagePath } from "@/lib/api";
import { <PERSON>ton, Chip, Image, Switch } from "@nextui-org/react";
import Link from "next/link";
import { useState } from "react";
import toast from "react-hot-toast";

interface MenuArrangementsTableProps {
  data: ProductCategory[];
  meta: PaginationMeta;
  updateCategoryOrder: (formData: FormData) => Promise<void>;
  toggleCategoryVisibility: (formData: FormData) => Promise<void>;
  branchId: string;
}

export default function MenuArrangementsTable({
  data,
  meta,
  updateCategoryOrder,
  toggleCategoryVisibility,
  branchId,
}: MenuArrangementsTableProps) {
  const [isUpdating, setIsUpdating] = useState<string | null>(null);

  const handleOrderChange = async (categoryId: string, action: "up" | "down") => {
    setIsUpdating(categoryId);
    const formData = new FormData();
    formData.append("categoryId", categoryId);
    formData.append("action", action);

    try {
      await toast.promise(updateCategoryOrder(formData), {
        loading: `Moving category ${action}...`,
        success: "Category order updated!",
        error: "Failed to update category order",
      });
    } finally {
      setIsUpdating(null);
    }
  };

  const handleVisibilityToggle = async (categoryId: string, currentVisibility: boolean) => {
    setIsUpdating(categoryId);
    const formData = new FormData();
    formData.append("categoryId", categoryId);
    formData.append("isVisible", String(currentVisibility));

    try {
      await toast.promise(toggleCategoryVisibility(formData), {
        loading: `${currentVisibility ? "Hiding" : "Showing"} category...`,
        success: `Category ${currentVisibility ? "hidden" : "shown"} successfully!`,
        error: "Failed to update category visibility",
      });
    } finally {
      setIsUpdating(null);
    }
  };

  return (
    <CustomTable
      title="Menu Categories"
      columns={[
        {
          name: "Order",
          uid: "order",
          renderCell: (category: ProductCategory) => (
            <div className="flex items-center gap-2">
              <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                {category.order || 0}
              </span>
              <div className="flex flex-col gap-1">
                <Button
                  size="sm"
                  variant="light"
                  isIconOnly
                  isDisabled={isUpdating === category.id}
                  onPress={() => handleOrderChange(category.id, "up")}
                  className="h-6 w-6 min-w-6"
                >
                  <Icon name="icon-[heroicons--chevron-up-20-solid]" classNames="h-3 w-3" />
                </Button>
                <Button
                  size="sm"
                  variant="light"
                  isIconOnly
                  isDisabled={isUpdating === category.id}
                  onPress={() => handleOrderChange(category.id, "down")}
                  className="h-6 w-6 min-w-6"
                >
                  <Icon name="icon-[heroicons--chevron-down-20-solid]" classNames="h-3 w-3" />
                </Button>
              </div>
            </div>
          ),
        },
        {
          name: "Category",
          uid: "category",
          renderCell: (category: ProductCategory) => (
            <div className="flex items-center space-x-3">
              <Image
                src={imagePath(category.image?.url)}
                alt={category.name}
                width={40}
                height={40}
                className="rounded-lg object-cover"
                fallbackSrc="/assets/images/placeholder.png"
              />
              <div>
                <Link
                  href={`/preferences/menu-arrangements/categories/${category.id}/products`}
                  className="font-semibold text-default-700 hover:text-primary"
                >
                  {category.name}
                </Link>
                <p className="text-sm text-default-500">{category.slug}</p>
              </div>
            </div>
          ),
        },
        {
          name: "Description",
          uid: "details",
          renderCell: (category: ProductCategory) => (
            <div className="max-w-xs">
              <p className="text-sm text-default-600 truncate">
                {category.details || "No description"}
              </p>
            </div>
          ),
        },
        {
          name: "Products Count",
          uid: "productsCount",
          renderCell: (category: ProductCategory) => (
            <Chip
              size="sm"
              variant="flat"
              color="primary"
              className="text-xs"
            >
              {category.productsCount || 0} products
            </Chip>
          ),
        },
        {
          name: "Visibility",
          uid: "isVisible",
          renderCell: (category: ProductCategory) => (
            <div className="flex items-center gap-2">
              <Switch
                size="sm"
                isSelected={category.isVisible !== false}
                isDisabled={isUpdating === category.id}
                onValueChange={() => handleVisibilityToggle(category.id, category.isVisible !== false)}
                classNames={{
                  base: "inline-flex flex-row-reverse w-full max-w-md bg-content1 hover:bg-content2 items-center justify-between cursor-pointer rounded-lg gap-2 p-2 border-2 border-transparent data-[selected=true]:border-primary",
                  wrapper: "p-0 h-4 overflow-visible",
                  thumb: "w-6 h-6 border-2 shadow-lg group-data-[hover=true]:border-primary group-data-[selected=true]:ml-6 group-data-[pressed=true]:w-7 group-data-[selected]:group-data-[pressed]:ml-4",
                }}
              />
              <span className="text-xs text-default-500">
                {category.isVisible !== false ? "Visible" : "Hidden"}
              </span>
            </div>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (category: ProductCategory) => (
            <div className="flex items-center gap-2">
              <Button
                as={Link}
                href={`/preferences/menu-arrangements/categories/${category.id}/products`}
                size="sm"
                variant="light"
                isIconOnly
              >
                <Icon name="icon-[heroicons--eye-20-solid]" classNames="h-4 w-4 text-primary" />
              </Button>
              <Button
                as={Link}
                href={`/preferences/menu-arrangements/categories/${category.id}/products`}
                size="sm"
                variant="light"
                isIconOnly
              >
                <Icon name="icon-[heroicons--pencil-20-solid]" classNames="h-4 w-4 text-default-600" />
              </Button>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
      filter={[
        {
          column: "isVisible",
          displayName: "Visibility",
          values: [
            { name: "Visible", value: "true" },
            { name: "Hidden", value: "false" },
          ],
        },
      ]}
    />
  );
}
