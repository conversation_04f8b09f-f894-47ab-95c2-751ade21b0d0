"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { api } from "@/lib/api";
import ServiceAreaForm from "@/components/forms/service-area-form";
import { PaginatedData, Branch } from "@/types";
import { Button } from "@nextui-org/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import toast from "react-hot-toast";

export default function CreateServiceAreaPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [loading, setLoading] = useState(true);
  const [vendorId, setVendorId] = useState<string | null>(null);

  useEffect(() => {
    if (status === "loading") return;

    // Check if user is authenticated and has vendor access
    if (!session) {
      router.push('/login');
      return;
    }

    // Get vendor ID from session
    const currentVendorId = session?.vendor?.id || session?.branch?.vendorId;
    setVendorId(currentVendorId);

    if (currentVendorId) {
      // Fetch vendor branches
      api.get<PaginatedData<Branch>>(`vendors/${currentVendorId}/branches`)
        .then(response => {
          setBranches(response?.data || []);
        })
        .catch(error => {
          console.error("Failed to fetch branches:", error);
          toast.error("Failed to load branches");
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [session, status, router]);

  const createServiceArea = async (data: FormData) => {
    if (!vendorId) return;

    try {
      await api.post(`vendors/${vendorId}/service-areas`, data);
      toast.success("Service area created successfully!");
      router.push("/service-areas");
    } catch (error) {
      console.error("Failed to create service area:", error);
      toast.error("Failed to create service area");
      throw error;
    }
  };

  // Show loading state
  if (status === "loading" || loading) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (redirect will happen)
  if (!session) {
    return null;
  }

  if (!vendorId) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              No Vendor Access
            </h2>
            <p className="text-gray-600">
              You need to be associated with a vendor to create service areas.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <Button
          as={Link}
          href="/service-areas"
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
          className="mb-4"
        >
          Back to Service Areas
        </Button>
        
        <h1 className="text-2xl font-bold text-gray-900">Create Service Area</h1>
        <p className="text-gray-600 mt-1">
          Define a new delivery coverage area for your business
        </p>
      </div>

      <div className="max-w-4xl">
        <ServiceAreaForm
          action={createServiceArea}
          branches={branches}
          vendorId={vendorId}
        />
      </div>
    </div>
  );
}
