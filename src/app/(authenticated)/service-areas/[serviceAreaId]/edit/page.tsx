"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { api } from "@/lib/api";
import ServiceAreaForm from "@/components/forms/service-area-form";
import { PaginatedData, Branch, ServiceArea } from "@/types";
import { Button } from "@nextui-org/react";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import toast from "react-hot-toast";

export default function EditServiceAreaPage({
  params: { serviceAreaId },
}: {
  params: { serviceAreaId: string };
}) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [serviceArea, setServiceArea] = useState<ServiceArea | null>(null);
  const [loading, setLoading] = useState(true);
  const [vendorId, setVendorId] = useState<string | null>(null);

  useEffect(() => {
    if (status === "loading") return;

    // Check if user is authenticated and has vendor access
    if (!session) {
      router.push('/login');
      return;
    }

    // Get vendor ID from session
    const currentVendorId = session?.vendor?.id || session?.branch?.vendorId;
    setVendorId(currentVendorId);

    if (currentVendorId) {
      // Fetch service area and branches
      Promise.all([
        api.get<ServiceArea>(`vendors/${currentVendorId}/service-areas/${serviceAreaId}`),
        api.get<PaginatedData<Branch>>(`vendors/${currentVendorId}/branches`)
      ])
        .then(([serviceAreaResponse, branchesResponse]) => {
          if (!serviceAreaResponse) {
            router.push('/not-found');
            return;
          }
          setServiceArea(serviceAreaResponse);
          setBranches(branchesResponse?.data || []);
        })
        .catch(error => {
          console.error("Failed to fetch data:", error);
          toast.error("Failed to load service area data");
          router.push('/service-areas');
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [session, status, router, serviceAreaId]);

  const updateServiceArea = async (data: FormData) => {
    if (!vendorId) return;

    try {
      await api.put(`vendors/${vendorId}/service-areas/${serviceAreaId}`, data);
      toast.success("Service area updated successfully!");
      router.push(`/service-areas/${serviceAreaId}`);
    } catch (error) {
      console.error("Failed to update service area:", error);
      toast.error("Failed to update service area");
      throw error;
    }
  };

  // Show loading state
  if (status === "loading" || loading) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (redirect will happen)
  if (!session) {
    return null;
  }

  if (!vendorId) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              No Vendor Access
            </h2>
            <p className="text-gray-600">
              You need to be associated with a vendor to edit service areas.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!serviceArea) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Service Area Not Found
            </h2>
            <p className="text-gray-600">
              The requested service area could not be found.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <Button
          as={Link}
          href={`/service-areas/${serviceAreaId}`}
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
          className="mb-4"
        >
          Back to Service Area
        </Button>
        
        <h1 className="text-2xl font-bold text-gray-900">Edit Service Area</h1>
        <p className="text-gray-600 mt-1">
          Update the details and coverage settings for &ldquo;{serviceArea.name}&rdquo;
        </p>
      </div>

      <div className="max-w-4xl">
        <ServiceAreaForm
          action={updateServiceArea}
          branches={branches}
          vendorId={vendorId}
          serviceArea={serviceArea}
          isEdit={true}
        />
      </div>
    </div>
  );
}
