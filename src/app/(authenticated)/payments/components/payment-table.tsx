"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { deletePayment } from "../actions";
import { formatDate } from "@/lib/date";
import Link from "next/link";
import { toast } from "react-hot-toast";
import { Payment, PaginatedData } from "../types";

interface PaymentTableProps {
  data: Payment[];
  meta: PaginatedData<Payment>["meta"];
  session: any; // TODO: Add proper session type
}

const transformPaginationMeta = (meta: any) => {
  if (!meta) return meta;
  return {
    total: meta.total,
    perPage: meta.perPage,
    currentPage: meta.currentPage,
    lastPage: meta.lastPage,
    firstPage: meta.firstPage || 1,
    firstPageUrl: meta.firstPageUrl || null,
    lastPageUrl: meta.lastPageUrl || null,
    nextPageUrl: meta.nextPageUrl || null,
    previousPageUrl: meta.previousPageUrl || null,
  };
};

export default function PaymentTable({ data, meta, session }: PaymentTableProps) {
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);
    
    toast.promise(deletePayment(formData), {
      loading: "Deleting payment...",
      success: "Payment deleted!",
      error: "Failed to delete payment",
    });
  };

  const columns = [
    {
      uid: "name",
      name: "Customer",
      renderCell: (payment: Payment) => (
        <Link href={`/users/${payment.userId}`}>
          {payment.customer?.name}
        </Link>
      ),
    },
    {
      uid: "amount",
      name: "Amount",
      renderCell: (payment: Payment) => (
        <span className="amount">KES {payment.amount}</span>
      ),
    },
    { uid: "ref", name: "Reference" },
    { uid: "receipt", name: "Transaction ID" },
    { uid: "status", name: "Status" },
    {
      uid: "createdAt",
      name: "Date",
      renderCell: (payment: Payment) => (
        <p>{formatDate(payment.createdAt, "EEEE, MMM dd, yyyy hh:mma")}</p>
      ),
    },
    {
      uid: "actions",
      name: "Actions",
      renderCell: (payment: Payment) => (
        <div className="flex items-center gap-3">
          <Link href={`/payments/${payment.id}`} className="text-primary">
            <Icon name="icon-[heroicons--eye-20-solid]" />
          </Link>
          <Link href={`/payments/${payment.id}/print`} target="_blank" className="text-primary">
            <Icon name="icon-[heroicons--printer-20-solid]" />
          </Link>
          <button 
            disabled 
            className="cursor-not-allowed opacity-50"
            title="Delete functionality is currently disabled"
          >
            <Icon name="icon-[heroicons--trash-20-solid]" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      title="Payments"
      columns={columns}
      data={data}
      meta={transformPaginationMeta(meta)}
    />
  );
} 