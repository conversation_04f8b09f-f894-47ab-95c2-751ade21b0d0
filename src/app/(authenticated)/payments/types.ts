export interface Payment {
  id: string;
  amount: number;
  userId: string;
  customer?: {
    name: string;
  };
  ref: string;
  receipt: string;
  status: string;
  createdAt: string;
  barcode?: string;
}

export interface PaymentFormData {
  amount: number;
  userId: string;
}

export interface PaginatedData<T> {
  data: T[];
  meta: {
    currentPage: number;
    lastPage: number;
    perPage: number;
    total: number;
    firstPage: number;
    firstPageUrl: string | null;
    lastPageUrl: string | null;
    nextPageUrl: string | null;
    previousPageUrl: string | null;
  };
} 