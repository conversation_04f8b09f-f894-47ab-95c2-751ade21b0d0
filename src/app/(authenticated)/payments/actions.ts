"use server";

import { api } from "@/lib/api";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";

export const updatePayment = async (data: FormData) => {
  const paymentId = data.get("id") as string;
  await api.put(`payments/${paymentId}`, data);
  revalidatePath("/payments");
  revalidatePath(`/payments/${paymentId}`);
};

export async function deletePayment(data: FormData): Promise<void> {
  const paymentId = data.get("id") as string;
  await api.destroy(paymentId, "payments");
  revalidatePath("/payments");
} 