"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { formatDate } from "date-fns";
import Link from "next/link";
import { FormTemplate } from "@/types/form-template";
import { PaginationMeta } from "@/types/pagination";
import { Button } from "@/components/ui/button";
import { useTransition } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import toast from "react-hot-toast";

export default function FormTemplatesTable({
  data,
  meta,
  deleteTemplate,
}: {
  data: FormTemplate[];
  meta?: PaginationMeta;
  deleteTemplate: (data: FormData) => Promise<void>;
}) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const { data: session } = useSession();

  const handleDelete = async (id: string) => {
    try {
      const formData = new FormData();
      formData.append("id", id);
      await deleteTemplate(formData);
      toast.success("Template deleted successfully");
      router.refresh();
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error("Failed to delete template");
    }
  };

  return (
    <CustomTable
      title="Form Templates"
      columns={[
        {
          name: "Name",
          uid: "name",
          renderCell: (template: FormTemplate) => (
            <Link
              href={`/products/forms/templates/${template.id}`}
              className="flex items-center space-x-2"
            >
              <div>
                <p className="font-medium">{template.name}</p>
                <p className="text-sm text-default-500">{template.id}</p>
              </div>
            </Link>
          ),
        },
        {
          name: "Details",
          uid: "details",
          renderCell: (template: FormTemplate) => (
            <div
              className="max-w-md truncate"
              dangerouslySetInnerHTML={{
                __html: template.details,
              }}
            />
          ),
        },
        {
          name: "Sections",
          uid: "sections",
          renderCell: (template: FormTemplate) => (
            <span className="text-sm">
              {Object.keys(template.sections || {}).length} sections
            </span>
          ),
        },
        {
          name: "Created",
          uid: "createdAt",
          sortable: true,
          renderCell: (template: FormTemplate) => (
            <>
              {formatDate(
                new Date(template.createdAt),
                "EEE, MMM dd, yyyy hh:mm a",
              )}
            </>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (template: FormTemplate) => (
            <div className="flex items-center justify-center gap-4">
              <Link href={`/products/forms/templates/${template.id}`} className="flex items-center justify-center">
                <Icon
                  name="icon-[heroicons--eye-20-solid]"
                  classNames="text-primary"
                />
              </Link>

              <Link href={`/products/forms/templates/${template.id}/edit`} className="flex items-center justify-center">
                <Icon
                  name="icon-[heroicons--pencil-square-20-solid]"
                  classNames="text-primary"
                />
              </Link>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDelete(template.id)}
                disabled={isPending}
                className="flex items-center justify-center p-0 h-auto"
              >
                <Icon
                  name="icon-[heroicons--trash-20-solid]"
                  classNames="text-danger"
                />
              </Button>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
    />
  );
} 