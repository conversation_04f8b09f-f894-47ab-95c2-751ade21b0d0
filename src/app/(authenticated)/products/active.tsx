"use client";

import { ChangeEvent, useState } from "react";
import { showToastPromise } from "@/lib/toast-utils";

export default function ProductActiveStatus({
  defaultChecked,
  onChange,
}: {
  defaultChecked: boolean;
  onChange: (active: boolean) => Promise<void>;
}) {
  const [checked, setChecked] = useState(defaultChecked);

  const handleChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.checked;
    setChecked(newValue);

    await showToastPromise(
      onChange(newValue),
      "product",
      newValue ? "activate" : "deactivate"
    );
  };

  return (
    <label className="inline-flex cursor-pointer items-center">
      <input
        type="checkbox"
        className="peer sr-only"
        defaultChecked={checked}
        onChange={handleChange}
      />
      <div className="peer relative h-6 w-11 rounded-full bg-gray-200 after:absolute after:start-[2px] after:top-[2px] after:h-5 after:w-5 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-default-600 peer-checked:after:translate-x-full peer-checked:after:border-white peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-default-300 dark:border-gray-600 dark:bg-gray-700 dark:peer-focus:ring-default-800 rtl:peer-checked:after:-translate-x-full"></div>
    </label>
  );
} 