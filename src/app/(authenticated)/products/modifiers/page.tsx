import { auth } from "@/auth";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import ModifiersTable from "@/components/tables/modifiers-table";
import { getModifiers } from "@/actions/modifiers";

export const metadata: Metadata = {
  title: "Modifier Options",
  description: "Manage product modifier options",
};

export default async function ModifiersPage() {
  const session = await auth();
  
  // Check if user is logged in
  if (!session || !session.user) {
    redirect("/auth/signin");
  }
  
  // Determine user role
  const userRoles = session.user.roles || [];
  const isAdmin = userRoles.some(role => role.name === "admin");
  const isVendor = userRoles.some(role => role.name === "vendor");
  
  // If the user is not an admin or vendor, they don't have access
  if (!isAdmin && !isVendor) {
    redirect("/dashboard");
  }

  // For admin users, show "coming soon" message
  if (isAdmin) {
    return (
      <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
        <div className="mx-auto max-w-7xl">
          <div className="flex flex-col items-center justify-center py-20 text-center">
            <h1 className="mb-4 text-4xl font-bold text-primary">Coming Soon</h1>
            <p className="mb-8 text-xl text-default-600 dark:text-default-400">
              The Modifier Options feature is currently under development and will be available soon.
            </p>
            <div className="rounded-lg bg-default-100 p-6 dark:bg-default-800">
              <p className="text-default-700 dark:text-default-300">
                This feature will allow administrators to manage product modifier options across the platform.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // For vendor users, continue with the existing functionality
  // Fetch modifiers data
  const modifiers = await getModifiers();

  // Handle case where modifiers might be undefined
  if (!modifiers) {
    return (
      <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
        <div className="mx-auto max-w-7xl">
          <p>No modifiers found</p>
        </div>
      </div>
    );
  }

  // Create pagination meta that matches the expected structure
  const meta = {
    total: modifiers.length,
    perPage: modifiers.length,
    currentPage: 1,
    lastPage: 1,
    firstPage: 1,
    firstPageUrl: "/products/modifiers?page=1",
    lastPageUrl: "/products/modifiers?page=1",
    nextPageUrl: null,
    previousPageUrl: null,
  };

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-7xl">
        <ModifiersTable 
          data={modifiers}
          meta={meta}
          session={session}
        />
      </div>
    </div>
  );
} 