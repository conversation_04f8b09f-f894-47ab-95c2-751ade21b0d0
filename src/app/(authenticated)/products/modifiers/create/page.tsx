import { auth } from "@/auth";
import { Metadata } from "next";
import { redirect } from "next/navigation";
import ModifierForm from "@/components/forms/modifier-form";
import { createModifier } from "@/actions/modifiers";

export const metadata: Metadata = {
  title: "Create Modifier",
  description: "Create a new modifier option",
};

async function handleCreateModifier(data: any, session: any) {
  "use server";
  
  // Add vendorId if the user is a vendor
  const userRoles = session.user.roles || [];
  const isVendor = userRoles.some((role: any) => role.name === "vendor");
  
  if (isVendor && session.vendor?.id) {
    data.vendorId = session.vendor.id;
  }
  
  const result = await createModifier(data);
  
  if (result.success) {
    redirect("/products/modifiers");
  } else {
    throw new Error(result.error || "Failed to create modifier");
  }
}

export default async function CreateModifierPage() {
  const session = await auth();
  
  // Check if user is logged in
  if (!session || !session.user) {
    redirect("/auth/signin");
  }
  
  // Determine user role
  const userRoles = session.user.roles || [];
  const isAdmin = userRoles.some(role => role.name === "admin");
  const isVendor = userRoles.some(role => role.name === "vendor");
  
  // If the user is not an admin or vendor, they don't have access
  if (!isAdmin && !isVendor) {
    redirect("/dashboard");
  }

  const handleSubmit = async (data: any) => {
    await handleCreateModifier(data, session);
  };

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-6 text-2xl font-bold">Create Modifier</h1>
        <ModifierForm 
          onSubmit={handleSubmit}
        />
      </div>
    </div>
  );
} 