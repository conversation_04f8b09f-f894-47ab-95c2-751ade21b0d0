import { auth } from "@/auth";
import { Metadata } from "next";
import { notFound, redirect } from "next/navigation";
import ModifierForm from "@/components/forms/modifier-form";
import { getModifier, updateModifier } from "@/actions/modifiers";

export const metadata: Metadata = {
  title: "Edit Modifier",
  description: "Edit a product modifier option",
};

export default async function EditModifierPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await auth();
  
  // Check if user is logged in
  if (!session || !session.user) {
    redirect("/auth/signin");
  }
  
  // Determine user role
  const userRoles = session.user.roles || [];
  const isAdmin = userRoles.some(role => role.name === "admin");
  const isVendor = userRoles.some(role => role.name === "vendor");
  
  // If the user is not an admin or vendor, they don't have access
  if (!isAdmin && !isVendor) {
    redirect("/dashboard");
  }

  // Fetch the modifier
  const modifier = await getModifier(params.id);
  
  // If the modifier doesn't exist, return 404
  if (!modifier) {
    notFound();
  }
  
  // If the user is a vendor, check if they own this modifier
  if (isVendor && session.vendor?.id && modifier.vendorId !== session.vendor.id) {
    redirect("/products/modifiers");
  }

  const handleUpdateModifier = async (data: any) => {
    "use server";
    
    await updateModifier(params.id, data);
    redirect("/products/modifiers");
  };

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-6 text-2xl font-bold">Edit Modifier</h1>
        <ModifierForm 
          modifier={modifier}
          onSubmit={handleUpdateModifier}
        />
      </div>
    </div>
  );
} 