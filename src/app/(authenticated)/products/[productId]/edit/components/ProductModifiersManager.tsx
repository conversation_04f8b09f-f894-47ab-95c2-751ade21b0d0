'use client';

import { useState, useEffect } from 'react';
import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { Input, Select, SelectItem, Switch, Chip, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, useDisclosure } from '@nextui-org/react';
import { Modifier } from '@/types/modifiers';
import {
  getProductModifiers,
  attachModifierToProduct,
  updateProductModifier,
  detachModifierFromProduct,
  getAvailableModifiers,
  ProductModifierRelationship
} from '@/actions/product-modifiers';
import toast from 'react-hot-toast';
import Link from 'next/link';

interface ProductModifiersManagerProps {
  productId: string;
  vendorId: string;
}

export default function ProductModifiersManager({ productId, vendorId }: ProductModifiersManagerProps) {
  const [productModifiers, setProductModifiers] = useState<ProductModifierRelationship[]>([]);
  const [availableModifiers, setAvailableModifiers] = useState<Modifier[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});
  const [searchTerm, setSearchTerm] = useState("");
  
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();
  
  const [attachForm, setAttachForm] = useState({
    modifierId: '',
    priceAdjustmentOverride: '',
    isDefault: false,
    sortOrder: 0
  });

  // Load data
  useEffect(() => {
    loadData();
  }, [productId, vendorId]);

  const loadData = async () => {
    setIsLoading(true);
    try {
      const [modifiers, available] = await Promise.all([
        getProductModifiers(productId),
        getAvailableModifiers(vendorId)
      ]);
      
      setProductModifiers(modifiers);
      setAvailableModifiers(available);
    } catch (error) {
      toast.error('Failed to load modifiers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAttachModifier = async () => {
    if (!attachForm.modifierId) {
      toast.error('Please select a modifier');
      return;
    }

    setLoadingStates(prev => ({ ...prev, attach: true }));
    
    try {
      const formData = new FormData();
      formData.append('productId', productId);
      formData.append('modifierId', attachForm.modifierId);
      if (attachForm.priceAdjustmentOverride) {
        formData.append('priceAdjustmentOverride', attachForm.priceAdjustmentOverride);
      }
      formData.append('isDefault', attachForm.isDefault.toString());
      formData.append('sortOrder', attachForm.sortOrder.toString());

      const result = await attachModifierToProduct(formData);
      
      if (result.success) {
        toast.success('Modifier attached successfully');
        onClose();
        setAttachForm({ modifierId: '', priceAdjustmentOverride: '', isDefault: false, sortOrder: 0 });
        await loadData();
      } else {
        toast.error(result.error || 'Failed to attach modifier');
      }
    } catch (error) {
      toast.error('Failed to attach modifier');
    } finally {
      setLoadingStates(prev => ({ ...prev, attach: false }));
    }
  };

  const handleDetachModifier = async (relationship: ProductModifierRelationship) => {
    const formData = new FormData();
    formData.append('productId', productId);
    formData.append('modifierId', relationship.id);

    const result = await detachModifierFromProduct(formData);

    if (result.success) {
      toast.success('Modifier detached successfully');
      await loadData();
    } else {
      toast.error(result.error || 'Failed to detach modifier');
    }
  };

  const handleUpdateModifier = async (relationship: ProductModifierRelationship, field: string, value: any) => {
    const relationshipId = relationship.id;
    setLoadingStates(prev => ({ ...prev, [relationshipId]: true }));
    
    try {
      const formData = new FormData();
      formData.append('productId', productId);
      formData.append('modifierId', relationship.modifierId);
      formData.append('priceAdjustmentOverride', relationship.priceAdjustmentOverride?.toString() || '');
      formData.append('isDefault', relationship.isDefault.toString());
      formData.append('sortOrder', relationship.sortOrder.toString());
      
      // Update the specific field
      formData.set(field, value.toString());

      const result = await updateProductModifier(formData);
      
      if (result.success) {
        toast.success('Modifier updated successfully');
        await loadData();
      } else {
        toast.error(result.error || 'Failed to update modifier');
      }
    } catch (error) {
      toast.error('Failed to update modifier');
    } finally {
      setLoadingStates(prev => ({ ...prev, [relationshipId]: false }));
    }
  };

  const columns = [
    {
      name: "Modifier",
      uid: "modifier",
      renderCell: (relationship: ProductModifierRelationship) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Icon name="icon-[mingcute--settings-3-line]" className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{relationship.name}</p>
            {relationship.description && (
              <p className="text-sm text-gray-600">{relationship.description}</p>
            )}
          </div>
        </div>
      ),
    },
    {
      name: "Type",
      uid: "type",
      renderCell: (relationship: ProductModifierRelationship) => (
        <Chip
          color={
            relationship.type === "preparation"
              ? "primary"
              : relationship.type === "condiment"
                ? "secondary"
                : "success"
          }
          variant="flat"
          size="sm"
        >
          {relationship.type}
        </Chip>
      ),
    },
    {
      name: "Price Override",
      uid: "price",
      renderCell: (relationship: ProductModifierRelationship) => (
        <Input
          size="sm"
          type="number"
          step="0.01"
          placeholder="Default price"
          value={relationship.priceAdjustment || ''}
          onChange={(e) => handleUpdateModifier(relationship, 'priceAdjustment', e.target.value)}
          startContent={<span className="text-gray-400">KES</span>}
          isDisabled={loadingStates[relationship.id]}
          className="max-w-32"
        />
      ),
    },
    {
      name: "Default",
      uid: "default",
      renderCell: (relationship: ProductModifierRelationship) => (
        <Switch
          size="sm"
          isSelected={relationship.isDefault}
          onValueChange={(value) => handleUpdateModifier(relationship, 'isDefault', value)}
          isDisabled={loadingStates[relationship.id]}
        />
      ),
    },
    {
      name: "Sort Order",
      uid: "sort",
      renderCell: (relationship: ProductModifierRelationship) => (
        <Input
          size="sm"
          type="number"
          value={relationship.sortOrder.toString()}
          onChange={(e) => handleUpdateModifier(relationship, 'sortOrder', e.target.value)}
          isDisabled={loadingStates[relationship.id]}
          className="max-w-20"
        />
      ),
    },
    {
      name: "Actions",
      uid: "actions",
      renderCell: (relationship: ProductModifierRelationship) => (
        <ActionButton
          variant="outline"
          size="sm"
          onClick={() => openDeleteDialog(relationship, relationship.name)}
          className="text-red-600 hover:text-red-700 hover:border-red-300"
        >
          <Icon name="icon-[mingcute--delete-2-line]" className="w-4 h-4" />
          Detach
        </ActionButton>
      ),
    },
  ];

  // Filter available modifiers to exclude already attached ones and apply search
  const unattachedModifiers = availableModifiers.filter(
    modifier => 
      !productModifiers.some(pm => pm.id === modifier.id) &&
      modifier.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Filter product modifiers by search term
  const filteredProductModifiers = productModifiers.filter(
    modifier => modifier.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Product Modifiers</h3>
            <p className="text-sm text-gray-600">
              Manage modifier options that customers can select for this product
            </p>
          </div>
          <div className="flex gap-3">
            <Link href="/products/modifiers/create">
              <ActionButton color="primary">
                <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
                Add Modifiers
              </ActionButton>
            </Link>
            <ActionButton onClick={onOpen} disabled={unattachedModifiers.length === 0}>
              <Icon name="icon-[mingcute--settings-3-line]" className="w-4 h-4" />
              Attach Modifier
            </ActionButton>
          </div>
        </div>

        {/* Search Filter */}
        <div className="bg-white rounded-lg border p-4">
          <Input
            label="Search Modifiers"
            placeholder="Search modifiers by name..."
            value={searchTerm}
            onValueChange={setSearchTerm}
            startContent={<Icon name="icon-[mingcute--search-line]" className="w-4 h-4 text-gray-400" />}
            className="max-w-md"
          />
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg border">
          <CustomTable
            title="Product Modifiers"
            columns={columns}
            data={filteredProductModifiers}
            searchable
            searchPlaceholder="Search modifiers..."
            isLoading={isLoading}
            emptyContent={
              <div className="text-center py-8">
                <Icon name="icon-[mingcute--settings-3-line]" className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No modifiers attached</h3>
                <p className="text-gray-600 mb-4">Attach modifiers to allow customers to customize this product.</p>
                <ActionButton onClick={onOpen} disabled={unattachedModifiers.length === 0}>
                  <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
                  Attach First Modifier
                </ActionButton>
              </div>
            }
          />
        </div>
      </div>

      {/* Attach Modifier Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalContent>
          <ModalHeader>Attach Modifier to Product</ModalHeader>
          <ModalBody className="space-y-4">
            <Select
              label="Select Modifier"
              placeholder="Choose a modifier to attach"
              selectedKeys={attachForm.modifierId ? [attachForm.modifierId] : []}
              onSelectionChange={(keys) => {
                const selectedKey = Array.from(keys)[0] as string;
                setAttachForm(prev => ({ ...prev, modifierId: selectedKey }));
              }}
            >
              {unattachedModifiers.map((modifier) => (
                <SelectItem key={modifier.id} value={modifier.id}>
                  <div className="flex items-center justify-between">
                    <span>{modifier.name}</span>
                    <Chip size="sm" variant="flat">
                      {modifier.type}
                    </Chip>
                  </div>
                </SelectItem>
              ))}
            </Select>

            <Input
              label="Price Override (KES)"
              placeholder="Leave empty to use default price"
              type="number"
              step="0.01"
              value={attachForm.priceAdjustmentOverride}
              onValueChange={(value) => setAttachForm(prev => ({ ...prev, priceAdjustmentOverride: value }))}
            />

            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Set as Default</p>
                <p className="text-sm text-gray-600">This modifier will be pre-selected for customers</p>
              </div>
              <Switch
                isSelected={attachForm.isDefault}
                onValueChange={(value) => setAttachForm(prev => ({ ...prev, isDefault: value }))}
              />
            </div>

            <Input
              label="Sort Order"
              type="number"
              value={attachForm.sortOrder.toString()}
              onValueChange={(value) => setAttachForm(prev => ({ ...prev, sortOrder: parseInt(value) || 0 }))}
              description="Lower numbers appear first"
            />
          </ModalBody>
          <ModalFooter>
            <ActionButton variant="outline" onClick={onClose}>
              Cancel
            </ActionButton>
            <ActionButton 
              onClick={handleAttachModifier}
              isLoading={loadingStates.attach}
              disabled={!attachForm.modifierId}
            >
              Attach Modifier
            </ActionButton>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <DeleteConfirmationDialog />
    </>
  );
}
