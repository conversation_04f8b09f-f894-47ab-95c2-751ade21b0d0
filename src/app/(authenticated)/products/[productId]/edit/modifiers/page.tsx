import { Suspense } from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { auth } from '@/auth';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { getProduct } from '@/actions/products';
import ProductModifiersManager from '../components/ProductModifiersManager';

interface PageProps {
  params: { productId: string };
}

export default async function ProductModifiersPage({ params }: PageProps) {
  return (
    <div className="p-6 space-y-6">
      <Suspense fallback={
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      }>
        <ProductModifiersContent productId={params.productId} />
      </Suspense>
    </div>
  );
}

async function ProductModifiersContent({ productId }: { productId: string }) {
  try {
    const session = await auth();
    if (!session?.user) {
      notFound();
    }

    const product = await getProduct(productId);
    if (!product) {
      notFound();
    }

    // Check if user has access to this product
    const isAdmin = session.user.role === 'admin';
    const isVendor = session.user.role === 'vendor' && session.vendor?.id === product.vendorId;
    const isBranchAdmin = session.user.role === 'branch_admin' && session.branch?.vendorId === product.vendorId;

    if (!isAdmin && !isVendor && !isBranchAdmin) {
      notFound();
    }

    return (
      <>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/products/${productId}/edit`}>
              <ActionButton variant="outline" size="sm">
                <Icon name="icon-[mingcute--arrow-left-line]" className="w-4 h-4" />
              </ActionButton>
            </Link>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Product Modifiers</h1>
              <p className="text-gray-600 mt-1">
                Manage modifier options for &quot;{product.name}&quot;
              </p>
            </div>
          </div>
          <Link href={`/products/${productId}`}>
            <ActionButton variant="outline">
              <Icon name="icon-[mingcute--eye-line]" className="w-4 h-4" />
              View Product
            </ActionButton>
          </Link>
        </div>

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/products" className="hover:text-primary">
            Products
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <Link href={`/products/${productId}`} className="hover:text-primary">
            {product.name}
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <Link href={`/products/${productId}/edit`} className="hover:text-primary">
            Edit
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <span className="text-gray-900">Modifiers</span>
        </nav>

        {/* Product Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Icon name="icon-[mingcute--box-3-line]" className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900">{product.name}</h3>
              <p className="text-sm text-blue-700">
                {product.type} • KES {product.price}
                {product.ref && ` • Ref: ${product.ref}`}
              </p>
            </div>
          </div>
        </div>

        {/* Modifiers Manager */}
        <ProductModifiersManager 
          productId={productId} 
          vendorId={product.vendorId} 
        />

        {/* Help Section */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <Icon name="icon-[mingcute--lightbulb-line]" className="w-5 h-5 text-gray-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Modifier Management Tips</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• <strong>Attach:</strong> Add modifiers that customers can select for this product</li>
                <li>• <strong>Price Override:</strong> Set custom pricing for this product (overrides modifier default)</li>
                <li>• <strong>Default:</strong> Pre-select modifiers that are commonly chosen</li>
                <li>• <strong>Sort Order:</strong> Control the display order of modifiers (lower numbers first)</li>
                <li>• <strong>Detach:</strong> Remove modifiers that are no longer relevant</li>
              </ul>
            </div>
          </div>
        </div>
      </>
    );
  } catch (error) {
    return (
      <div className="p-8 text-center">
        <Icon name="icon-[mingcute--alert-circle-line]" className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load product</h3>
        <p className="text-gray-600 mb-4">
          {error instanceof Error ? error.message : 'An unexpected error occurred'}
        </p>
        <Link href="/products">
          <ActionButton>
            Back to Products
          </ActionButton>
        </Link>
      </div>
    );
  }
}
