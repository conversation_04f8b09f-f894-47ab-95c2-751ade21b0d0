"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON>, Card, CardBody, CardHeader, Chip } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { getPackagingOption } from "@/actions/packaging-options";
import toast from "react-hot-toast";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface PageProps {
  params: {
    optionId: string;
  };
}

export default function PackagingOptionPage({ params }: PageProps) {
  const router = useRouter();
  const [packagingOption, setPackagingOption] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadPackagingOption = async () => {
      try {
        const option = await getPackagingOption(params.optionId);

        if (!option) {
          router.push('/not-found');
          return;
        }

        setPackagingOption(option);
      } catch (err) {
        console.error("Error loading packaging option:", err);
        setError("Failed to load packaging option details. Some information may not be available.");
      } finally {
        setLoading(false);
      }
    };

    loadPackagingOption();
  }, [params.optionId, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-default-50">
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-600">Loading...</div>
        </div>
      </div>
    );
  }

  const formatPrice = (price: string | number) => {
    return `KES ${parseFloat(price.toString()).toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="min-h-screen bg-default-50">
      {/* Header */}
      <div className="bg-white border-b border-default-200">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                as={Link}
                href="/products/packaging-options"
                variant="light"
                isIconOnly
                className="text-default-600"
              >
                <Icon name="icon-[heroicons--arrow-left-20-solid]" classNames="h-5 w-5" />
              </Button>
              
              <div>
                <h1 className="text-2xl font-bold text-default-900">
                  {packagingOption?.name || "Packaging Option"}
                </h1>
                <p className="text-default-600">
                  Packaging option details
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {packagingOption && (
                <Button
                  as={Link}
                  href={`/products/packaging-options/${packagingOption.id}/edit`}
                  color="primary"
                  startContent={<Icon name="icon-[heroicons--pencil-20-solid]" classNames="h-4 w-4" />}
                >
                  Edit
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-6">
        {/* Error Alert */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-3">
              <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-red-800 font-medium mb-1">Error loading packaging option</h3>
                <p className="text-red-700 text-sm">{error}</p>
              </div>
            </div>
          </div>
        )}

        {packagingOption ? (
          <div className="grid gap-6">
          {/* Main Details Card */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-default-900">Details</h2>
            </CardHeader>
            <CardBody className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-default-600">Name</label>
                  <p className="text-lg font-semibold text-default-900 mt-1">
                    {packagingOption.name}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-default-600">Price</label>
                  <p className="text-lg font-semibold text-default-900 mt-1">
                    {formatPrice(packagingOption.price)}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-default-600">Status</label>
                  <div className="mt-1">
                    <Chip
                      color={packagingOption.active ? "success" : "default"}
                      variant="flat"
                    >
                      {packagingOption.active ? "Active" : "Inactive"}
                    </Chip>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-default-600">ID</label>
                  <p className="text-sm text-default-500 mt-1 font-mono">
                    {packagingOption.id}
                  </p>
                </div>
              </div>

              {packagingOption.description && (
                <div>
                  <label className="text-sm font-medium text-default-600">Description</label>
                  <p className="text-default-900 mt-1 leading-relaxed">
                    {packagingOption.description}
                  </p>
                </div>
              )}
            </CardBody>
          </Card>

          {/* Metadata Card */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-default-900">Metadata</h2>
            </CardHeader>
            <CardBody>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-default-600">Created</label>
                  <p className="text-default-900 mt-1">
                    {formatDate(packagingOption.createdAt)}
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium text-default-600">Last Updated</label>
                  <p className="text-default-900 mt-1">
                    {formatDate(packagingOption.updatedAt)}
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>

          {/* Usage Information Card */}
          <Card>
            <CardHeader>
              <h2 className="text-xl font-semibold text-default-900">Usage Information</h2>
            </CardHeader>
            <CardBody>
              <div className="space-y-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="font-medium text-blue-900 mb-2">How it works</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• This packaging option can be associated with your products</li>
                    <li>• Charges are automatically applied to Takeaway, Delivery, and Self-pickup orders</li>
                    <li>• Packaging charges are NOT applied to Dine-in orders</li>
                    <li>• Total charge = (Packaging Price × Product Quantity) for each selected option</li>
                  </ul>
                </div>

                {!packagingOption.active && (
                  <div className="p-4 bg-orange-50 rounded-lg border border-orange-200">
                    <h3 className="font-medium text-orange-900 mb-2">Inactive Status</h3>
                    <p className="text-sm text-orange-800">
                      This packaging option is currently inactive and cannot be selected for products. 
                      Activate it to make it available for use.
                    </p>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        </div>
        ) : (
          // Fallback content when packaging option is not available
          <div className="text-center py-12">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-12 w-12 text-orange-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-default-900 mb-2">Packaging option not found</h3>
            <p className="text-default-600 mb-6">
              The packaging option could not be loaded. It may have been deleted or you may not have permission to access it.
            </p>
            <div className="flex items-center justify-center gap-3">
              <Button
                as={Link}
                href="/products/packaging-options"
                variant="light"
              >
                Back to Packaging Options
              </Button>
              <Button
                color="primary"
                onPress={() => window.location.reload()}
                startContent={<Icon name="icon-[heroicons--arrow-path-20-solid]" classNames="h-4 w-4" />}
              >
                Try Again
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
