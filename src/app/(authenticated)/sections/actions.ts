"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { auth } from "@/auth";

export const createSection = async (data: FormData) => {
  const session = await auth();
  if (!session?.branch?.id) {
    return { success: false, error: "No branch found" };
  }

  try {
    const response = await api.post(`branches/${session.branch.id}/sections`, data);
    revalidatePath("/sections");
    return { success: true, data: response };
  } catch (error) {
    console.error("Error creating section:", error);
    return { success: false, error: "Failed to create section" };
  }
};

export const editSection = async (data: FormData) => {
  const session = await auth();
  if (!session?.branch?.id) {
    return { success: false, error: "No branch found" };
  }

  try {
    const response = await api.put(
      `sections/${data.get("id")}`,
      data
    );
    revalidatePath("/sections");
    return { success: true, data: response };
  } catch (error) {
    console.error("Error editing section:", error);
    return { success: false, error: "Failed to edit section" };
  }
};

export const deleteSection = async (data: FormData) => {
  const session = await auth();
  if (!session?.branch?.id) {
    return { success: false, error: "No branch found" };
  }

  try {
    const response = await api.destroy(
      data.get("id") as string,
      "sections"
    );
    revalidatePath("/sections");
    return { success: true, data: response };
  } catch (error) {
    console.error("Error deleting section:", error);
    return { success: false, error: "Failed to delete section" };
  }
};

export const createLot = async (data: FormData) => {
  const session = await auth();
  if (!session?.branch?.id) {
    return { success: false, error: "No branch found" };
  }

  try {
    const response = await api.post(`branches/${session.branch.id}/lots`, data);
    revalidatePath(`/sections/${data.get("sectionId")}/lots`);
    return { success: true, data: response };
  } catch (error) {
    console.error("Error creating lot:", error);
    return { success: false, error: "Failed to create lot" };
  }
};

export const editLot = async (data: FormData) => {
  const session = await auth();
  if (!session?.branch?.id) {
    return { success: false, error: "No branch found" };
  }

  try {
    const response = await api.put(
      `lots/${data.get("id")}`,
      data
    );
    revalidatePath(`/sections/${data.get("sectionId")}/lots`);
    return { success: true, data: response };
  } catch (error) {
    console.error("Error editing lot:", error);
    return { success: false, error: "Failed to edit lot" };
  }
};

export const deleteLot = async (data: FormData) => {
  const session = await auth();
  if (!session?.branch?.id) {
    return { success: false, error: "No branch found" };
  }

  try {
    const response = await api.destroy(
      data.get("id") as string,
      "lots"
    );
    revalidatePath(`/sections/${data.get("sectionId")}/lots`);
    return { success: true, data: response };
  } catch (error) {
    console.error("Error deleting lot:", error);
    return { success: false, error: "Failed to delete lot" };
  }
};