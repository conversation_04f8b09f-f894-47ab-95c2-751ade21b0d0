"use client";

import { useForm } from "react-hook-form";
import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ooter } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { toast } from "react-hot-toast";

interface EditNameModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEdit: (data: FormData) => Promise<{ success: boolean; error?: string }>;
  currentName: string;
  id: string;
  resourceType: "section" | "lot";
  sectionId?: string;
}

export default function EditNameModal({ 
  isOpen, 
  onClose, 
  onEdit, 
  currentName, 
  id, 
  resourceType,
  sectionId 
}: EditNameModalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      name: currentName,
    },
  });

  const onSubmit = async (data: any) => {
    const formData = new FormData();
    formData.append("id", id);
    formData.append("name", data.name);
    if (sectionId) {
      formData.append("sectionId", sectionId);
    }

    try {
      const result = await onEdit(formData);
      if (result.success) {
        toast.success(`${resourceType} updated successfully 👌`);
        onClose();
      } else {
        toast.error(result.error || `Could not update ${resourceType} 🤯`);
      }
    } catch (error) {
      console.error(`Error updating ${resourceType}:`, error);
      toast.error(`Could not update ${resourceType} 🤯`);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader className="flex flex-col gap-1">Edit {resourceType} name</ModalHeader>
          <ModalBody>
            <div className="space-y-2">
              <label htmlFor="name" className="block text-sm font-medium">
                Name
              </label>
              <input
                {...register("name", { required: "Name is required" })}
                type="text"
                id="name"
                className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                placeholder={`Enter ${resourceType} name`}
              />
              {errors.name && (
                <p className="text-sm text-danger">{errors.name.message as string}</p>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="bordered" onPress={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              color="primary"
              isLoading={isSubmitting}
              startContent={<Icon name="icon-[heroicons--pencil-square-20-solid]" />}
            >
              Save Changes
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
} 