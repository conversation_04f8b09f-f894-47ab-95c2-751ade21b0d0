"use client";

import { useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface CreateSectionFormProps {
  createSection: (data: FormData) => Promise<void>;
}

export default function CreateSectionForm({ createSection }: CreateSectionFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      name: "",
      details: "",
    },
  });

  const onSubmit = async (data: any) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);

    try {
      await toast.promise(createSection(formData), {
        loading: "Creating section...",
        success: "Section created successfully 👌",
        error: "Could not create section 🤯",
      });
    } catch (error) {
      console.error("Error creating section:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Create Section</h1>
        <Link href="/sections">
          <Button variant="outline">Back to sections</Button>
        </Link>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="name" className="block text-sm font-medium">
            Name
          </label>
          <input
            {...register("name", { required: "Name is required" })}
            type="text"
            id="name"
            className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Enter section name"
          />
          {errors.name && (
            <p className="text-sm text-danger">{errors.name.message as string}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="details" className="block text-sm font-medium">
            Details
          </label>
          <textarea
            {...register("details")}
            id="details"
            rows={4}
            className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Enter section details"
          />
        </div>

        <div className="flex justify-end">
          <Button type="submit" disabled={isSubmitting}>
            Create Section
          </Button>
        </div>
      </form>
    </div>
  );
} 