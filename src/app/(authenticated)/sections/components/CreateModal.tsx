"use client";

import { useForm } from "react-hook-form";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ooter } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { toast } from "react-hot-toast";

interface CreateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreate: (data: FormData) => Promise<{ success: boolean; error?: string }>;
  resourceType: "section" | "lot";
  sectionId?: string;
}

export default function CreateModal({ 
  isOpen, 
  onClose, 
  onCreate, 
  resourceType,
  sectionId 
}: CreateModalProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    defaultValues: {
      name: "",
      details: "",
    },
  });

  const onSubmit = async (data: any) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);
    if (sectionId) {
      formData.append("sectionId", sectionId);
    }

    try {
      const result = await onCreate(formData);
      if (result.success) {
        toast.success(`${resourceType} created successfully 👌`);
        reset();
        onClose();
      } else {
        toast.error(result.error || `Could not create ${resourceType} 🤯`);
      }
    } catch (error) {
      console.error(`Error creating ${resourceType}:`, error);
      toast.error(`Could not create ${resourceType} 🤯`);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose}>
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader className="flex flex-col gap-1">Create {resourceType}</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="name" className="block text-sm font-medium">
                  Name
                </label>
                <input
                  {...register("name", { required: "Name is required" })}
                  type="text"
                  id="name"
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder={`Enter ${resourceType} name`}
                />
                {errors.name && (
                  <p className="text-sm text-danger">{errors.name.message as string}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="details" className="block text-sm font-medium">
                  Details
                </label>
                <textarea
                  {...register("details")}
                  id="details"
                  rows={4}
                  className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
                  placeholder={`Enter ${resourceType} details`}
                />
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="bordered" onPress={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              color="primary"
              isLoading={isSubmitting}
              startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
            >
              Create {resourceType}
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
} 