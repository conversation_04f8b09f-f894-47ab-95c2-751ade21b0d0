"use client";

import { CustomTable, useDeleteConfirmation } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { ActionButton } from "@/components/ui/action-button";
import { MdEdit, MdDelete } from "react-icons/md";
import { formatDate } from "date-fns";
import { Lot } from "../types";
import { PaginationMeta } from "@/types/pagination";
import { Button } from "@nextui-org/react";
import { useTransition, useState } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import EditNameModal from "./EditNameModal";
import CreateModal from "./CreateModal";
import { editLot, deleteLot, createLot } from "../actions";

interface LotsTableProps {
  data: Lot[];
  meta?: PaginationMeta;
  sectionId: string;
}

export default function LotsTable({ data, meta, sectionId }: LotsTableProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const [editingLot, setEditingLot] = useState<Lot | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  const handleDelete = async (id: string) => {
    try {
      const formData = new FormData();
      formData.append("id", id);
      const result = await deleteLot(formData);
      if (result.success) {
        toast.success("Lot deleted successfully");
        router.refresh();
      } else {
        toast.error(result.error || "Failed to delete lot");
      }
    } catch (error) {
      console.error('Error deleting lot:', error);
      toast.error("Failed to delete lot");
    }
  };

  const handleDeleteClick = (lot: Lot) => {
    openDeleteDialog(
      lot.id,
      lot.name,
      () => handleDelete(lot.id)
    );
  };

  return (
    <>
      <CustomTable
        title=""
        columns={[
          {
            name: "Name",
            uid: "name",
            renderCell: (lot: Lot) => (
              <div className="flex items-center space-x-2">
                <div>
                  <span className="font-medium">{lot.name}</span>
                  <p className="text-sm text-default-500">{lot.id}</p>
                </div>
              </div>
            ),
          },
          {
            name: "Details",
            uid: "details",
            renderCell: (lot: Lot) => (
              <div
                className="max-w-md truncate"
                dangerouslySetInnerHTML={{
                  __html: lot.details,
                }}
              />
            ),
          },
          {
            name: "Created",
            uid: "createdAt",
            sortable: true,
            renderCell: (lot: Lot) => (
              <>
                {formatDate(
                  new Date(lot.createdAt),
                  "EEE, MMM dd, yyyy hh:mm a",
                )}
              </>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (lot: Lot) => (
              <div className="flex items-center justify-center gap-3">
                <ActionButton
                  variant="ghost"
                  size="sm"
                  onClick={() => setEditingLot(lot)}
                >
                  <MdEdit className="h-4 w-4 text-primary" />
                </ActionButton>

                <ActionButton
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteClick(lot)}
                  disabled={isPending}
                  className="text-red-500 hover:text-red-700"
                >
                  <MdDelete className="h-4 w-4" />
                </ActionButton>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
        action={
          <div className="flex items-center justify-between">
            <div className="flex">
              <Button
                color="primary"
                startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
                onPress={() => setIsCreateModalOpen(true)}
              >
                Create Lot
              </Button>
            </div>
          </div>
        }
      />

      {editingLot && (
        <EditNameModal
          isOpen={!!editingLot}
          onClose={() => setEditingLot(null)}
          onEdit={editLot}
          currentName={editingLot.name}
          id={editingLot.id}
          resourceType="lot"
        />
      )}

      <CreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreate={createLot}
        resourceType="lot"
        sectionId={sectionId}
      />
      <DeleteConfirmationDialog />
    </>
  );
}