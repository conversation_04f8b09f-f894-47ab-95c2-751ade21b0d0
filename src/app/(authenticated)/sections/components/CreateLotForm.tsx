"use client";

import { useForm } from "react-hook-form";
import { showToastPromise } from "@/lib/toast-utils";
import Link from "next/link";
import { Button } from "@nextui-org/react";
import { Icon } from "@/components/icon";

interface CreateLotFormProps {
  createLot: (data: FormData) => Promise<void>;
  sectionId: string;
}

export default function CreateLotForm({ createLot, sectionId }: CreateLotFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm({
    defaultValues: {
      name: "",
      details: "",
    },
  });

  const onSubmit = async (data: any) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);
    formData.append("sectionId", sectionId);

    try {
      await showToastPromise(
        createLot(formData),
        "lot",
        "create"
      );
    } catch (error) {
      console.error("Error creating lot:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Create Lot</h1>
        <Link href={`/sections/${sectionId}`}>
          <Button variant="bordered">Back to lots</Button>
        </Link>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="name" className="block text-sm font-medium">
            Name
          </label>
          <input
            {...register("name", { required: "Name is required" })}
            type="text"
            id="name"
            className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Enter lot name"
          />
          {errors.name && (
            <p className="text-sm text-danger">{errors.name.message as string}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="details" className="block text-sm font-medium">
            Details
          </label>
          <textarea
            {...register("details")}
            id="details"
            rows={4}
            className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
            placeholder="Enter lot details"
          />
        </div>

        <div className="flex justify-end">
          <Button 
            type="submit" 
            color="primary"
            isLoading={isSubmitting}
            startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          >
            Create Lot
          </Button>
        </div>
      </form>
    </div>
  );
} 