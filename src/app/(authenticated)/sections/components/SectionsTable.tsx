"use client";

import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { formatDate } from "date-fns";
import Link from "next/link";
import { Section } from "../types";
import { PaginationMeta } from "@/types/pagination";
import { Button } from "@nextui-org/react";
import { useTransition, useState } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import EditNameModal from "./EditNameModal";
import CreateModal from "./CreateModal";
import { ConfirmDialog } from "@/components/ui/dialog";
import { editSection, deleteSection, createSection } from "../actions";

interface SectionsTableProps {
  data: Section[];
  meta?: PaginationMeta;
}

export default function SectionsTable({ data, meta }: SectionsTableProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const [editingSection, setEditingSection] = useState<Section | null>(null);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [sectionToDelete, setSectionToDelete] = useState<Section | null>(null);

  const handleDeleteClick = (section: Section) => {
    setSectionToDelete(section);
  };

  const handleConfirmDelete = async () => {
    if (!sectionToDelete) return;
    
    try {
      const formData = new FormData();
      formData.append("id", sectionToDelete.id);
      const result = await deleteSection(formData);
      if (result.success) {
        toast.success("Section deleted successfully");
        router.refresh();
      } else {
        toast.error(result.error || "Failed to delete section");
      }
    } catch (error) {
      console.error('Error deleting section:', error);
      toast.error("Failed to delete section");
    } finally {
      setSectionToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setSectionToDelete(null);
  };

  return (
    <>
      <CustomTable
        title=""
        columns={[
          {
            name: "Name",
            uid: "name",
            renderCell: (section: Section) => (
              <div className="flex items-center space-x-2">
                <div>
                  <Link href={`/sections/${section.id}/lots`} className="font-medium hover:underline">
                    {section.name}
                  </Link>
                  <p className="text-sm text-default-500">{section.id}</p>
                </div>
              </div>
            ),
          },
          {
            name: "Details",
            uid: "details",
            renderCell: (section: Section) => (
              <div
                className="max-w-md truncate"
                dangerouslySetInnerHTML={{
                  __html: section.details,
                }}
              />
            ),
          },
          {
            name: "Created",
            uid: "createdAt",
            sortable: true,
            renderCell: (section: Section) => (
              <>
                {formatDate(
                  new Date(section.createdAt),
                  "EEE, MMM dd, yyyy hh:mm a",
                )}
              </>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (section: Section) => (
              <div className="flex items-center justify-center gap-4">
                <Link href={`/sections/${section.id}/lots`}>
                  <Button
                    isIconOnly
                    variant="light"
                    color="primary"
                  >
                    <Icon
                      name="icon-[heroicons--table-cells-20-solid]"
                    />
                  </Button>
                </Link>

                <Button
                  isIconOnly
                  variant="light"
                  color="primary"
                  onPress={() => setEditingSection(section)}
                >
                  <Icon
                    name="icon-[heroicons--pencil-square-20-solid]"
                  />
                </Button>

                <Button
                  isIconOnly
                  variant="light"
                  color="danger"
                  onPress={() => handleDeleteClick(section)}
                  isDisabled={isPending}
                >
                  <Icon
                    name="icon-[heroicons--trash-20-solid]"
                  />
                </Button>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
        action={
          <div className="flex items-center justify-between">
            <div className="flex">
              <Button
                color="primary"
                startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
                onPress={() => setIsCreateModalOpen(true)}
              >
                Create Section
              </Button>
            </div>
          </div>
        }
      />

      {editingSection && (
        <EditNameModal
          isOpen={!!editingSection}
          onClose={() => setEditingSection(null)}
          onEdit={editSection}
          currentName={editingSection.name}
          id={editingSection.id}
          resourceType="section"
        />
      )}

      <CreateModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onCreate={createSection}
        resourceType="section"
      />

      <ConfirmDialog
        isOpen={!!sectionToDelete}
        onClose={() => setSectionToDelete(null)}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        title="Delete Section"
        message={`Are you sure you want to delete the section "${sectionToDelete?.name}"? This action cannot be undone and will also delete all lots within this section.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="error"
      />
    </>
  );
}
