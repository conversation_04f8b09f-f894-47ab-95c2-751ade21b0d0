export interface Section {
  id: string;
  name: string;
  details: string;
  branchId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Lot {
  id: string;
  name: string;
  details: string;
  sectionId: string;
  image: any;
  createdAt: string;
  updatedAt: string;
  section?: {
    id: string;
    name: string;
    branchId: string;
    details: string;
    active: boolean;
  };
  staff?: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
  }>;
} 