import { Metadata } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Section, Lot } from "../../types";
import LotsTable from "../../components/LotsTable";

export const metadata: Metadata = {
  title: "Section Lots",
  description: "Manage section lots",
};

export default async function SectionLotsPage({
  params,
  searchParams,
}: {
  params: { sectionId: string };
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  if (!session?.branch?.id) {
    redirect("/sections");
  }

  console.log("Fetching lots for branch:", session.branch.id);
  console.log("Section ID:", params.sectionId);

  // Get all sections for the branch
  const sections = await api.get<{ data: Section[]; meta: any }>(
    `branches/${session.branch.id}/sections`,
    { per: 100 } // Get all sections
  );

  // Find the specific section
  const section = sections?.data?.find(s => s.id === params.sectionId);
  console.log("Found section:", section);

  if (!section) {
    return (
      <div className="p-6">
        <p>Section not found</p>
      </div>
    );
  }

  // Get all lots for the branch
  const lots = await api.get<{ data: Lot[]; meta: any }>(
    `branches/${session.branch.id}/lots`,
    { per: 100, ...searchParams } // Increased per page to get all lots
  );

  console.log("API Response:", {
    hasData: !!lots,
    dataLength: lots?.data?.length,
    firstLot: lots?.data?.[0],
    meta: lots?.meta
  });

  // Filter lots by section ID
  const sectionLots = lots?.data?.filter(lot => lot.sectionId === params.sectionId) || [];
  console.log("Filtered lots:", {
    totalLots: lots?.data?.length || 0,
    filteredLots: sectionLots.length,
    sectionId: params.sectionId
  });

  // Transform meta data to match PaginationMeta interface
  const transformedMeta = {
    total: sectionLots.length,
    perPage: 10,
    currentPage: Number(searchParams.page) || 1,
    lastPage: Math.ceil(sectionLots.length / 10),
    firstPage: 1,
    firstPageUrl: `/sections/${params.sectionId}/lots?page=1&per=10`,
    lastPageUrl: `/sections/${params.sectionId}/lots?page=${Math.ceil(sectionLots.length / 10)}&per=10`,
    nextPageUrl: null,
    previousPageUrl: null,
  };

  // Paginate the filtered lots
  const page = Number(searchParams.page) || 1;
  const perPage = Number(searchParams.per) || 10;
  const start = (page - 1) * perPage;
  const end = start + perPage;
  const paginatedLots = sectionLots.slice(start, end);

  console.log("Final paginated lots:", {
    page,
    perPage,
    start,
    end,
    paginatedLotsCount: paginatedLots.length
  });

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Lots in {section.name}</h1>
        <p className="text-default-500">{section.details}</p>
      </div>
      <LotsTable 
        data={paginatedLots} 
        meta={transformedMeta} 
        sectionId={params.sectionId} 
      />
    </div>
  );
} 