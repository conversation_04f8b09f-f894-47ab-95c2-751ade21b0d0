import { Metadata } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Section } from "../../../types";
import CreateLotForm from "../../../components/CreateLotForm";
import { createLot } from "../../../actions";

export const metadata: Metadata = {
  title: "Create Lot",
  description: "Create a new lot",
};

export default async function CreateLotPage({
  params,
}: {
  params: { sectionId: string };
}) {
  const session = await auth();
  if (!session?.branch?.id) {
    redirect("/sections");
  }

  // Fetch section details
  const section = await api.get<Section>(`sections/${params.sectionId}`);

  if (!section) {
    return (
      <div className="p-6">
        <p>Section not found</p>
      </div>
    );
  }

  // Wrapper function to handle the createLot action result
  const handleCreateLot = async (formData: FormData) => {
    const result = await createLot(formData);
    if (!result.success) {
      throw new Error(result.error || "Failed to create lot");
    }
  };

  return (
    <div className="p-6">
      <CreateLotForm createLot={handleCreateLot} sectionId={params.sectionId} />
    </div>
  );
} 