import { Metadata } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Lot } from "../sections/types";
import LotsTable from "../sections/components/LotsTable";

export const metadata: Metadata = {
  title: "Lots",
  description: "Manage lots",
};

export default async function LotsPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  if (!session?.branch?.id) {
    redirect("/sections");
  }

  // If sectionId is provided, use the section-specific endpoint
  const endpoint = searchParams.sectionId 
    ? `branches/${session.branch.id}/sections/${searchParams.sectionId}/lots`
    : `branches/${session.branch.id}/lots`;

  const lots = await api.get<{ data: Lot[]; meta: any }>(
    endpoint,
    { per: 10, ...searchParams }
  );

  if (!lots) {
    return (
      <div className="p-6">
        <p>No lots found</p>
      </div>
    );
  }

  // Transform meta data to match PaginationMeta interface
  const transformedMeta = {
    ...lots.meta,
    firstPage: 1,
    firstPageUrl: `/lots?page=1&per=${lots.meta.perPage}${searchParams.sectionId ? `&sectionId=${searchParams.sectionId}` : ''}`,
    lastPageUrl: `/lots?page=${lots.meta.lastPage}&per=${lots.meta.perPage}${searchParams.sectionId ? `&sectionId=${searchParams.sectionId}` : ''}`,
    nextPageUrl: lots.meta.currentPage < lots.meta.lastPage 
      ? `/lots?page=${lots.meta.currentPage + 1}&per=${lots.meta.perPage}${searchParams.sectionId ? `&sectionId=${searchParams.sectionId}` : ''}`
      : null,
    previousPageUrl: lots.meta.currentPage > 1
      ? `/lots?page=${lots.meta.currentPage - 1}&per=${lots.meta.perPage}${searchParams.sectionId ? `&sectionId=${searchParams.sectionId}` : ''}`
      : null,
  };

  return (
    <div className="p-6">
      <LotsTable 
        data={lots.data} 
        meta={transformedMeta} 
        sectionId={searchParams.sectionId || ''} 
      />
    </div>
  );
} 