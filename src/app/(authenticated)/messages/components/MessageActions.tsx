'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Card, Select, SelectItem, Badge } from '@nextui-org/react';
import { api } from '@/lib/api';
import { ActionType } from '../system-actions/components/types';
import toast from 'react-hot-toast';

interface MessageAction {
  id: string;
  type_id: string;
  config: Record<string, any>;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
}

interface MessageActionsProps {
  messageId: string;
  templateActions?: MessageAction[];
  initialActions?: MessageAction[];
  onActionsChange?: (actions: MessageAction[]) => void;
}

export default function MessageActions({ 
  messageId,
  templateActions = [],
  initialActions = [],
  onActionsChange 
}: MessageActionsProps) {
  const [actions, setActions] = useState<MessageAction[]>(initialActions);
  const [actionTypes, setActionTypes] = useState<ActionType[]>([]);
  const [selectedType, setSelectedType] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [showInherited, setShowInherited] = useState(true);

  // Load available action types
  useEffect(() => {
    const loadActionTypes = async () => {
      try {
        const response = await api.get<{ data: ActionType[] }>('/api/v1/action-types');
        if (response?.data) {
          setActionTypes(response.data);
        }
      } catch (error) {
        toast.error('Failed to load action types');
      }
    };
    loadActionTypes();
  }, []);

  const handleAddAction = async () => {
    if (!selectedType) return;

    const actionType = actionTypes.find(type => type.id === selectedType);
    if (!actionType) return;

    const newAction = {
      id: crypto.randomUUID(),
      type_id: selectedType,
      config: actionType.default_config,
      status: 'pending' as const
    };

    const updatedActions = [...actions, newAction];
    setActions(updatedActions);
    onActionsChange?.(updatedActions);
    setSelectedType('');
  };

  const handleRemoveAction = (actionId: string) => {
    const updatedActions = actions.filter(action => action.id !== actionId);
    setActions(updatedActions);
    onActionsChange?.(updatedActions);
  };

  const handleUpdateActionStatus = async (actionId: string, newStatus: MessageAction['status']) => {
    try {
      await api.put(`/api/v1/messages/${messageId}/actions/${actionId}`, {
        status: newStatus
      });

      const updatedActions = actions.map(action => 
        action.id === actionId ? { ...action, status: newStatus } : action
      );
      setActions(updatedActions);
      onActionsChange?.(updatedActions);
      toast.success('Action status updated');
    } catch (error) {
      toast.error('Failed to update action status');
    }
  };

  const displayedActions = showInherited 
    ? [...templateActions, ...actions]
    : actions;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Select
            label="Action Type"
            placeholder="Select an action type"
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="max-w-xs"
          >
            {actionTypes.map((type) => (
              <SelectItem key={type.id} value={type.id}>
                {type.name}
              </SelectItem>
            ))}
          </Select>
          <Button 
            color="primary"
            onClick={handleAddAction}
            isDisabled={!selectedType}
          >
            Add Action
          </Button>
        </div>
        <Button
          variant="light"
          onClick={() => setShowInherited(!showInherited)}
        >
          {showInherited ? 'Hide Inherited' : 'Show Inherited'}
        </Button>
      </div>

      <div className="grid gap-4">
        {displayedActions.map((action) => {
          const actionType = actionTypes.find(type => type.id === action.type_id);
          const isInherited = templateActions.some(templateAction => templateAction.id === action.id);
          
          return (
            <Card key={action.id} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="text-lg font-semibold">{actionType?.name}</h3>
                    {isInherited && (
                      <Badge color="primary" variant="flat">Inherited</Badge>
                    )}
                  </div>
                  <p className="text-sm text-default-500">{actionType?.description}</p>
                  <div className="mt-2">
                    <Select
                      size="sm"
                      value={action.status}
                      onChange={(e) => handleUpdateActionStatus(action.id, e.target.value as MessageAction['status'])}
                      className="max-w-xs"
                    >
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </Select>
                  </div>
                </div>
                {!isInherited && (
                  <Button
                    color="danger"
                    variant="light"
                    onClick={() => handleRemoveAction(action.id)}
                  >
                    Remove
                  </Button>
                )}
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
} 