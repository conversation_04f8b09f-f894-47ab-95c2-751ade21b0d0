"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { Button, Select, SelectItem } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { createMessage, updateMessage } from "../actions";
import { toast } from "react-hot-toast";
import AsyncSelect from "react-select/async";
import { useSession } from "next-auth/react";
import { FormModalWithForm } from "@/components/ui/form-modal";

interface MessageFormProps {
  defaultValues?: Partial<Message>;
  isEditing?: boolean;
  trigger?: React.ReactNode;
}

interface Message {
  id?: string;
  title: string;
  details: string;
  templateId?: string;
  type: "all" | "groups" | "individual";
  recipients?: string[];
  groups?: string[];
  channels: string[];
}

interface SelectOption {
  value: string;
  label: string;
}

interface TemplateOption extends SelectOption {
  content?: string;
}

export default function MessageForm({ 
  defaultValues, 
  isEditing = false,
  trigger 
}: MessageFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { data: session } = useSession();
  
  const { register, handleSubmit, control, watch, setValue, formState: { errors, isSubmitting } } = useForm<Message>({
    defaultValues: {
      title: defaultValues?.title || "",
      details: defaultValues?.details || "",
      templateId: defaultValues?.templateId || "",
      type: defaultValues?.type || "all",
      channels: defaultValues?.channels || ["database"],
      recipients: defaultValues?.recipients || [],
      groups: defaultValues?.groups || [],
    },
  });

  const messageType = watch("type");

  const loadTemplates = async (inputValue: string) => {
    const response = await fetch(`/messages/templates?s=${inputValue}`);
    const data = await response.json();
    return data.map((template: any) => ({ 
      value: template.id, 
      label: template.name,
      content: template.content
    }));
  };

  const loadRecipientsOrGroups = async (inputValue: string, type: string) => {
    if (type === "individual") {
      const response = await fetch(`/branches/${session?.branch?.id}/customers?q=${inputValue}`);
      const data = await response.json();
      return data.data.map((customer: any) => ({ 
        label: `${customer.firstName} ${customer.lastName}`,
        value: customer.id 
      }));
    } else {
      const response = await fetch(`/groups?q=${inputValue}`);
      const data = await response.json();
      return data.map((item: any) => ({ 
        label: item.name, 
        value: item.id 
      }));
    }
  };

  const onSubmit = async (data: Message) => {
    // Validate recipients/groups
    const validationResult = validateRecipients();
    if (validationResult !== true) {
      toast.error(validationResult);
      return;
    }

    const formData = new FormData();

    if (isEditing && defaultValues?.id) {
      formData.append("id", defaultValues.id);
    }

    // Append form fields
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach((v, i) => formData.append(`${key}[${i}]`, v));
        } else {
          formData.append(key, value.toString());
        }
      }
    });

    const action = isEditing ? updateMessage : createMessage;

    try {
      await action(formData);
      toast.success(isEditing ? "Message updated successfully!" : "Message created successfully!");
      setIsOpen(false);
    } catch (error) {
      toast.error(isEditing ? "Failed to update message" : "Failed to create message");
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit(onSubmit)();
  };

  // Custom validation for recipients/groups
  const validateRecipients = () => {
    const type = watch("type");
    const recipients = watch("recipients");
    const groups = watch("groups");

    if (type === "individual" && (!recipients || recipients.length === 0)) {
      return "Please select at least one recipient";
    }
    if (type === "groups" && (!groups || groups.length === 0)) {
      return "Please select at least one group";
    }
    return true;
  };

  return (
    <>
      {trigger ? (
        <div onClick={() => setIsOpen(true)}>{trigger}</div>
      ) : (
        <Button
          color="primary"
          startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          onPress={() => setIsOpen(true)}
        >
          New Message
        </Button>
      )}

      <FormModalWithForm
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title={isEditing ? "Edit Message" : "New Message"}
        size="2xl"
        submitLabel={isEditing ? "Update Message" : "Send Message"}
        isSubmitting={isSubmitting}
        onSubmit={handleFormSubmit}
      >
        <div className="space-y-4">
          {/* Message Type Selection */}
          <div>
            <label className="block text-sm font-medium text-default-900 dark:text-white mb-2">
              Message Type
            </label>
            <Select
              {...register("type", { required: "Message type is required" })}
              value={messageType}
              onChange={(e) => setValue("type", e.target.value as Message["type"])}
              isInvalid={!!errors.type}
              errorMessage={errors.type?.message}
            >
              <SelectItem key="all" value="all">All customers</SelectItem>
              <SelectItem key="groups" value="groups">Customer groups</SelectItem>
              <SelectItem key="individual" value="individual">Individual customers</SelectItem>
            </Select>
          </div>

          {/* Recipient Selection */}
          {messageType === "individual" && (
            <div>
              <label className="block text-sm font-medium text-default-900 dark:text-white mb-2">
                Select Recipients *
              </label>
              <AsyncSelect<SelectOption, true>
                isMulti
                cacheOptions
                loadOptions={(inputValue) => loadRecipientsOrGroups(inputValue, "individual")}
                onChange={(selected) => setValue("recipients", selected?.map((option: SelectOption) => option.value) || [])}
                placeholder="Search for customers..."
              />
              {messageType === "individual" && (!watch("recipients") || watch("recipients")?.length === 0) && (
                <p className="mt-1 text-sm text-danger">Please select at least one recipient</p>
              )}
            </div>
          )}

          {messageType === "groups" && (
            <div>
              <label className="block text-sm font-medium text-default-900 dark:text-white mb-2">
                Select Groups *
              </label>
              <AsyncSelect<SelectOption, true>
                isMulti
                cacheOptions
                loadOptions={(inputValue) => loadRecipientsOrGroups(inputValue, "groups")}
                onChange={(selected) => setValue("groups", selected?.map((option: SelectOption) => option.value) || [])}
                placeholder="Search for groups..."
              />
              {messageType === "groups" && (!watch("groups") || watch("groups")?.length === 0) && (
                <p className="mt-1 text-sm text-danger">Please select at least one group</p>
              )}
            </div>
          )}

          {/* Template Selection */}
          <div>
            <label className="block text-sm font-medium text-default-900 dark:text-white mb-2">
              Message Template
            </label>
            <AsyncSelect<TemplateOption>
              isClearable
              isSearchable
              loadOptions={loadTemplates}
              onChange={(selected) => {
                if (selected) {
                  setValue("templateId", selected.value);
                  if (selected.content) {
                    setValue("details", selected.content);
                  }
                }
              }}
              placeholder="Select a template..."
            />
          </div>

          {/* Message Content */}
          <div>
            <label className="block text-sm font-medium text-default-900 dark:text-white mb-2">
              Message Content *
            </label>
            <textarea
              {...register("details", {
                required: "Message content is required",
                minLength: { value: 10, message: "Message must be at least 10 characters long" }
              })}
              rows={6}
              className={`w-full rounded-lg border p-3 text-sm focus:border-default-600 focus:ring-default-600 dark:focus:border-default-500 dark:focus:ring-default-600 ${
                errors.details
                  ? "border-danger bg-danger-50 text-danger-900 dark:border-danger dark:bg-danger-900/20 dark:text-danger"
                  : "border-default-300 bg-default-50 text-default-900 dark:border-default-600 dark:bg-default-700 dark:text-white"
              }`}
              placeholder="Enter your message content..."
            />
            {errors.details && (
              <p className="mt-1 text-sm text-danger">{errors.details.message}</p>
            )}
          </div>

          {/* Notification Channels */}
          <div>
            <label className="block text-sm font-medium text-default-900 dark:text-white mb-2">
              Notification Channels
            </label>
            <div className="grid grid-cols-2 gap-4">
              {["database", "sms", "fcm", "email"].map((channel) => (
                <div key={channel} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id={channel}
                    value={channel}
                    {...register("channels")}
                    className="h-4 w-4 rounded border-default-300 text-primary focus:ring-primary"
                  />
                  <label htmlFor={channel} className="text-sm capitalize">
                    {channel === "fcm" ? "In-App" : channel}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </div>
      </FormModalWithForm>
    </>
  );
}