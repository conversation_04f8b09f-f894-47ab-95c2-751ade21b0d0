'use client';

import { useState, useEffect } from "react";
import { SubmitHand<PERSON>, useForm } from "react-hook-form";
import { toast } from "react-hot-toast";
import { Tabs, Tab } from "@nextui-org/react";
import { api } from "@/lib/api";
import MessageActions from "./MessageActions";

interface MessageTemplate {
  content: string;
  actions: any[];
}

interface ApiResponse<T> {
  data: T;
}

interface MessageFormProps {
  messageId: string;
  templateId?: string;
  onSubmit: (data: FormData) => Promise<void>;
}

export default function MessageForm({
  messageId,
  templateId,
  onSubmit,
}: MessageFormProps) {
  const [selectedTab, setSelectedTab] = useState("content");
  const [template, setTemplate] = useState<MessageTemplate | null>(null);
  const [actions, setActions] = useState<any[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm();

  // Load template if templateId is provided
  useEffect(() => {
    const loadTemplate = async () => {
      if (templateId) {
        try {
          const response = await api.get<ApiResponse<MessageTemplate>>(`/api/v1/message-templates/${templateId}`);
          if (response?.data) {
            const templateData = response.data;
            setTemplate(templateData);
            setValue("content", templateData.content);
            setActions(templateData.actions || []);
          }
        } catch (error) {
          toast.error("Failed to load template");
        }
      }
    };
    loadTemplate();
  }, [templateId, setValue]);

  const handleFormSubmit: SubmitHandler<any> = async (data) => {
    const formData = new FormData();
    formData.append("content", data.content);
    formData.append("templateId", templateId || "");
    formData.append("actions", JSON.stringify(actions));

    try {
      await onSubmit(formData);
      toast.success("Message created successfully");
    } catch (error) {
      toast.error("Failed to create message");
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      <Tabs 
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
      >
        <Tab key="content" title="Content">
          <div className="mt-4">
            <label
              htmlFor="content"
              className="mb-2 block text-sm font-medium text-default-900 dark:text-white"
            >
              Message Content
            </label>
            <textarea
              id="content"
              rows={4}
              {...register("content", { required: true })}
              className="block w-full rounded-lg border border-default-300 bg-default-50 p-3 text-sm text-default-900 focus:border-default-600 focus:ring-default-600 dark:border-default-600 dark:bg-default-700 dark:text-white dark:placeholder-default-400 dark:focus:border-default-500 dark:focus:ring-default-600"
              placeholder="Enter your message content here"
            />
            {errors.content && (
              <p className="mt-1 text-sm text-red-500">Content is required</p>
            )}
          </div>
        </Tab>
        <Tab key="actions" title="Actions">
          <div className="mt-4">
            <MessageActions
              messageId={messageId}
              templateActions={template?.actions || []}
              initialActions={actions}
              onActionsChange={setActions}
            />
          </div>
        </Tab>
      </Tabs>

      <div className="flex justify-end">
        <button
          type="submit"
          disabled={isSubmitting}
          className="rounded-lg bg-primary px-5 py-2 text-center text-sm font-medium text-white hover:bg-default-800 focus:outline-none focus:ring-4 focus:ring-default-300 dark:bg-primary dark:hover:bg-primary dark:focus:ring-default-800"
        >
          {isSubmitting ? "Sending..." : "Send Message"}
        </button>
      </div>
    </form>
  );
} 