"use server";

import { api } from "@/lib/api";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";

export const createMessage = async (data: FormData) => {
  const session = await auth();
  
  // Add authentication context
  data.append("userId", session?.user.id!);
  data.append("vendorId", session?.vendor?.id as string);
  data.append("branchId", session?.branch?.id as string);

  await api.post("messages", data);
  revalidatePath("/messages");
};

export const updateMessage = async (data: FormData) => {
  const messageId = data.get("id") as string;
  await api.put(`messages/${messageId}`, data);
  revalidatePath("/messages");
  revalidatePath(`/messages/${messageId}`);
};

export const deleteMessage = async (data: FormData) => {
  const messageId = data.get("id") as string;
  await api.destroy(messageId, "messages");
  revalidatePath("/messages");
};

export const getMessages = async (searchParams: Record<string, string>) => {
  const session = await auth();
  return api.get<PaginatedData<Message>>("messages", searchParams);
};

export const getMessage = async (id: string) => {
  return api.get<Message>(`messages/${id}`);
}; 