'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Input, Textarea, Switch, Select, SelectItem } from '@nextui-org/react';
import toast from 'react-hot-toast';
import { getActionType, updateActionType } from '../../actions';
import { ActionTypeFormData } from '../../components/types';

// Predefined action types with their schemas
const ACTION_TYPES = {
  button: {
    name: 'Call to Action Button',
    description: 'A clickable button that performs an action',
    config_schema: {
      type: 'object',
      properties: {
        label: {
          type: 'string',
          description: 'Button text'
        },
        action: {
          type: 'string',
          enum: ['open_url', 'send_message', 'make_call']
        },
        url: {
          type: 'string',
          description: 'URL to open (required if action is open_url)'
        }
      },
      required: ['label', 'action']
    },
    default_config: {
      label: 'Click Me',
      action: 'open_url'
    }
  },
  form: {
    name: 'Contact Form',
    description: 'A form to collect user information',
    config_schema: {
      type: 'object',
      properties: {
        fields: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              type: { type: 'string', enum: ['text', 'email', 'phone', 'number'] },
              required: { type: 'boolean' },
              placeholder: { type: 'string' }
            },
            required: ['name', 'type']
          }
        },
        submit_text: { type: 'string' }
      },
      required: ['fields', 'submit_text']
    },
    default_config: {
      fields: [
        {
          name: 'email',
          type: 'email',
          required: true,
          placeholder: 'Enter your email'
        }
      ],
      submit_text: 'Submit'
    }
  },
  quick_reply: {
    name: 'Quick Reply Buttons',
    description: 'Buttons for quick responses',
    config_schema: {
      type: 'object',
      properties: {
        buttons: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              text: { type: 'string' },
              value: { type: 'string' }
            },
            required: ['text', 'value']
          }
        }
      },
      required: ['buttons']
    },
    default_config: {
      buttons: [
        { text: 'Yes', value: 'yes' },
        { text: 'No', value: 'no' }
      ]
    }
  }
};

export default function EditActionTypePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [formData, setFormData] = useState<ActionTypeFormData>({
    type: '',
    name: '',
    description: '',
    config_schema: {
      type: 'object',
      properties: {},
      required: []
    },
    default_config: {},
    is_active: true
  });
  const [loading, setLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchActionType = async () => {
      try {
        const { data, error } = await getActionType(params.id);
        
        if (error) {
          setError(error);
          toast.error(error);
        } else if (data) {
          setFormData({
            type: data.type,
            name: data.name,
            description: data.description,
            config_schema: data.config_schema,
            default_config: data.default_config,
            is_active: data.is_active
          });
        }
      } catch (err) {
        setError('Failed to fetch action type');
        toast.error('Failed to fetch action type');
        console.error('Error fetching action type:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchActionType();
  }, [params.id]);

  const handleTypeChange = (type: string) => {
    const selectedType = ACTION_TYPES[type as keyof typeof ACTION_TYPES];
    if (selectedType) {
      setFormData({
        ...formData,
        type,
        name: selectedType.name,
        description: selectedType.description,
        config_schema: selectedType.config_schema,
        default_config: selectedType.default_config
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { error } = await updateActionType(params.id, formData);
      
      if (error) {
        toast.error(error);
      } else {
        toast.success('Action type updated successfully');
        router.push('/messages/system-actions');
      }
    } catch (err) {
      console.error('Error updating action type:', err);
      toast.error('Failed to update action type');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
        <div className="mx-auto max-w-3xl">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
            <div className="space-y-4">
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
        <div className="mx-auto max-w-3xl">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Error</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button
              color="primary"
              onPress={() => router.push('/messages/system-actions')}
            >
              Back to System Actions
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-3xl">
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-2">Edit Action Type</h1>
          <p className="text-gray-600">Modify an existing action type</p>
        </div>
        
        <div className="rounded-lg border border-default-200 bg-white p-6 shadow-sm dark:border-default-700 dark:bg-default-800">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Select
                  label="Action Type"
                  placeholder="Select an action type"
                  value={formData.type}
                  onChange={(e) => handleTypeChange(e.target.value)}
                  isRequired
                >
                  {Object.entries(ACTION_TYPES).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value.name}
                    </SelectItem>
                  ))}
                </Select>
              </div>

              <div>
                <Input
                  label="Name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  isRequired
                />
              </div>
            </div>

            <div>
              <Textarea
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                isRequired
              />
            </div>

            <div>
              <Textarea
                label="Config Schema"
                value={JSON.stringify(formData.config_schema, null, 2)}
                onChange={(e) => {
                  try {
                    const schema = JSON.parse(e.target.value);
                    setFormData({ ...formData, config_schema: schema });
                  } catch (error) {
                    // Handle invalid JSON
                  }
                }}
                isRequired
              />
            </div>

            <div>
              <Textarea
                label="Default Config"
                value={JSON.stringify(formData.default_config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value);
                    setFormData({ ...formData, default_config: config });
                  } catch (error) {
                    // Handle invalid JSON
                  }
                }}
                isRequired
              />
            </div>

            <div>
              <Switch
                isSelected={formData.is_active}
                onValueChange={(value) => setFormData({ ...formData, is_active: value })}
              >
                Active
              </Switch>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                color="danger"
                variant="light"
                onPress={() => router.push('/messages/system-actions')}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="primary"
                type="submit"
                isLoading={isSubmitting}
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 