'use client';

export default function Error({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-7xl">
        <div className="flex flex-col items-center justify-center space-y-4">
          <h2 className="text-2xl font-bold text-red-500">Something went wrong!</h2>
          <p className="text-gray-600">{error.message}</p>
          <button
            onClick={() => reset()}
            className="rounded-lg bg-primary px-5 py-2 text-white"
          >
            Try again
          </button>
        </div>
      </div>
    </div>
  );
}