import { api } from "@/lib/api";
import { ActionType, ActionTypeFormData } from "../components/types";

export const systemActionsApi = {
  // List all action types (with optional params for pagination/search)
  getAll: (params?: Record<string, string | number>) =>
    api.get<{ data: ActionType[]; meta?: any }>("action-types", params),

  // Get a single action type by ID
  get: (id: string) => api.get<ActionType>(`action-types/${id}`),

  // Create a new action type
  create: (data: ActionTypeFormData) => api.post<ActionType>("action-types", data),

  // Update an existing action type
  update: (id: string, data: ActionTypeFormData) =>
    api.put<ActionType>(`action-types/${id}`, data),

  // Delete an action type
  delete: (id: string) => api.destroy(id, "action-types"),

  // Validate action type configuration
  validateConfig: (id: string, config: Record<string, any>) =>
    api.post<{ valid: boolean }>(`action-types/${id}/validate-config`, { config }),
}; 