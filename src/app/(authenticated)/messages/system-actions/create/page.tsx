'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button, Input, Textarea, Switch, Select, SelectItem } from '@nextui-org/react';
import toast from 'react-hot-toast';
import { createActionType } from '../actions';
import { ActionTypeFormData } from '../components/types';

// Predefined action types with their schemas
const ACTION_TYPES = {
  button: {
    name: 'Call to Action Button',
    description: 'A clickable button that performs an action',
    config_schema: {
      type: 'object',
      properties: {
        label: {
          type: 'string',
          description: 'Button text'
        },
        action: {
          type: 'string',
          enum: ['open_url', 'send_message', 'make_call']
        },
        url: {
          type: 'string',
          description: 'URL to open (required if action is open_url)'
        }
      },
      required: ['label', 'action']
    },
    default_config: {
      label: 'Click Me',
      action: 'open_url'
    }
  },
  form: {
    name: 'Contact Form',
    description: 'A form to collect user information',
    config_schema: {
      type: 'object',
      properties: {
        fields: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              type: { type: 'string', enum: ['text', 'email', 'phone', 'number'] },
              required: { type: 'boolean' },
              placeholder: { type: 'string' }
            },
            required: ['name', 'type']
          }
        },
        submit_text: { type: 'string' }
      },
      required: ['fields', 'submit_text']
    },
    default_config: {
      fields: [
        {
          name: 'email',
          type: 'email',
          required: true,
          placeholder: 'Enter your email'
        }
      ],
      submit_text: 'Submit'
    }
  },
  quick_reply: {
    name: 'Quick Reply Buttons',
    description: 'Buttons for quick responses',
    config_schema: {
      type: 'object',
      properties: {
        buttons: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              text: { type: 'string' },
              value: { type: 'string' }
            },
            required: ['text', 'value']
          }
        }
      },
      required: ['buttons']
    },
    default_config: {
      buttons: [
        { text: 'Yes', value: 'yes' },
        { text: 'No', value: 'no' }
      ]
    }
  },
  feedback: {
    name: 'Feedback Form',
    description: 'A form to collect user feedback',
    config_schema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Feedback form title'
        },
        rating_type: {
          type: 'string',
          enum: ['stars', 'emoji', 'number'],
          description: 'Type of rating to collect'
        },
        max_rating: {
          type: 'number',
          description: 'Maximum rating value (for number type)',
          minimum: 1,
          maximum: 10
        },
        comment_required: {
          type: 'boolean',
          description: 'Whether comment is required'
        },
        comment_placeholder: {
          type: 'string',
          description: 'Placeholder text for comment field'
        }
      },
      required: ['title', 'rating_type']
    },
    default_config: {
      title: 'How was your experience?',
      rating_type: 'stars',
      comment_required: true,
      comment_placeholder: 'Tell us more about your experience...'
    }
  },
  report: {
    name: 'Report Issue',
    description: 'A form to report issues or problems',
    config_schema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Report form title'
        },
        categories: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: 'Available report categories'
        },
        allow_attachments: {
          type: 'boolean',
          description: 'Whether to allow file attachments'
        },
        max_attachments: {
          type: 'number',
          description: 'Maximum number of attachments allowed',
          minimum: 0,
          maximum: 5
        },
        attachment_types: {
          type: 'array',
          items: {
            type: 'string',
            enum: ['image', 'document', 'video']
          },
          description: 'Allowed attachment types'
        }
      },
      required: ['title', 'categories']
    },
    default_config: {
      title: 'Report an Issue',
      categories: ['Bug', 'Feature Request', 'Other'],
      allow_attachments: true,
      max_attachments: 3,
      attachment_types: ['image', 'document']
    }
  },
  link: {
    name: 'External Link',
    description: 'A link to an external resource',
    config_schema: {
      type: 'object',
      properties: {
        url: {
          type: 'string',
          description: 'URL to open',
          format: 'uri'
        },
        open_in_new_tab: {
          type: 'boolean',
          description: 'Whether to open in new tab'
        },
        button_text: {
          type: 'string',
          description: 'Text to display on the button'
        },
        button_style: {
          type: 'string',
          enum: ['primary', 'secondary', 'text'],
          description: 'Style of the button'
        },
        icon: {
          type: 'string',
          description: 'Icon to display (optional)'
        }
      },
      required: ['url', 'button_text']
    },
    default_config: {
      url: 'https://example.com',
      open_in_new_tab: true,
      button_text: 'Visit Website',
      button_style: 'primary'
    }
  }
};

export default function CreateActionTypePage() {
  const router = useRouter();
  const [formData, setFormData] = useState<ActionTypeFormData>({
    type: '',
    name: '',
    description: '',
    config_schema: {
      type: 'object',
      properties: {} as Record<string, any>,
      required: [] as string[]
    },
    default_config: {} as Record<string, any>,
    is_active: true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleTypeChange = (type: string) => {
    const selectedType = ACTION_TYPES[type as keyof typeof ACTION_TYPES];
    if (selectedType) {
      setFormData({
        ...formData,
        type,
        name: selectedType.name,
        description: selectedType.description,
        config_schema: selectedType.config_schema,
        default_config: selectedType.default_config
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const { error } = await createActionType(formData);
      
      if (error) {
        toast.error(error);
      } else {
        toast.success('Action type created successfully');
        router.push('/messages/system-actions');
      }
    } catch (error) {
      console.error('Error creating action type:', error);
      toast.error('Failed to create action type');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-3xl">
        <div className="mb-8">
          <h1 className="text-2xl font-bold mb-2">Create Action Type</h1>
          <p className="text-gray-600">Define a new action type for messages</p>
        </div>
        
        <div className="rounded-lg border border-default-200 bg-white p-6 shadow-sm dark:border-default-700 dark:bg-default-800">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Select
                  label="Action Type"
                  placeholder="Select an action type"
                  value={formData.type}
                  onChange={(e) => handleTypeChange(e.target.value)}
                  isRequired
                >
                  {Object.entries(ACTION_TYPES).map(([key, value]) => (
                    <SelectItem key={key} value={key}>
                      {value.name}
                    </SelectItem>
                  ))}
                </Select>
              </div>

              <div>
                <Input
                  label="Name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  isRequired
                />
              </div>
            </div>

            <div>
              <Textarea
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                isRequired
              />
            </div>

            <div>
              <Textarea
                label="Config Schema"
                value={JSON.stringify(formData.config_schema, null, 2)}
                onChange={(e) => {
                  try {
                    const schema = JSON.parse(e.target.value);
                    setFormData({ ...formData, config_schema: schema });
                  } catch (error) {
                    // Handle invalid JSON
                  }
                }}
                isRequired
              />
            </div>

            <div>
              <Textarea
                label="Default Config"
                value={JSON.stringify(formData.default_config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value);
                    setFormData({ ...formData, default_config: config });
                  } catch (error) {
                    // Handle invalid JSON
                  }
                }}
                isRequired
              />
            </div>

            <div>
              <Switch
                isSelected={formData.is_active}
                onValueChange={(value) => setFormData({ ...formData, is_active: value })}
              >
                Active
              </Switch>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <Button
                color="danger"
                variant="light"
                onPress={() => router.push('/messages/system-actions')}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                color="primary"
                type="submit"
                isLoading={isSubmitting}
              >
                Create Action Type
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 