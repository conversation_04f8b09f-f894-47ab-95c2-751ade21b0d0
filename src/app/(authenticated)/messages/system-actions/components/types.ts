export interface ActionType {
  id: string;
  type: string;
  name: string;
  description: string;
  config_schema: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  default_config: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ActionTypeFormData {
  type: string;
  name: string;
  description: string;
  config_schema: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  default_config: Record<string, any>;
  is_active: boolean;
}

export interface ApiResponse<T> {
  data: T;
  meta?: {
    total: number;
    page: number;
    per_page: number;
  };
}
