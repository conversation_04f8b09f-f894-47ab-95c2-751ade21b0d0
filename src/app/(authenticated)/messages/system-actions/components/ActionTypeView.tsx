'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@nextui-org/react";
import { ActionType } from './types';

interface ActionTypeViewProps {
  isOpen: boolean;
  onClose: () => void;
  actionType: ActionType;
}

export default function ActionTypeView({ isOpen, onClose, actionType }: ActionTypeViewProps) {
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="2xl"
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          Action Type Details
        </ModalHeader>
        <ModalBody>
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-gray-500">Type</h3>
              <p className="mt-1">{actionType.type}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Name</h3>
              <p className="mt-1">{actionType.name}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Description</h3>
              <p className="mt-1">{actionType.description}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Config Schema</h3>
              <pre className="mt-1 bg-gray-50 p-2 rounded-md overflow-auto">
                {JSON.stringify(actionType.config_schema, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Default Config</h3>
              <pre className="mt-1 bg-gray-50 p-2 rounded-md overflow-auto">
                {JSON.stringify(actionType.default_config, null, 2)}
              </pre>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Status</h3>
              <p className="mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  actionType.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {actionType.is_active ? 'Active' : 'Inactive'}
                </span>
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Created At</h3>
              <p className="mt-1">{new Date(actionType.created_at).toLocaleString()}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500">Updated At</h3>
              <p className="mt-1">{new Date(actionType.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onPress={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 