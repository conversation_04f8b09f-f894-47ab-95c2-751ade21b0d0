'use client';

import { useEffect, useState } from 'react';
import ActionTypesTable from './ActionTypesTable';
import { getActionTypes } from '../actions';
import { ActionType } from './types';

export function SystemActionsContent() {
  const [actionTypes, setActionTypes] = useState<ActionType[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadActionTypes = async () => {
      const { data, error } = await getActionTypes();
      if (error) {
        setError(error);
      } else if (data) {
        setActionTypes(data);
      }
    };

    loadActionTypes();
  }, []);

  return (
    <div className="h-full min-h-screen bg-white p-5 dark:bg-default-900">
      <div className="mx-auto max-w-7xl">
        <div className="mb-6">
          <h1 className="text-2xl font-bold">System Actions</h1>
        </div>
        <ActionTypesTable initialActionTypes={actionTypes} initialError={error} />
      </div>
    </div>
  );
}
