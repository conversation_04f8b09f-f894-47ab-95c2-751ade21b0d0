'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dal<PERSON><PERSON>, <PERSON>dal<PERSON>ooter, <PERSON>ton, Input, Textarea, Switch } from "@nextui-org/react";
import { Icon } from "@/components/icon";

interface ActionTypeFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ActionTypeFormData) => void;
  initialData?: ActionTypeFormData;
  mode: 'create' | 'edit';
}

export interface ActionTypeFormData {
  type: string;
  name: string;
  description: string;
  config_schema: object;
  default_config: object;
  is_active: boolean;
}

export default function ActionTypeForm({ isOpen, onClose, onSubmit, initialData, mode }: ActionTypeFormProps) {
  const [formData, setFormData] = useState<ActionTypeFormData>({
    type: '',
    name: '',
    description: '',
    config_schema: {},
    default_config: {},
    is_active: true,
  });

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    }
  }, [initialData]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="2xl"
    >
      <ModalContent>
        <form onSubmit={handleSubmit}>
          <ModalHeader className="flex flex-col gap-1">
            {mode === 'create' ? 'Create New Action Type' : 'Edit Action Type'}
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Type"
                placeholder="Enter action type (e.g., email_notification)"
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                isRequired
              />
              <Input
                label="Name"
                placeholder="Enter action name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                isRequired
              />
              <Textarea
                label="Description"
                placeholder="Enter action description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                isRequired
              />
              <Textarea
                label="Config Schema"
                placeholder="Enter JSON schema"
                value={JSON.stringify(formData.config_schema, null, 2)}
                onChange={(e) => {
                  try {
                    const schema = JSON.parse(e.target.value);
                    setFormData({ ...formData, config_schema: schema });
                  } catch (error) {
                    // Handle invalid JSON
                  }
                }}
                isRequired
              />
              <Textarea
                label="Default Config"
                placeholder="Enter default configuration"
                value={JSON.stringify(formData.default_config, null, 2)}
                onChange={(e) => {
                  try {
                    const config = JSON.parse(e.target.value);
                    setFormData({ ...formData, default_config: config });
                  } catch (error) {
                    // Handle invalid JSON
                  }
                }}
                isRequired
              />
              <Switch
                isSelected={formData.is_active}
                onValueChange={(value) => setFormData({ ...formData, is_active: value })}
              >
                Active
              </Switch>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button color="primary" type="submit">
              {mode === 'create' ? 'Create' : 'Save Changes'}
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
} 