'use client';

import { useEffect, useState } from 'react';
import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { Button } from '@nextui-org/react';
import { Icon } from '@/components/icon';
import { ActionButton } from '@/components/ui/action-button';
import { MdEdit, MdDelete, MdVisibility } from 'react-icons/md';
import { useRouter } from 'next/navigation';
import { ActionType } from './types';
import ActionTypeView from './ActionTypeView';
import { deleteActionType, updateActionType } from '../actions';
import toast from 'react-hot-toast';

interface ActionTypesTableProps {
  initialActionTypes?: ActionType[];
  initialError: string | null;
}

export default function ActionTypesTable({ initialActionTypes = [], initialError }: ActionTypesTableProps) {
  const router = useRouter();
  const [actionTypes, setActionTypes] = useState<ActionType[]>(initialActionTypes);
  const [error, setError] = useState<string | null>(initialError);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedActionType, setSelectedActionType] = useState<ActionType | null>(null);
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  const handleCreate = () => {
    router.push('/messages/system-actions/create');
  };

  const handleEdit = (actionType: ActionType) => {
    router.push(`/messages/system-actions/${actionType.id}/edit`);
  };

  const handleView = (actionType: ActionType) => {
    setSelectedActionType(actionType);
    setIsViewOpen(true);
  };

  const handleDelete = async (id: string) => {
    const { error } = await deleteActionType(id);
    if (error) {
      toast.error(error);
    } else {
      setActionTypes(types => types.filter(type => type.id !== id));
      toast.success('Action type deleted');
    }
  };

  const handleDeleteClick = (actionType: ActionType) => {
    openDeleteDialog(
      actionType.id,
      actionType.name,
      () => handleDelete(actionType.id)
    );
  };

  const handleToggleStatus = async (id: string) => {
    const actionType = actionTypes.find(type => type.id === id);
    if (!actionType) return;
    
    const updated = { ...actionType, is_active: !actionType.is_active };
    const { data, error } = await updateActionType(id, updated);
    
    if (error) {
      toast.error(error);
    } else if (data) {
      setActionTypes(types => types.map(type => 
        type.id === id ? { ...type, is_active: data.is_active, updated_at: data.updated_at } : type
      ));
      toast.success(actionType.is_active ? 'Deactivated' : 'Activated');
    }
  };

  const columns = [
    { uid: 'id', name: 'ID' },
    { uid: 'type', name: 'Type' },
    { uid: 'name', name: 'Name' },
    { uid: 'description', name: 'Description' },
    {
      uid: 'is_active',
      name: 'Status',
      renderCell: (item: ActionType) => (
        <Button
          size="sm"
          color={item.is_active ? 'success' : 'danger'}
          variant="flat"
          onClick={() => handleToggleStatus(item.id)}
        >
          {item.is_active ? 'Active' : 'Inactive'}
        </Button>
      ),
    },
    {
      uid: 'created_at',
      name: 'Created',
      renderCell: (item: ActionType) => new Date(item.created_at).toLocaleString(),
    },
    {
      uid: 'updated_at',
      name: 'Updated',
      renderCell: (item: ActionType) => new Date(item.updated_at).toLocaleString(),
    },
    {
      uid: 'actions',
      name: 'Actions',
      renderCell: (item: ActionType) => (
        <div className="flex gap-2">
          <ActionButton
            variant="ghost"
            size="sm"
            onClick={() => handleView(item)}
          >
            <MdVisibility className="h-4 w-4 text-primary" />
          </ActionButton>
          <ActionButton
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(item)}
          >
            <MdEdit className="h-4 w-4 text-primary" />
          </ActionButton>
          <ActionButton
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(item)}
            className="text-red-500 hover:text-red-700"
          >
            <MdDelete className="h-4 w-4" />
          </ActionButton>
        </div>
      ),
    },
  ];

  if (error) return <div className="p-8 text-center text-danger">{error}</div>;

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button
          color="primary"
          onClick={handleCreate}
          startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
        >
          Create New Action Type
        </Button>
      </div>

      <CustomTable
        columns={columns}
        data={actionTypes}
        title="System Action Types"
      />

      {selectedActionType && (
        <ActionTypeView
          isOpen={isViewOpen}
          onClose={() => setIsViewOpen(false)}
          actionType={selectedActionType}
        />
      )}
      <DeleteConfirmationDialog />
    </div>
  );
}