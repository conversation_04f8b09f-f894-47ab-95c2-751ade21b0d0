import { getActionTypes } from './actions';
import ActionTypesTable from './components/ActionTypesTable';
import { Metadata } from "next";
import { ActionType } from './components/types';

export const metadata: Metadata = {
  title: "System Actions",
  description: "Manage system action types for messages",
  keywords: ["system", "actions", "messages", "admin"],
};

export const dynamic = 'force-dynamic';

export default async function SystemActionsPage() {
  const { data: initialActionTypes, error } = await getActionTypes();
  const actionTypes: ActionType[] = initialActionTypes ?? [];

  return (
    <div className="p-4">
      <ActionTypesTable initialActionTypes={actionTypes} initialError={error} />
    </div>
  );
}