"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import { Group, PaginatedResponse } from "./types";

// List groups
export async function listGroups(
  branchId: string, 
  page: number = 1, 
  order: string = "createdAt",
  sortOrder: string = "desc"
) {
  try {
    const response = await api.get<PaginatedResponse<Group>>(
      `branches/${branchId}/groups`,
      {
        per: 10,
        page,
        order,
        sort: sortOrder,
      }
    );
    return response;
  } catch (error) {
    console.error("Error fetching groups:", error);
    throw error;
  }
}

// Create group
export async function createGroup(data: FormData): Promise<{ success: boolean; group?: Group; error?: string }> {
  try {
    const group = await api.post<Group>("groups", {
      name: data.get("name"),
      details: data.get("details"),
      branchId: data.get("branchId"),
    });
    revalidatePath("/messages/groups");
    return { success: true, group: group || undefined };
  } catch (error) {
    console.error("Error creating group:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create group"
    };
  }
}

// Update group
export async function updateGroup(data: FormData): Promise<{ success: boolean; group?: Group; error?: string }> {
  try {
    const id = data.get("id");
    if (!id || typeof id !== 'string') {
      return {
        success: false,
        error: 'Invalid group ID'
      };
    }
    const group = await api.put<Group>(`groups/${id}`, {
      name: data.get("name"),
      details: data.get("details"),
    });
    revalidatePath("/messages/groups");
    return { success: true, group: group || undefined };
  } catch (error) {
    console.error("Error updating group:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update group"
    };
  }
}

// Delete group
export async function deleteGroup(data: FormData): Promise<{ success: boolean; error?: string }> {
  try {
    const id = data.get("id");
    if (!id || typeof id !== 'string') {
      return {
        success: false,
        error: 'Invalid group ID'
      };
    }
    await api.destroy(id, "groups");
    revalidatePath("/messages/groups");
    return { success: true };
  } catch (error) {
    console.error("Error deleting group:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete group"
    };
  }
}

// List group members
export async function listGroupMembers(groupId: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/v1/groups/${groupId}/members`,
      {
        headers: {
          Authorization: `Bearer ${process.env.API_TOKEN}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch group members");
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching group members:", error);
    throw error;
  }
}

// Add member to group
export async function addGroupMember(groupId: string, userId: string, vendorId: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/v1/groups/${groupId}/members`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.API_TOKEN}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          vendorId,
          active: true,
        }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to add member to group");
    }

    revalidatePath("/messages/groups");
    return await response.json();
  } catch (error) {
    console.error("Error adding member to group:", error);
    throw error;
  }
}

// Remove member from group
export async function removeGroupMember(groupId: string, userId: string) {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/v1/groups/${groupId}/members/${userId}`,
      {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${process.env.API_TOKEN}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to remove member from group");
    }

    revalidatePath("/messages/groups");
    return true;
  } catch (error) {
    console.error("Error removing member from group:", error);
    throw error;
  }
} 