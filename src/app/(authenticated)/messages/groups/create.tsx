"use client";

import { <PERSON><PERSON>, In<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader } from "@nextui-org/react";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { useSession } from "next-auth/react";

interface CreateGroupProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function CreateGroup({ isOpen, onOpenChange }: CreateGroupProps) {
  const { data: session } = useSession();
  const { register, handleSubmit, formState: { errors }, reset } = useForm({
    defaultValues: {
      name: "",
      details: "",
    },
  });

  const onSubmit = async (data: { name: string; details: string }) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);
    if (session?.branch?.id) {
      formData.append("branchId", session.branch.id);
    }
    await fetch("/api/branches/groups", {
      method: "POST",
      body: formData,
    });
    reset();
    onOpenChange(false);
  };

  return (
    <Modal isOpen={isOpen} onOpenChange={onOpenChange}>
      <ModalContent>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalHeader>Create Group</ModalHeader>
          <ModalBody>
            <Input
              label="Name"
              placeholder="Enter group name"
              {...register("name", { required: "Name is required" })}
              errorMessage={errors.name?.message}
            />
            <Input
              label="Details"
              placeholder="Enter group details"
              {...register("details", { required: "Details are required" })}
              errorMessage={errors.details?.message}
            />
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onPress={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button color="primary" type="submit">
              Create
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
} 