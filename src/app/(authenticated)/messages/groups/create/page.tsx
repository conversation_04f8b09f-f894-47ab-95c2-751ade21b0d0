import { Metada<PERSON> } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import CreateGroupForm from "../components/CreateGroupForm";

export const metadata: Metadata = {
  title: "Create Group",
  description: "Create a new message group",
};

export default async function CreateGroupPage() {
  const session = await auth();

  if (!session?.branch?.id) {
    redirect("/messages/groups");
  }

  const createGroup = async (data: FormData) => {
    "use server";
    try {
      const response = await api.post("groups", {
        name: data.get("name"),
        details: data.get("details"),
        branchId: session.branch.id,
      });
      redirect("/messages/groups");
    } catch (error) {
      console.error("Error creating group:", error);
      throw error;
    }
  };

  return (
    <div className="p-6">
      <CreateGroupForm createGroup={createGroup} />
    </div>
  );
} 