"use client";

import { Button, Input } from "@nextui-org/react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";

interface CreateGroupFormProps {
  createGroup: (data: FormData) => Promise<void>;
}

export default function CreateGroupForm({ createGroup }: CreateGroupFormProps) {
  const router = useRouter();
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm({
    defaultValues: {
      name: "",
      details: "",
    },
  });

  const onSubmit = async (data: { name: string; details: string }) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);
    await createGroup(formData);
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Create New Group</h1>
        <p className="text-default-500">Create a new message group to organize your communications.</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Input
          label="Group Name"
          placeholder="Enter group name"
          {...register("name", { required: "Name is required" })}
          errorMessage={errors.name?.message}
        />

        <Input
          label="Group Details"
          placeholder="Enter group description"
          {...register("details", { required: "Details are required" })}
          errorMessage={errors.details?.message}
        />

        <div className="flex gap-4">
          <Button
            color="primary"
            type="submit"
            isLoading={isSubmitting}
          >
            Create Group
          </Button>
          <Button
            variant="light"
            onPress={() => router.back()}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
} 