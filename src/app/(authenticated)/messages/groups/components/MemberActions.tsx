"use client";

import { But<PERSON> } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { useRouter } from "next/navigation";
import { api } from "@/lib/api";

interface MemberActionsProps {
  groupId: string;
  memberId: string;
  onMemberRemoved: () => void;
}

export default function MemberActions({ groupId, memberId, onMemberRemoved }: MemberActionsProps) {
  const router = useRouter();

  const handleRemoveMember = async () => {
    if (confirm("Are you sure you want to remove this member from the group?")) {
      try {
        await api.destroy(`groups/${groupId}/members/${memberId}`);
        onMemberRemoved();
      } catch (error) {
        console.error("Error removing member:", error);
      }
    }
  };

  return (
    <div className="flex justify-center items-center gap-5">
      <Button
        isIconOnly
        variant="light"
        color="danger"
        onPress={handleRemoveMember}
      >
        <Icon name="icon-[heroicons--trash-20-solid]" />
      </Button>
    </div>
  );
} 