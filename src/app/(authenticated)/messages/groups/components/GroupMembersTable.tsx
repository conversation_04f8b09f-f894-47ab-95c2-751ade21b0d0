"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON>ton, Pagination } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { useRouter } from "next/navigation";
import { GroupMember } from "../types";
import { CustomTable } from "@/components/custom-table";
import { toast } from "react-hot-toast";
import Image from "next/image";

interface GroupMembersTableProps {
  groupId: string;
}

interface PaginatedResponse {
  data: GroupMember[];
  meta: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
  };
}

export default function GroupMembersTable({ groupId }: GroupMembersTableProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const loadMembers = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/messages/groups/${groupId}/members?page=${page}&per=10`);
      if (!response.ok) {
        throw new Error('Failed to load members');
      }
      const data: PaginatedResponse = await response.json();
      setMembers(data.data);
      setTotalPages(data.meta.lastPage);
    } catch (error) {
      console.error("Error loading members:", error);
      toast.error("Failed to load members");
    } finally {
      setIsLoading(false);
    }
  }, [groupId, page]);

  useEffect(() => {
    loadMembers();
  }, [loadMembers]);

  const handleRemoveMember = async (memberId: string) => {
    try {
      const response = await fetch(`/api/messages/groups/${groupId}/members/${memberId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to remove member');
      }

      toast.success("Member removed successfully");
      loadMembers();
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end">
        <Button
          color="primary"
          startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          onPress={() => router.push(`/messages/groups/${groupId}/members/add`)}
        >
          Add Member
        </Button>
      </div>

      <CustomTable
        title="Members"
        columns={[
          {
            name: "NAME",
            uid: "name",
            renderCell: (member: GroupMember) => (
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-default-100 flex items-center justify-center">
                  {member.avatarUrl ? (
                    <Image 
                      src={member.avatarUrl} 
                      alt={member.name} 
                      width={32} 
                      height={32} 
                      className="rounded-full"
                    />
                  ) : (
                    <span className="text-sm font-medium">{member.initials}</span>
                  )}
                </div>
                <span>{member.name}</span>
              </div>
            ),
          },
          {
            name: "EMAIL",
            uid: "email",
            renderCell: (member: GroupMember) => member.email,
          },
          {
            name: "PHONE",
            uid: "phone",
            renderCell: (member: GroupMember) => member.phone,
          },
          {
            name: "JOINED",
            uid: "joinedAt",
            renderCell: (member: GroupMember) => new Date(member.meta.pivot_created_at).toLocaleDateString(),
          },
          {
            name: "ACTIONS",
            uid: "actions",
            renderCell: (member: GroupMember) => (
              <Button
                size="sm"
                color="danger"
                variant="light"
                onPress={() => handleRemoveMember(member.id)}
              >
                Remove
              </Button>
            ),
          },
        ]}
        data={members}
      />

      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            total={totalPages}
            page={page}
            onChange={setPage}
          />
        </div>
      )}
    </div>
  );
} 