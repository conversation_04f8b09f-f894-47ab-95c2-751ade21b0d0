"use client";

import { But<PERSON> } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { useRouter } from "next/navigation";
import { Group } from "../types";
import { CustomTable } from "@/components/custom-table";

interface PaginationMeta {
  total: number;
  perPage: number;
  currentPage: number;
  lastPage: number;
  firstPage: number;
  firstPageUrl: string;
  lastPageUrl: string;
  nextPageUrl: string | null;
  previousPageUrl: string | null;
}

interface GroupsTableProps {
  data: Group[];
  meta?: PaginationMeta;
  deleteGroup: (data: FormData) => Promise<void>;
}

export default function GroupsTable({ data, meta, deleteGroup }: GroupsTableProps) {
  const router = useRouter();

  const handleDeleteGroup = async (id: string) => {
    if (confirm("Are you sure you want to delete this group?")) {
      const formData = new FormData();
      formData.append("id", id);
      await deleteGroup(formData);
    }
  };

  const columns = [
    {
      name: "Name",
      uid: "name",
      renderCell: (group: Group) => (
        <div className="flex flex-col">
          <p className="text-bold text-small capitalize">{group.name}</p>
          <p className="text-bold text-tiny capitalize text-default-400">{group.details}</p>
        </div>
      ),
    },
    {
      name: "Created",
      uid: "createdAt",
      sortable: true,
      renderCell: (group: Group) => (
        <p>{new Date(group.createdAt).toLocaleDateString()}</p>
      ),
    },
    {
      name: "Actions",
      uid: "actions",
      align: "center",
      renderCell: (group: Group) => (
        <div className="flex justify-center items-center gap-5">
          <Button
            isIconOnly
            variant="light"
            color="primary"
            onPress={() => router.push(`/messages/groups/${group.id}/edit`)}
          >
            <Icon name="icon-[heroicons--pencil-square-20-solid]" />
          </Button>
          <Button
            isIconOnly
            variant="light"
            color="primary"
            onPress={() => router.push(`/messages/groups/${group.id}/members`)}
          >
            <Icon name="icon-[heroicons--user-group-20-solid]" />
          </Button>
          <Button
            isIconOnly
            variant="light"
            color="danger"
            onPress={() => handleDeleteGroup(group.id)}
          >
            <Icon name="icon-[heroicons--trash-20-solid]" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      title="Groups"
      columns={columns}
      data={data}
      meta={meta}
      action={
        <Button
          color="primary"
          startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          onPress={() => router.push("/messages/groups/create")}
        >
          Create Group
        </Button>
      }
    />
  );
} 