"use client";

import { But<PERSON> } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { Group } from "../types";

interface GroupActionsProps {
  group: Group;
  onDelete: (groupId: string) => void;
}

export default function GroupActions({ group, onDelete }: GroupActionsProps) {
  const router = useRouter();

  return (
    <div className="flex gap-2">
      <Button
        size="sm"
        variant="light"
        onPress={() => router.push(`/messages/groups/${group.id}`)}
      >
        View
      </Button>
      <Button
        size="sm"
        color="danger"
        variant="light"
        onPress={() => onDelete(group.id)}
      >
        Delete
      </Button>
    </div>
  );
} 