"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON><PERSON>, Table, TableBody, TableCell, TableColumn, TableHeader, TableRow, Pa<PERSON>ation, Spinner } from "@nextui-org/react";
import { useState, useEffect } from "react";
import { Icon } from "@/components/icon";
import toast from "react-hot-toast";

interface GroupMember {
  userId: string;
  vendorId: string;
  active: boolean;
  name: string;
  email: string;
  phone: string;
}

interface ManageMembersProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  groupId: string;
  groupName: string;
}

// Mock data for development
const mockMembers: GroupMember[] = [
  {
    userId: "1",
    vendorId: "v1",
    active: true,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1234567890"
  },
  {
    userId: "2",
    vendorId: "v1",
    active: true,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1987654321"
  },
  {
    userId: "3",
    vendorId: "v1",
    active: false,
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+1122334455"
  }
];

export default function ManageMembers({ isOpen, onOpenChange, groupId, groupName }: ManageMembersProps) {
  const [members, setMembers] = useState<GroupMember[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isActionLoading, setIsActionLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [memberToRemove, setMemberToRemove] = useState<GroupMember | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchMembers();
    }
  }, [isOpen, page]);

  const fetchMembers = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement API call
      // For now, using mock data
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      setMembers(mockMembers);
      setTotalPages(Math.ceil(mockMembers.length / 10));
    } catch (error) {
      console.error("Error fetching members:", error);
      toast.error("Failed to fetch members");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (member: GroupMember) => {
    setMemberToRemove(member);
  };

  const confirmRemoveMember = async () => {
    if (!memberToRemove) return;
    
    setIsActionLoading(true);
    try {
      // TODO: Implement remove member API call
      setMembers(members.filter(member => member.userId !== memberToRemove.userId));
      toast.success("Member removed successfully");
    } catch (error) {
      console.error("Error removing member:", error);
      toast.error("Failed to remove member");
    } finally {
      setIsActionLoading(false);
      setMemberToRemove(null);
    }
  };

  const handleAddMember = async () => {
    setIsActionLoading(true);
    try {
      // TODO: Implement add member API call
      toast.success("Member added successfully");
    } catch (error) {
      console.error("Error adding member:", error);
      toast.error("Failed to add member");
    } finally {
      setIsActionLoading(false);
    }
  };

  const filteredMembers = members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.phone.includes(searchQuery)
  );

  return (
    <>
      <Modal 
        isOpen={isOpen} 
        onOpenChange={onOpenChange}
        size="5xl"
        isDismissable={false}
        classNames={{
          base: "max-w-[1200px]",
          body: "py-6",
          header: "border-b-[1px] border-[#292D46]",
          footer: "border-t-[1px] border-[#292D46]",
          closeButton: "hover:bg-white/5 active:bg-white/10",
        }}
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">Manage Members - {groupName}</ModalHeader>
          <ModalBody>
            <div className="flex flex-col gap-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Search members..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  startContent={
                    <Icon
                      name="icon-[heroicons--magnifying-glass-20-solid]"
                      classNames="text-default-400"
                    />
                  }
                  classNames={{
                    input: "text-base",
                    inputWrapper: "h-12",
                  }}
                />
                <Button
                  color="primary"
                  onPress={handleAddMember}
                  isLoading={isActionLoading}
                  className="h-12 px-6"
                >
                  Add Member
                </Button>
              </div>

              {isLoading ? (
                <div className="flex justify-center items-center min-h-[400px]">
                  <Spinner size="lg" />
                </div>
              ) : filteredMembers.length === 0 ? (
                <div className="flex flex-col items-center justify-center min-h-[400px] gap-4">
                  <Icon
                    name="icon-[heroicons--user-group-20-solid]"
                    classNames="text-default-400 text-6xl"
                  />
                  <p className="text-default-400 text-lg">No members found</p>
                </div>
              ) : (
                <>
                  <Table 
                    aria-label="Group members table"
                    classNames={{
                      wrapper: "min-h-[400px]",
                      th: "text-base",
                      td: "text-base",
                    }}
                  >
                    <TableHeader>
                      <TableColumn>NAME</TableColumn>
                      <TableColumn>EMAIL</TableColumn>
                      <TableColumn>PHONE</TableColumn>
                      <TableColumn>STATUS</TableColumn>
                      <TableColumn>ACTIONS</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {filteredMembers.map((member) => (
                        <TableRow key={member.userId}>
                          <TableCell>{member.name}</TableCell>
                          <TableCell>{member.email}</TableCell>
                          <TableCell>{member.phone}</TableCell>
                          <TableCell>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              member.active 
                                ? "bg-success-100 text-success-600" 
                                : "bg-danger-100 text-danger-600"
                            }`}>
                              {member.active ? "Active" : "Inactive"}
                            </span>
                          </TableCell>
                          <TableCell>
                            <Button
                              isIconOnly
                              variant="light"
                              color="danger"
                              onPress={() => handleRemoveMember(member)}
                              isLoading={isActionLoading && memberToRemove?.userId === member.userId}
                            >
                              <Icon
                                name="icon-[heroicons--trash-20-solid]"
                                classNames="text-danger"
                              />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>

                  {totalPages > 1 && (
                    <div className="flex justify-center mt-4">
                      <Pagination
                        total={totalPages}
                        page={page}
                        onChange={setPage}
                        showControls
                      />
                    </div>
                  )}
                </>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={() => onOpenChange(false)}
              className="px-6"
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Modal
        isOpen={!!memberToRemove}
        onOpenChange={() => setMemberToRemove(null)}
        size="sm"
      >
        <ModalContent>
          <ModalHeader>Remove Member</ModalHeader>
          <ModalBody>
            <p>Are you sure you want to remove {memberToRemove?.name} from this group?</p>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onPress={() => setMemberToRemove(null)}
            >
              Cancel
            </Button>
            <Button
              color="danger"
              onPress={confirmRemoveMember}
              isLoading={isActionLoading}
            >
              Remove
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
} 