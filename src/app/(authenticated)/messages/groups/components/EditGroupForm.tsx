"use client";

import { Button, Input } from "@nextui-org/react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { Group } from "../types";

interface EditGroupFormProps {
  group: Group;
  updateGroup: (data: FormData) => Promise<void>;
}

interface FormValues {
  name: string;
  details: string;
}

export default function EditGroupForm({ group, updateGroup }: EditGroupFormProps) {
  const router = useRouter();
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<FormValues>({
    defaultValues: {
      name: group.name,
      details: group.details,
    },
  });

  const onSubmit = async (data: FormValues) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("details", data.details);
    await updateGroup(formData);
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Edit Group</h1>
        <p className="text-default-500">Update group information</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Input
          label="Group Name"
          placeholder="Enter group name"
          {...register("name", { required: "Name is required" })}
          errorMessage={errors.name?.message}
        />

        <Input
          label="Group Details"
          placeholder="Enter group description"
          {...register("details", { required: "Details are required" })}
          errorMessage={errors.details?.message}
        />

        <div className="flex gap-4">
          <Button
            color="primary"
            type="submit"
            isLoading={isSubmitting}
          >
            Save Changes
          </Button>
          <Button
            variant="light"
            onPress={() => router.back()}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
} 