"use client";

import { useState, useEffect, useCallback } from "react";
import { Button, Input, Select, SelectItem } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { useForm, SubmitHandler } from "react-hook-form";
import { toast } from "react-hot-toast";

interface Member {
  id: string;
  name: string;
  phone: string;
  email: string;
}

interface FormValues {
  memberId: string;
}

interface AddMemberFormProps {
  addMember: (data: FormData) => Promise<void>;
  groupId: string;
}

export default function AddMemberForm({ addMember, groupId }: AddMemberFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMembers, setIsLoadingMembers] = useState(true);
  const [allMembers, setAllMembers] = useState<Member[]>([]);
  const [filteredMembers, setFilteredMembers] = useState<Member[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMember, setSelectedMember] = useState<string>("");

  const { register, handleSubmit, formState: { errors }, setValue, watch } = useForm<FormValues>();
  const memberId = watch("memberId");

  const loadMembers = useCallback(async () => {
    try {
      const response = await fetch(`/api/messages/groups/${groupId}/members`);
      if (!response.ok) {
        throw new Error('Failed to load members');
      }
      const data = await response.json();
      if (Array.isArray(data)) {
        setAllMembers(data);
        setFilteredMembers(data);
      } else {
        console.error("Invalid response format:", data);
        setAllMembers([]);
        setFilteredMembers([]);
      }
    } catch (error) {
      console.error("Error loading members:", error);
      setAllMembers([]);
      setFilteredMembers([]);
    } finally {
      setIsLoadingMembers(false);
    }
  }, [groupId]);

  useEffect(() => {
    loadMembers();
  }, [loadMembers]);

  useEffect(() => {
    if (memberId) {
      setSelectedMember(memberId);
    }
  }, [memberId]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setFilteredMembers(allMembers);
      return;
    }

    const filtered = allMembers.filter(member => 
      member.name.toLowerCase().includes(query.toLowerCase()) ||
      member.email.toLowerCase().includes(query.toLowerCase()) ||
      member.phone.includes(query)
    );
    setFilteredMembers(filtered);
  };

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append("memberId", data.memberId);
      
      toast.promise(
        addMember(formData),
        {
          loading: "Adding member...",
          success: "Member added successfully 👌",
          error: "Failed to add member 🤯"
        },
        {
          position: "bottom-center"
        }
      );
    } catch (error) {
      console.error("Error adding member:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <Input
          label="Search Members"
          placeholder="Search by name, email or phone..."
          value={searchQuery}
          onChange={(e) => handleSearch(e.target.value)}
        />
      </div>

      <div>
        <Select
          label="Select Member"
          placeholder="Choose a member"
          isLoading={isLoadingMembers}
          value={selectedMember}
          {...register("memberId", { required: "Member is required" })}
          errorMessage={errors.memberId?.message?.toString()}
        >
          {filteredMembers.map((member: Member) => (
            <SelectItem key={member.id} value={member.id}>
              {member.name} ({member.email})
            </SelectItem>
          ))}
        </Select>
      </div>

      <div className="flex justify-end gap-4">
        <Button
          color="default"
          variant="light"
          onPress={() => router.back()}
        >
          Cancel
        </Button>
        <Button
          color="primary"
          type="submit"
          isLoading={isLoading}
        >
          Add Member
        </Button>
      </div>
    </form>
  );
} 