import { Metadata } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Group } from "./types";
import GroupsTable from "./components/GroupsTable";

export const metadata: Metadata = {
  title: "Groups",
  description: "Manage message groups",
};

export default async function GroupsPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();
  if (!session?.branch?.id) {
    redirect("/messages/groups");
  }

  const groups = await api.get<{ data: Group[]; meta: any }>(
    `branches/${session.branch.id}/groups`,
    { per: 10, ...searchParams }
  );

  const deleteGroup = async (data: FormData) => {
    "use server";
    try {
      const id = data.get("id");
      if (!id || typeof id !== "string") {
        throw new Error("Invalid group ID");
      }
      await api.destroy(`groups/${id}`);
      redirect("/messages/groups");
    } catch (error) {
      console.error("Error deleting group:", error);
      throw error;
    }
  };

  if (!groups) {
    return (
      <div className="p-6">
        <p>No groups found</p>
      </div>
    );
  }

  // Transform meta data to match PaginationMeta interface
  const transformedMeta = {
    ...groups.meta,
    firstPage: 1,
    firstPageUrl: `/messages/groups?page=1&per=${groups.meta.perPage}`,
    lastPageUrl: `/messages/groups?page=${groups.meta.lastPage}&per=${groups.meta.perPage}`,
    nextPageUrl: groups.meta.currentPage < groups.meta.lastPage 
      ? `/messages/groups?page=${groups.meta.currentPage + 1}&per=${groups.meta.perPage}`
      : null,
    previousPageUrl: groups.meta.currentPage > 1
      ? `/messages/groups?page=${groups.meta.currentPage - 1}&per=${groups.meta.perPage}`
      : null,
  };

  return (
    <div className="p-6">
      <GroupsTable data={groups.data} meta={transformedMeta} deleteGroup={deleteGroup} />
    </div>
  );
} 