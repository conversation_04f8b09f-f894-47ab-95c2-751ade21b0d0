import { Metadata } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Group } from "../../types";
import GroupMembersTable from "../../components/GroupMembersTable";

export const metadata: Metadata = {
  title: "Group Members",
  description: "Manage group members",
};

export default async function GroupMembersPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await auth();
  if (!session?.branch?.id) {
    redirect("/messages/groups");
  }

  const group = await api.get<Group>(`groups/${params.id}`);
  if (!group) {
    redirect("/messages/groups");
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Group Members</h1>
        <p className="text-default-500">Manage members for group: {group.name}</p>
      </div>

      <GroupMembersTable groupId={params.id} />
    </div>
  );
} 