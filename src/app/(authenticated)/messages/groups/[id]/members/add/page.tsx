import { Metadata } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import { Group } from "../../../types";
import AddMemberForm from "../../../components/AddMemberForm";

export const metadata: Metadata = {
  title: "Add Group Member",
  description: "Add a new member to the group",
};

export default async function AddMemberPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await auth();
  if (!session?.branch?.id) {
    redirect("/messages/groups");
  }

  const group = await api.get<Group>(`groups/${params.id}`);
  if (!group) {
    redirect("/messages/groups");
  }

  const addMember = async (data: FormData) => {
    "use server";
    
    const memberId = data.get("memberId");
    if (!memberId || typeof memberId !== "string") {
      throw new Error("Member ID is required");
    }

    try {
      await api.post(`groups/${params.id}/members`, {
        userId: memberId
      });

      redirect(`/messages/groups/${params.id}/members`);
    } catch (error) {
      console.error("Error adding member:", error);
      throw error;
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Add Member</h1>
        <p className="text-default-500">Add a new member to group: {group.name}</p>
      </div>

      <AddMemberForm addMember={addMember} groupId={params.id} />
    </div>
  );
} 