"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Di<PERSON>r, Input } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "react-hot-toast";
import AsyncSelect from "react-select/async";
import { ArrowLeftIcon } from "@radix-ui/react-icons";

interface User {
  id: string;
  name: string;
  email: string;
}

interface SelectedUser {
  value: string;
  label: string;
}

export default function AddMembersPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [selectedUsers, setSelectedUsers] = useState<SelectedUser[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const loadUsers = async (inputValue: string) => {
    try {
      const response = await fetch(`/api/users?q=${inputValue}`);
      const data = await response.json();
      return data.map((user: User) => ({
        value: user.id,
        label: `${user.name} (${user.email})`
      }));
    } catch (error) {
      console.error('Error loading users:', error);
      return [];
    }
  };

  const handleSubmit = async () => {
    if (selectedUsers.length === 0) {
      toast.error('Please select at least one user');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/groups/${params.id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          members: selectedUsers.map(user => user.value)
        }),
      });

      if (!response.ok) throw new Error('Failed to add members');

      toast.success('Members added successfully');
      router.push(`/messages/groups/${params.id}`);
    } catch (error) {
      console.error('Error adding members:', error);
      toast.error('Failed to add members');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="flex items-center gap-4 mb-6">
        <Button
          isIconOnly
          variant="light"
          onPress={() => router.back()}
        >
          <ArrowLeftIcon />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">Add Members</h1>
          <p className="text-default-500">Add new members to the group</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <p className="text-md">Select Members</p>
        </CardHeader>
        <Divider/>
        <CardBody>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Search Users
              </label>
              <AsyncSelect
                isMulti
                cacheOptions
                defaultOptions
                value={selectedUsers}
                onChange={(newValue) => setSelectedUsers(newValue as SelectedUser[])}
                loadOptions={loadUsers}
                placeholder="Search users by name or email..."
                className="react-select-container"
                classNamePrefix="react-select"
              />
              <p className="text-sm text-default-500 mt-2">
                {selectedUsers.length} users selected
              </p>
            </div>

            <div className="flex gap-4 justify-end">
              <Button
                color="default"
                variant="light"
                onPress={() => router.back()}
              >
                Cancel
              </Button>
              <Button
                color="primary"
                onPress={handleSubmit}
                isLoading={isSubmitting}
              >
                Add Members
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
} 