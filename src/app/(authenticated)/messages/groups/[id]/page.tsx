"use client";

import { <PERSON><PERSON>, <PERSON>, CardBody, CardHeader, Chip, Divider, Input, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from "@nextui-org/react";
import { useRouter } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import { toast } from "react-hot-toast";
import { Pencil1Icon, TrashIcon, PlusIcon } from "@radix-ui/react-icons";

interface GroupMember {
  id: string;
  name: string;
  email: string;
  role: string;
  joinedAt: string;
}

interface GroupDetails {
  id: string;
  name: string;
  details: string;
  createdAt: string;
  memberCount: number;
  members: GroupMember[];
}

export default function GroupDetailsPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [group, setGroup] = useState<GroupDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  const fetchGroupDetails = useCallback(async () => {
    try {
      const response = await fetch(`/api/groups/${params.id}`);
      if (!response.ok) throw new Error('Failed to fetch group details');
      const data = await response.json();
      setGroup(data);
    } catch (error) {
      console.error('Error fetching group details:', error);
      toast.error('Failed to load group details');
    } finally {
      setIsLoading(false);
    }
  }, [params.id]);

  useEffect(() => {
    fetchGroupDetails();
  }, [fetchGroupDetails]);

  const handleDeleteGroup = async () => {
    if (!confirm('Are you sure you want to delete this group?')) return;

    try {
      const response = await fetch(`/api/groups/${params.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete group');

      toast.success('Group deleted successfully');
      router.push('/messages/groups');
    } catch (error) {
      console.error('Error deleting group:', error);
      toast.error('Failed to delete group');
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!confirm('Are you sure you want to remove this member?')) return;

    try {
      const response = await fetch(`/api/groups/${params.id}/members/${memberId}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to remove member');

      toast.success('Member removed successfully');
      fetchGroupDetails(); // Refresh the group details
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove member');
    }
  };

  const filteredMembers = group?.members.filter(member =>
    member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-default-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-default-200 rounded w-1/2 mb-8"></div>
          <div className="h-64 bg-default-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Group not found</h2>
          <Button
            color="primary"
            className="mt-4"
            onPress={() => router.push('/messages/groups')}
          >
            Back to Groups
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-2xl font-bold">{group.name}</h1>
          <p className="text-default-500 mt-1">{group.details}</p>
        </div>
        <div className="flex gap-2">
          <Button
            color="primary"
            variant="light"
            startContent={<Pencil1Icon />}
            onPress={() => router.push(`/messages/groups/${params.id}/edit`)}
          >
            Edit Group
          </Button>
          <Button
            color="danger"
            variant="light"
            startContent={<TrashIcon />}
            onPress={handleDeleteGroup}
          >
            Delete Group
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader className="flex gap-3">
          <div className="flex flex-col">
            <p className="text-md">Group Information</p>
            <p className="text-small text-default-500">Created on {new Date(group.createdAt).toLocaleDateString()}</p>
          </div>
        </CardHeader>
        <Divider/>
        <CardBody>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-default-500">Total Members</p>
              <p className="text-xl font-semibold">{group.memberCount}</p>
            </div>
            <div>
              <p className="text-sm text-default-500">Group ID</p>
              <p className="text-sm font-mono">{group.id}</p>
            </div>
          </div>
        </CardBody>
      </Card>

      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Members</h2>
        <Button
          color="primary"
          startContent={<PlusIcon />}
          onPress={() => router.push(`/messages/groups/${params.id}/add-members`)}
        >
          Add Members
        </Button>
      </div>

      <div className="mb-4">
        <Input
          placeholder="Search members..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-xs"
        />
      </div>

      <Table aria-label="Group members table">
        <TableHeader>
          <TableColumn>NAME</TableColumn>
          <TableColumn>EMAIL</TableColumn>
          <TableColumn>ROLE</TableColumn>
          <TableColumn>JOINED</TableColumn>
          <TableColumn>ACTIONS</TableColumn>
        </TableHeader>
        <TableBody
          items={filteredMembers}
          emptyContent="No members found"
        >
          {(member) => (
            <TableRow key={member.id}>
              <TableCell>{member.name}</TableCell>
              <TableCell>{member.email}</TableCell>
              <TableCell>
                <Chip size="sm" variant="flat">
                  {member.role}
                </Chip>
              </TableCell>
              <TableCell>{new Date(member.joinedAt).toLocaleDateString()}</TableCell>
              <TableCell>
                <Button
                  size="sm"
                  color="danger"
                  variant="light"
                  onPress={() => handleRemoveMember(member.id)}
                >
                  Remove
                </Button>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
} 