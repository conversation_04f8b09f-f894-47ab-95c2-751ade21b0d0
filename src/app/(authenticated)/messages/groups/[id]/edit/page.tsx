import { <PERSON>ada<PERSON> } from "next";
import { api } from "@/lib/api";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import EditGroupForm from "../../components/EditGroupForm";
import { Group } from "../../types";

export const metadata: Metadata = {
  title: "Edit Group",
  description: "Edit message group details",
};

export default async function EditGroupPage({
  params,
}: {
  params: { id: string };
}) {
  const session = await auth();

  if (!session?.branch?.id) {
    redirect("/messages/groups");
  }

  const group = await api.get<Group>(`groups/${params.id}`);

  if (!group) {
    redirect("/messages/groups");
  }

  const updateGroup = async (data: FormData) => {
    "use server";
    try {
      const response = await api.put(`groups/${params.id}`, {
        name: data.get("name"),
        details: data.get("details"),
      });
      redirect("/messages/groups");
    } catch (error) {
      console.error("Error updating group:", error);
      throw error;
    }
  };

  return (
    <div className="p-6">
      <EditGroupForm group={group} updateGroup={updateGroup} />
    </div>
  );
} 