"use client";

import { <PERSON><PERSON>, In<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON> } from "@nextui-org/react";
import { Group } from "./types";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { Icon } from "@/components/icon";

interface EditGroupProps {
  defaultValues: Group;
  updateRecord: (data: FormData) => Promise<void>;
}

export default function EditGroup({ defaultValues, updateRecord }: EditGroupProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      name: defaultValues.name,
      details: defaultValues.details,
    },
  });

  const onSubmit = async (data: { name: string; details: string }) => {
    const formData = new FormData();
    formData.append("id", defaultValues.id);
    formData.append("name", data.name);
    formData.append("details", data.details);
    await updateRecord(formData);
    setIsOpen(false);
  };

  return (
    <>
      <Button
        isIconOnly
        variant="light"
        color="primary"
        onPress={() => setIsOpen(true)}
      >
        <Icon
          name="icon-[heroicons--pencil-square-20-solid]"
          classNames="text-primary"
        />
      </Button>

      <Modal isOpen={isOpen} onOpenChange={setIsOpen}>
        <ModalContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            <ModalHeader>Edit Group</ModalHeader>
            <ModalBody>
              <Input
                label="Name"
                placeholder="Enter group name"
                {...register("name", { required: "Name is required" })}
                errorMessage={errors.name?.message}
              />
              <Input
                label="Details"
                placeholder="Enter group details"
                {...register("details", { required: "Details are required" })}
                errorMessage={errors.details?.message}
              />
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button color="primary" type="submit">
                Save
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>
    </>
  );
} 