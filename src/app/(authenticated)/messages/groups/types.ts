export interface Group {
  id: string;
  name: string;
  details: string;
  vendorId: string | null;
  branchId: string;
  image: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface GroupMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  name: string;
  avatarUrl: string;
  initials: string;
  meta: {
    pivot_group_id: string;
    pivot_user_id: string;
    pivot_created_at: string;
    pivot_updated_at: string;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
} 