'use client';

import { useState, useRef, useEffect } from 'react';
import { Textarea, Card, Chip, Button, Tabs, Tab, Select, SelectItem } from '@nextui-org/react';
import { Icon } from '@/components/icon';

interface EnhancedContentEditorProps {
  value: string;
  onChange: (value: string) => void;
  onVariableInsert?: (variable: string) => void;
  placeholder?: string;
  label?: string;
  description?: string;
  isRequired?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  maxLength?: number;
  contentType?: 'plain' | 'html' | 'markdown';
  onContentTypeChange?: (type: 'plain' | 'html' | 'markdown') => void;
}

interface VariableMatch {
  variable: string;
  start: number;
  end: number;
  isValid: boolean;
}

export default function EnhancedContentEditor({
  value,
  onChange,
  onVariableInsert,
  placeholder = "Enter your template content...",
  label = "Template Content",
  description,
  isRequired = false,
  isInvalid = false,
  errorMessage,
  maxLength = 10000,
  contentType = 'plain',
  onContentTypeChange
}: EnhancedContentEditorProps) {
  const [selectedTab, setSelectedTab] = useState('editor');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [cursorPosition, setCursorPosition] = useState(0);

  // Validate template variables
  const validateVariables = (content: string): VariableMatch[] => {
    const variablePattern = /{{(\w+(?:\.\w+)*)}}/g;
    const matches: VariableMatch[] = [];
    let match;

    while ((match = variablePattern.exec(content)) !== null) {
      const variable = match[0];
      const variableName = match[1];
      
      // Basic validation - check if variable name is valid
      const isValid = /^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(variableName);
      
      matches.push({
        variable,
        start: match.index,
        end: match.index + variable.length,
        isValid
      });
    }

    return matches;
  };

  // Get validation results
  const variableMatches = validateVariables(value);
  const validVariables = variableMatches.filter(m => m.isValid);
  const invalidVariables = variableMatches.filter(m => !m.isValid);

  // Handle cursor position tracking
  const handleSelectionChange = () => {
    if (textareaRef.current) {
      setCursorPosition(textareaRef.current.selectionStart);
    }
  };

  // Insert variable at cursor position
  const insertVariable = (variable: string) => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    
    const newValue = value.substring(0, start) + variable + value.substring(end);
    onChange(newValue);
    
    // Set cursor position after inserted variable
    setTimeout(() => {
      if (textarea) {
        const newPosition = start + variable.length;
        textarea.setSelectionRange(newPosition, newPosition);
        textarea.focus();
        setCursorPosition(newPosition);
      }
    }, 0);

    onVariableInsert?.(variable);
  };

  // Handle content change
  const handleContentChange = (newValue: string) => {
    onChange(newValue);
  };

  // Get content statistics
  const getContentStats = () => {
    const wordCount = value.trim() ? value.trim().split(/\s+/).length : 0;
    const charCount = value.length;
    const lineCount = value.split('\n').length;
    
    return { wordCount, charCount, lineCount };
  };

  const stats = getContentStats();

  // Render content with syntax highlighting (simplified version)
  const renderHighlightedContent = () => {
    if (!value) return <span className="text-default-400">Enter content to see preview...</span>;

    let highlightedContent = value;
    
    // Highlight valid variables in green
    validVariables.forEach(match => {
      highlightedContent = highlightedContent.replace(
        match.variable,
        `<span class="bg-success-100 text-success-800 px-1 rounded">${match.variable}</span>`
      );
    });

    // Highlight invalid variables in red
    invalidVariables.forEach(match => {
      highlightedContent = highlightedContent.replace(
        match.variable,
        `<span class="bg-danger-100 text-danger-800 px-1 rounded">${match.variable}</span>`
      );
    });

    return (
      <div 
        className="whitespace-pre-wrap font-mono text-sm p-4 bg-default-50 rounded-lg min-h-[200px]"
        dangerouslySetInnerHTML={{ __html: highlightedContent }}
      />
    );
  };

  // Get content type info
  const getContentTypeInfo = () => {
    switch (contentType) {
      case 'html':
        return {
          label: 'HTML',
          description: 'Rich text with HTML formatting',
          placeholder: 'Enter HTML content with {{variables}}...',
          icon: 'icon-[heroicons--code-bracket-20-solid]'
        };
      case 'markdown':
        return {
          label: 'Markdown',
          description: 'Markdown formatted content',
          placeholder: 'Enter Markdown content with {{variables}}...',
          icon: 'icon-[heroicons--document-text-20-solid]'
        };
      default:
        return {
          label: 'Plain Text',
          description: 'Simple text content',
          placeholder: 'Enter plain text content with {{variables}}...',
          icon: 'icon-[heroicons--document-20-solid]'
        };
    }
  };

  const contentTypeInfo = getContentTypeInfo();

  // Enhanced description with variable info
  const enhancedDescription = (
    <div className="space-y-2">
      {description && <p className="text-sm text-default-600">{description}</p>}
      <div className="flex items-center gap-4 text-xs text-default-500">
        <span>Use {`{{variableName}}`} for dynamic content</span>
        <span>•</span>
        <span>{stats.charCount}/{maxLength} characters</span>
        <span>•</span>
        <span>{stats.wordCount} words</span>
        <span>•</span>
        <span>{validVariables.length} variables</span>
        <span>•</span>
        <span>{contentTypeInfo.description}</span>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Content Type Selector */}
      {onContentTypeChange && (
        <div className="flex items-center gap-4">
          <Select
            label="Content Type"
            selectedKeys={[contentType]}
            onSelectionChange={(keys) => onContentTypeChange(Array.from(keys)[0] as 'plain' | 'html' | 'markdown')}
            className="max-w-xs"
            variant="bordered"
            size="sm"
          >
            <SelectItem key="plain" startContent={<Icon name="icon-[heroicons--document-20-solid]" classNames="h-4 w-4" />}>
              Plain Text
            </SelectItem>
            <SelectItem key="html" startContent={<Icon name="icon-[heroicons--code-bracket-20-solid]" classNames="h-4 w-4" />}>
              HTML
            </SelectItem>
            <SelectItem key="markdown" startContent={<Icon name="icon-[heroicons--document-text-20-solid]" classNames="h-4 w-4" />}>
              Markdown
            </SelectItem>
          </Select>
          <div className="flex items-center gap-2 text-sm text-default-600">
            <Icon name={contentTypeInfo.icon} classNames="h-4 w-4" />
            <span>{contentTypeInfo.description}</span>
          </div>
        </div>
      )}

      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
        variant="underlined"
      >
        <Tab key="editor" title="Editor">
          <div className="space-y-4">
            <Textarea
              ref={textareaRef}
              label={label}
              placeholder={contentTypeInfo.placeholder}
              value={value}
              onChange={(e) => handleContentChange(e.target.value)}
              onSelect={handleSelectionChange}
              onKeyUp={handleSelectionChange}
              onClick={handleSelectionChange}
              description={enhancedDescription}
              isRequired={isRequired}
              isInvalid={isInvalid || invalidVariables.length > 0}
              errorMessage={
                invalidVariables.length > 0
                  ? `Invalid variables found: ${invalidVariables.map(v => v.variable).join(', ')}`
                  : errorMessage
              }
              variant="bordered"
              minRows={8}
              maxRows={20}
              maxLength={maxLength}
              classNames={{
                input: contentType === 'plain' ? "font-sans" : "font-mono",
              }}
            />

            {/* Variable Status */}
            {variableMatches.length > 0 && (
              <Card className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm font-medium text-default-700">Variables in Content</span>
                  <div className="flex items-center gap-2">
                    {validVariables.length > 0 && (
                      <Chip size="sm" color="success" variant="flat">
                        {validVariables.length} valid
                      </Chip>
                    )}
                    {invalidVariables.length > 0 && (
                      <Chip size="sm" color="danger" variant="flat">
                        {invalidVariables.length} invalid
                      </Chip>
                    )}
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-2">
                  {variableMatches.map((match, index) => (
                    <Chip
                      key={index}
                      size="sm"
                      color={match.isValid ? "success" : "danger"}
                      variant="flat"
                    >
                      {match.variable}
                    </Chip>
                  ))}
                </div>
              </Card>
            )}
          </div>
        </Tab>

        <Tab key="preview" title="Preview">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-default-700">Content Preview</span>
              <div className="flex items-center gap-2 text-xs text-default-500">
                <Icon name="icon-[heroicons--eye-20-solid]" classNames="h-4 w-4" />
                <span>Variables highlighted</span>
              </div>
            </div>
            
            {renderHighlightedContent()}

            {invalidVariables.length > 0 && (
              <Card className="p-4 border-danger-200 bg-danger-50">
                <div className="flex items-center gap-2 mb-2">
                  <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-4 w-4 text-danger" />
                  <span className="text-sm font-medium text-danger">Variable Syntax Issues</span>
                </div>
                <ul className="text-sm text-danger-700 space-y-1">
                  {invalidVariables.map((match, index) => (
                    <li key={index}>
                      <code className="bg-danger-100 px-1 rounded">{match.variable}</code> - Invalid variable name format
                    </li>
                  ))}
                </ul>
                <p className="text-xs text-danger-600 mt-2">
                  Variable names should contain only letters, numbers, underscores, and dots.
                </p>
              </Card>
            )}
          </div>
        </Tab>
      </Tabs>

      {/* Quick Actions */}
      <div className="flex items-center gap-2">
        <Button
          size="sm"
          variant="flat"
          onClick={() => insertVariable('{{customerName}}')}
          startContent={<Icon name="icon-[heroicons--user-20-solid]" classNames="h-3 w-3" />}
        >
          Customer Name
        </Button>
        <Button
          size="sm"
          variant="flat"
          onClick={() => insertVariable('{{orderNumber}}')}
          startContent={<Icon name="icon-[heroicons--hashtag-20-solid]" classNames="h-3 w-3" />}
        >
          Order Number
        </Button>
        <Button
          size="sm"
          variant="flat"
          onClick={() => insertVariable('{{serviceName}}')}
          startContent={<Icon name="icon-[heroicons--wrench-screwdriver-20-solid]" classNames="h-3 w-3" />}
        >
          Service Name
        </Button>
      </div>
    </div>
  );
}
