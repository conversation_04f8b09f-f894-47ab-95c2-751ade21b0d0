'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, Card, Input, Divider } from '@nextui-org/react';
import AsyncSelect from 'react-select/async';
import { Icon } from '@/components/icon';
import toast from 'react-hot-toast';

interface ObjectLink {
  id: string;
  objectType: 'product' | 'vendor' | 'service' | 'branch';
  objectId: string;
  objectName: string;
  displayText: string;
  route: string;
}

interface ObjectSelectorProps {
  objectLinks: ObjectLink[];
  onObjectLinksChange: (links: ObjectLink[]) => void;
  supportedTypes: ('product' | 'vendor' | 'service' | 'branch')[];
  isAdmin?: boolean;
  branchId?: string;
  vendorId?: string;
}

interface SelectOption {
  value: string;
  label: string;
  data?: any;
}

const OBJECT_TYPE_CONFIG = {
  product: {
    label: 'Products',
    icon: 'icon-[heroicons--cube-20-solid]',
    defaultDisplayText: 'View Product Details',
    searchEndpoint: '/api/products',
    routePrefix: 'products'
  },
  vendor: {
    label: 'Vendors',
    icon: 'icon-[heroicons--building-storefront-20-solid]',
    defaultDisplayText: 'Visit Vendor Profile',
    searchEndpoint: '/api/vendors',
    routePrefix: 'vendors'
  },
  service: {
    label: 'Services',
    icon: 'icon-[heroicons--wrench-screwdriver-20-solid]',
    defaultDisplayText: 'Book This Service',
    searchEndpoint: '/api/services',
    routePrefix: 'services'
  },
  branch: {
    label: 'Branches',
    icon: 'icon-[heroicons--map-pin-20-solid]',
    defaultDisplayText: 'View Branch Details',
    searchEndpoint: '/api/branches',
    routePrefix: 'branches'
  }
};

export default function ObjectSelector({
  objectLinks,
  onObjectLinksChange,
  supportedTypes,
  isAdmin = false,
  branchId,
  vendorId
}: ObjectSelectorProps) {
  const [selectedObjects, setSelectedObjects] = useState<Record<string, SelectOption | null>>({});
  const [customDisplayTexts, setCustomDisplayTexts] = useState<Record<string, string>>({});

  // Filter supported types based on user role - hide vendor if not admin
  const filteredSupportedTypes = supportedTypes.filter(type => {
    if (type === 'vendor' && !isAdmin) {
      return false;
    }
    return true;
  });

  // Load options for async select
  const loadOptions = async (inputValue: string, objectType: keyof typeof OBJECT_TYPE_CONFIG) => {
    if (inputValue.length < 2) return [];

    try {
      const config = OBJECT_TYPE_CONFIG[objectType];

      // Use branch products endpoint for products when branchId is available
      // Use vendor services endpoint for services when vendorId is available
      // Use vendor branches endpoint for branches when vendorId is available
      let endpoint = config.searchEndpoint;
      if (objectType === 'product' && branchId) {
        endpoint = `/api/branches/${branchId}/products`;
      } else if (objectType === 'service' && vendorId) {
        endpoint = `/api/vendors/${vendorId}/services`;
      } else if (objectType === 'branch' && vendorId) {
        endpoint = `/api/vendors/${vendorId}/branches`;
      }

      const response = await fetch(`${endpoint}?s=${encodeURIComponent(inputValue)}`);

      if (!response.ok) {
        throw new Error(`Failed to search ${config.label.toLowerCase()}`);
      }

      const data = await response.json();

      // Handle different response formats
      const items = Array.isArray(data) ? data : (data.data || []);

      return items.map((item: any) => ({
        value: item.id,
        label: item.name || item.title,
        data: item
      }));
    } catch (error) {
      console.error(`Error loading ${objectType}:`, error);
      toast.error(`Failed to search ${OBJECT_TYPE_CONFIG[objectType].label.toLowerCase()}`);
      return [];
    }
  };

  const handleObjectSelect = (objectType: keyof typeof OBJECT_TYPE_CONFIG, option: SelectOption | null) => {
    setSelectedObjects(prev => ({
      ...prev,
      [objectType]: option
    }));
  };

  const handleDisplayTextChange = (objectType: string, value: string) => {
    setCustomDisplayTexts(prev => ({
      ...prev,
      [objectType]: value
    }));
  };

  const addObjectLink = (objectType: keyof typeof OBJECT_TYPE_CONFIG) => {
    const selectedObject = selectedObjects[objectType];
    if (!selectedObject) {
      toast.error('Please select an object first');
      return;
    }

    const config = OBJECT_TYPE_CONFIG[objectType];
    const displayText = (customDisplayTexts[objectType] || config.defaultDisplayText).trim();

    // Validation
    if (!displayText) {
      toast.error('Display text cannot be empty');
      return;
    }

    if (displayText.length > 100) {
      toast.error('Display text must be 100 characters or less');
      return;
    }

    // Check for duplicate links
    const isDuplicate = objectLinks.some(link =>
      link.objectType === objectType && link.objectId === selectedObject.value
    );

    if (isDuplicate) {
      toast.error(`This ${config.label.slice(0, -1).toLowerCase()} is already linked`);
      return;
    }

    const newLink: ObjectLink = {
      id: crypto.randomUUID(),
      objectType,
      objectId: selectedObject.value,
      objectName: selectedObject.label,
      displayText,
      route: `${config.routePrefix}/${selectedObject.value}`
    };

    onObjectLinksChange([...objectLinks, newLink]);

    // Reset form
    setSelectedObjects(prev => ({ ...prev, [objectType]: null }));
    setCustomDisplayTexts(prev => ({ ...prev, [objectType]: '' }));

    toast.success(`${config.label.slice(0, -1)} link added successfully`);
  };

  const removeObjectLink = (linkId: string) => {
    onObjectLinksChange(objectLinks.filter(link => link.id !== linkId));
    toast.success('Object link removed');
  };

  const updateDisplayText = (linkId: string, newDisplayText: string) => {
    const trimmedText = newDisplayText.trim();

    // Validation
    if (trimmedText.length > 100) {
      toast.error('Display text must be 100 characters or less');
      return;
    }

    onObjectLinksChange(
      objectLinks.map(link =>
        link.id === linkId ? { ...link, displayText: trimmedText || 'View Details' } : link
      )
    );
  };

  const getLinksForType = (objectType: keyof typeof OBJECT_TYPE_CONFIG) => {
    return objectLinks.filter(link => link.objectType === objectType);
  };

  return (
    <div className="space-y-6">
      {filteredSupportedTypes.map((objectType) => {
        const config = OBJECT_TYPE_CONFIG[objectType];
        const typeLinks = getLinksForType(objectType);
        const selectedObject = selectedObjects[objectType];
        const customDisplayText = customDisplayTexts[objectType] || '';

        return (
          <div key={objectType} className="space-y-4">
            <div className="flex items-center gap-2">
              <Icon name={config.icon} classNames="h-5 w-5 text-primary" />
              <h4 className="text-md font-semibold text-default-900">{config.label}</h4>
            </div>

            {/* Add new object link */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 p-4 bg-default-50 rounded-lg">
              <div>
                <label className="block text-sm font-medium text-default-700 mb-2">
                  Search {config.label}
                </label>
                <AsyncSelect
                  cacheOptions
                  loadOptions={(inputValue) => loadOptions(inputValue, objectType)}
                  onChange={(option) => handleObjectSelect(objectType, option)}
                  value={selectedObject}
                  placeholder={`Search ${config.label.toLowerCase()}...`}
                  noOptionsMessage={({ inputValue }) =>
                    inputValue.length < 2 ? 'Type to search...' : 'No results found'
                  }
                  className="react-select-container"
                  classNamePrefix="react-select"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-default-700 mb-2">
                  Button Text
                </label>
                <Input
                  placeholder={config.defaultDisplayText}
                  value={customDisplayText}
                  onChange={(e) => handleDisplayTextChange(objectType, e.target.value)}
                  variant="bordered"
                  maxLength={100}
                  description={`${customDisplayText.length}/100 characters`}
                  isInvalid={customDisplayText.length > 100}
                  errorMessage={customDisplayText.length > 100 ? "Text too long" : ""}
                />
              </div>

              <div className="flex items-end">
                <Button
                  color="primary"
                  onClick={() => addObjectLink(objectType)}
                  isDisabled={!selectedObject}
                  startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
                >
                  Add Link
                </Button>
              </div>
            </div>

            {/* Display existing links */}
            {typeLinks.length > 0 && (
              <div className="space-y-2">
                {typeLinks.map((link) => (
                  <Card key={link.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="font-medium text-default-900">{link.objectName}</span>
                          <span className="text-xs text-default-500 bg-default-100 px-2 py-1 rounded">
                            {link.route}
                          </span>
                        </div>
                        <Input
                          size="sm"
                          value={link.displayText}
                          onChange={(e) => updateDisplayText(link.id, e.target.value)}
                          variant="bordered"
                          label="Button Text"
                          className="max-w-xs"
                          maxLength={100}
                          isInvalid={link.displayText.length > 100}
                          errorMessage={link.displayText.length > 100 ? "Text too long" : ""}
                        />
                      </div>
                      <Button
                        color="danger"
                        variant="light"
                        size="sm"
                        onClick={() => removeObjectLink(link.id)}
                        startContent={<Icon name="icon-[heroicons--trash-20-solid]" classNames="h-4 w-4" />}
                      >
                        Remove
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {objectType !== supportedTypes[supportedTypes.length - 1] && <Divider />}
          </div>
        );
      })}

      {objectLinks.length === 0 && (
        <div className="text-center py-8 text-default-500">
          <Icon name="icon-[heroicons--link-20-solid]" classNames="h-12 w-12 mx-auto mb-4 text-default-300" />
          <p>No object links added yet</p>
          <p className="text-sm">Search and select objects above to create links</p>
        </div>
      )}
    </div>
  );
}
