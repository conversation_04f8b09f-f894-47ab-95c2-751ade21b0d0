'use client';

import { useState } from 'react';
import { Card } from '@nextui-org/react';
import ObjectSelector from './ObjectSelector';

interface ObjectLink {
  id: string;
  objectType: 'product' | 'vendor' | 'service' | 'branch';
  objectId: string;
  objectName: string;
  displayText: string;
  route: string;
}

interface TemplateActionsProps {
  templateId?: string;
  initialActions?: ObjectLink[];
  onActionsChange?: (actions: ObjectLink[]) => void;
  isAdmin?: boolean;
  branchId?: string;
  vendorId?: string;
}

export default function TemplateActions({
  templateId,
  initialActions = [],
  onActionsChange,
  isAdmin = false,
  branchId,
  vendorId
}: TemplateActionsProps) {
  const [objectLinks, setObjectLinks] = useState<ObjectLink[]>(initialActions);

  const handleObjectLinksChange = (newLinks: ObjectLink[]) => {
    setObjectLinks(newLinks);
    onActionsChange?.(newLinks);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-default-900 mb-4">
          Link to Objects (Optional)
        </h3>
        <p className="text-sm text-default-600 mb-6">
          Link this template to specific objects. Messages using this template will inherit these links.
        </p>
      </div>

      <Card className="p-6">
        <ObjectSelector
          objectLinks={objectLinks}
          onObjectLinksChange={handleObjectLinksChange}
          supportedTypes={['product', 'vendor', 'service', 'branch']}
          isAdmin={isAdmin}
          branchId={branchId}
          vendorId={vendorId}
        />
      </Card>
    </div>
  );
}