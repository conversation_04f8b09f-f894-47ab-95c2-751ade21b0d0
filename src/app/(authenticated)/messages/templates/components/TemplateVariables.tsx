'use client';

import { useState } from 'react';
import { Card, Button, Input, Chip, Divider, Accordion, AccordionItem } from '@nextui-org/react';
import { Icon } from '@/components/icon';
import toast from 'react-hot-toast';

interface TemplateVariable {
  name: string;
  variable: string;
  description: string;
  example: string;
  category: string;
}

interface TemplateVariablesProps {
  onVariableInsert: (variable: string) => void;
  content: string;
}

const TEMPLATE_VARIABLES: TemplateVariable[] = [
  // Customer Variables
  {
    name: 'Customer Name',
    variable: '{{customerName}}',
    description: 'Full name of the customer',
    example: '<PERSON>',
    category: 'Customer'
  },
  {
    name: 'Contact Phone',
    variable: '{{contactPhone}}',
    description: 'Customer contact phone number',
    example: '+****************',
    category: 'Customer'
  },
  {
    name: 'Delivery Address',
    variable: '{{deliveryAddress}}',
    description: 'Customer delivery location',
    example: '123 Main St, City, State',
    category: 'Customer'
  },

  // Business Variables
  {
    name: 'Vendor Name',
    variable: '{{vendorName}}',
    description: 'Business name of the vendor',
    example: 'Tech Solutions Inc.',
    category: 'Business'
  },
  {
    name: 'Branch Name',
    variable: '{{branchName}}',
    description: 'Name of the branch location',
    example: 'Downtown Branch',
    category: 'Business'
  },

  // Order Variables
  {
    name: 'Order Number',
    variable: '{{orderNumber}}',
    description: 'Unique order reference number',
    example: 'ORD-12345',
    category: 'Order'
  },
  {
    name: 'Service Name',
    variable: '{{serviceName}}',
    description: 'Name of the service being provided',
    example: 'Premium Car Wash',
    category: 'Order'
  },
  {
    name: 'Total Amount',
    variable: '{{totalAmount}}',
    description: 'Total order amount',
    example: '$99.99',
    category: 'Order'
  },
  {
    name: 'Payment Status',
    variable: '{{paymentStatus}}',
    description: 'Current payment status',
    example: 'Paid',
    category: 'Order'
  },

  // Scheduling Variables
  {
    name: 'Appointment Time',
    variable: '{{appointmentTime}}',
    description: 'Scheduled appointment date and time',
    example: 'January 20, 2024 at 2:00 PM',
    category: 'Scheduling'
  },

  // Custom Variables
  {
    name: 'Custom Message',
    variable: '{{customMessage}}',
    description: 'Custom message content',
    example: 'Thank you for your business!',
    category: 'Custom'
  }
];

const VARIABLE_CATEGORIES = [
  { key: 'Customer', label: 'Customer Information', icon: 'icon-[heroicons--user-20-solid]' },
  { key: 'Business', label: 'Business Details', icon: 'icon-[heroicons--building-storefront-20-solid]' },
  { key: 'Order', label: 'Order Information', icon: 'icon-[heroicons--shopping-bag-20-solid]' },
  { key: 'Scheduling', label: 'Scheduling', icon: 'icon-[heroicons--calendar-20-solid]' },
  { key: 'Custom', label: 'Custom Fields', icon: 'icon-[heroicons--cog-6-tooth-20-solid]' }
];

export default function TemplateVariables({ onVariableInsert, content }: TemplateVariablesProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>(['Customer', 'Business', 'Order']);

  // Get variables used in current content
  const getUsedVariables = (): string[] => {
    const variablePattern = /{{(\w+(?:\.\w+)*)}}/g;
    const matches = content.match(variablePattern) || [];
    return matches;
  };

  // Filter variables based on search and categories
  const filteredVariables = TEMPLATE_VARIABLES.filter(variable => {
    const matchesSearch = variable.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         variable.variable.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         variable.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategories.length === 0 || selectedCategories.includes(variable.category);
    
    return matchesSearch && matchesCategory;
  });

  // Group variables by category
  const groupedVariables = VARIABLE_CATEGORIES.reduce((acc, category) => {
    acc[category.key] = filteredVariables.filter(v => v.category === category.key);
    return acc;
  }, {} as Record<string, TemplateVariable[]>);

  const handleVariableInsert = (variable: string) => {
    onVariableInsert(variable);
    toast.success(`Variable ${variable} inserted`);
  };

  const usedVariables = getUsedVariables();

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-semibold text-default-900">Template Variables</h4>
        <Chip size="sm" variant="flat" color="primary">
          {usedVariables.length} variables used
        </Chip>
      </div>

      <p className="text-sm text-default-600">
        Click any variable below to insert it into your template content. Variables will be replaced with actual data when messages are sent.
      </p>

      {/* Search */}
      <Input
        placeholder="Search variables..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        startContent={<Icon name="icon-[heroicons--magnifying-glass-20-solid]" classNames="h-4 w-4 text-default-400" />}
        variant="bordered"
        size="sm"
      />

      {/* Used Variables */}
      {usedVariables.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center gap-2 mb-3">
            <Icon name="icon-[heroicons--check-circle-20-solid]" classNames="h-4 w-4 text-success" />
            <span className="text-sm font-medium text-success">Variables in Use</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {usedVariables.map((variable, index) => (
              <Chip key={index} size="sm" variant="flat" color="success">
                {variable}
              </Chip>
            ))}
          </div>
        </Card>
      )}

      {/* Variable Categories */}
      <Accordion variant="bordered" selectionMode="multiple" defaultExpandedKeys={['Customer', 'Business', 'Order']}>
        {VARIABLE_CATEGORIES.map((category) => {
          const categoryVariables = groupedVariables[category.key];
          if (categoryVariables.length === 0) return null;

          return (
            <AccordionItem
              key={category.key}
              aria-label={category.label}
              title={
                <div className="flex items-center gap-2">
                  <Icon name={category.icon} classNames="h-4 w-4 text-primary" />
                  <span>{category.label}</span>
                  <Chip size="sm" variant="flat">
                    {categoryVariables.length}
                  </Chip>
                </div>
              }
            >
              <div className="space-y-3">
                {categoryVariables.map((variable) => (
                  <div key={variable.variable} className="flex items-start justify-between p-3 bg-default-50 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-default-900">{variable.name}</span>
                        {usedVariables.includes(variable.variable) && (
                          <Chip size="sm" variant="flat" color="success">
                            In Use
                          </Chip>
                        )}
                      </div>
                      <p className="text-sm text-default-600 mb-2">{variable.description}</p>
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-default-500">Example:</span>
                        <code className="text-xs bg-default-100 px-2 py-1 rounded text-default-700">
                          {variable.example}
                        </code>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      color="primary"
                      variant="flat"
                      onClick={() => handleVariableInsert(variable.variable)}
                      startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-3 w-3" />}
                    >
                      Insert
                    </Button>
                  </div>
                ))}
              </div>
            </AccordionItem>
          );
        })}
      </Accordion>

      {filteredVariables.length === 0 && (
        <div className="text-center py-8 text-default-500">
          <Icon name="icon-[heroicons--variable-20-solid]" classNames="h-12 w-12 mx-auto mb-4 text-default-300" />
          <p>No variables found</p>
          <p className="text-sm">Try adjusting your search terms</p>
        </div>
      )}
    </div>
  );
}
