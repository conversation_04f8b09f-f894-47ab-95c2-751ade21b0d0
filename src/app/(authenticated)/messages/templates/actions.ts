'use server';

import { api } from '@/lib/api';

interface ActionType {
  id: number;
  type: string;
  name: string;
  description: string;
  config_schema: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
  default_config: Record<string, any>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ApiResponse {
  types: ActionType[];
}

export async function getActionTypes() {
  try {
    const response = await api.get<ApiResponse>('action-types');

    if (!response?.types) {
      return { data: [], error: 'No action types found in response' };
    }

    // Transform the response to match our frontend interface
    const transformedData = response.types.map(type => ({
      id: type.id,
      type: type.type,
      name: type.name,
      description: type.description,
      configSchema: type.config_schema,
      defaultConfig: type.default_config,
      isActive: type.is_active,
      createdAt: type.created_at,
      updatedAt: type.updated_at
    }));

    return { data: transformedData, error: null };
  } catch (error) {
    return { data: [], error: 'Failed to load action types' };
  }
} 