'use client';

import { useState, ChangeEvent, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { Card, NextUIProvider } from "@nextui-org/react";
import { api } from "@/lib/api";
import TemplateActions from "../../components/TemplateActions";
import Image from "next/image";

interface MessageTemplate {
  id: string;
  name: string;
  content: string;
  image?: string;
  actions: any[];
}

export default function EditTemplatePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [content, setContent] = useState("");
  const [actions, setActions] = useState<any[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [name, setName] = useState("");
  const [preview, setPreview] = useState<string | null>(null);

  useEffect(() => {
    const fetchTemplate = async () => {
      try {
        const response = await api.get<{ data: MessageTemplate }>(`message-templates/${params.id}`);
        if (!response) {
          throw new Error('No response from server');
        }
        const template = response.data;
        setName(template.name);
        setContent(template.content);
        setActions(template.actions || []);
        if (template.image) {
          setPreview(template.image);
        }
      } catch (error) {
        console.error("Error fetching template:", error);
        toast.error("Failed to load template");
        router.push("/messages/templates");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplate();
  }, [params.id, router]);

  const handleUploadedFile = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("content", content);
      formData.append("actions", JSON.stringify(actions));

      if (preview && preview.startsWith('data:')) {
        const response = await fetch(preview);
        const blob = await response.blob();
        formData.append("image", blob);
      }

      await api.put(`message-templates/${params.id}`, formData);
      toast.success("Template updated successfully");
      router.push("/messages/templates");
    } catch (error) {
      console.error("Error updating template:", error);
      toast.error("Failed to update template");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <NextUIProvider>
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <Card className="p-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-default-700 mb-2">
                  Template Name
                </label>
                <input
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full rounded-lg border border-default-200 p-2"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-default-700 mb-2">
                  Template Image
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleUploadedFile}
                  className="w-full rounded-lg border border-default-200 p-2"
                />
                {preview && (
                  <div className="mt-2">
                    <Image
                      src={preview}
                      alt="Preview"
                      width={200}
                      height={200}
                      className="rounded-lg"
                    />
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-default-700 mb-2">
                  Template Content
                </label>
                <textarea
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  className="w-full rounded-lg border border-default-200 p-2 min-h-[200px]"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-default-700 mb-2">
                  Template Actions
                </label>
                <TemplateActions
                  initialActions={actions}
                  onActionsChange={setActions}
                />
              </div>
            </div>
          </Card>

          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="rounded-lg border border-default-200 px-4 py-2"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="rounded-lg bg-primary px-4 py-2 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Updating..." : "Update Template"}
            </button>
          </div>
        </form>
      </div>
    </NextUIProvider>
  );
} 