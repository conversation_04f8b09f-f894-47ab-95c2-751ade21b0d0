"use client";

import { useState, ChangeEvent, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { Card, Input, But<PERSON>, Tabs, Tab, Divider } from "@nextui-org/react";
import { useForm } from "react-hook-form";
import { useSession } from "next-auth/react";
import { api } from "@/lib/api";
import TemplateActions from "../components/TemplateActions";
import TemplateVariables from "../components/TemplateVariables";
import EnhancedContentEditor from "../components/EnhancedContentEditor";
// import TemplatePreview from "../components/TemplatePreview";
import Image from "next/image";
import { Icon } from "@/components/icon";

interface TemplateFormData {
  name: string;
  content: string;
  contentType?: 'plain' | 'html' | 'markdown';
  image?: FileList;
}

interface ObjectLink {
  id: string;
  objectType: 'product' | 'vendor' | 'service' | 'branch';
  objectId: string;
  objectName: string;
  displayText: string;
  route: string;
}

interface SessionInfo {
  user: {
    id: string;
    roles: any[];
  };
  branch?: any;
  vendor?: any;
  branchId?: string;
  vendorId?: string;
  isAdmin: boolean;
}

export default function CreateTemplatePage() {
  const router = useRouter();
  const { status } = useSession();
  const [objectLinks, setObjectLinks] = useState<ObjectLink[]>([]);
  const [preview, setPreview] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState('content');
  const [contentType, setContentType] = useState<'plain' | 'html' | 'markdown'>('plain');
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [sessionLoading, setSessionLoading] = useState(true);

  // Initialize form hooks first (must be called on every render)
  const { register, handleSubmit, watch, setValue, formState: { errors, isSubmitting } } = useForm<TemplateFormData>({
    defaultValues: {
      name: "",
      content: "",
    },
  });

  // Fetch session info from API
  useEffect(() => {
    const fetchSessionInfo = async () => {
      try {
        const response = await fetch('/api/auth/session-info');
        if (response.ok) {
          const data = await response.json();
          setSessionInfo(data);
          console.log('Fetched session info:', data);
        } else {
          console.error('Failed to fetch session info:', response.status);
        }
      } catch (error) {
        console.error('Error fetching session info:', error);
      } finally {
        setSessionLoading(false);
      }
    };

    fetchSessionInfo();
  }, []);

  // Get values from session info
  const isAdmin = sessionInfo?.isAdmin || false;
  const branchId = sessionInfo?.branchId;
  const vendorId = sessionInfo?.vendorId;

  const watchedContent = watch('content');
  const watchedName = watch('name');

  const handleUploadedFile = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image file must be smaller than 5MB');
        return;
      }

      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        toast.error('Please upload a valid image file (JPEG, PNG, GIF, or WebP)');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle variable insertion
  const handleVariableInsert = (variable: string) => {
    const currentContent = watchedContent || '';
    setValue('content', currentContent + variable);
  };

  const onSubmit = async (data: TemplateFormData) => {
    try {
      // Additional validation
      if (!data.name || data.name.trim().length < 3) {
        toast.error("Template name must be at least 3 characters long");
        return;
      }

      if (!data.content || data.content.trim().length < 1) {
        toast.error("Template content is required");
        return;
      }

      if (data.content.length > 10000) {
        toast.error("Template content must be 10,000 characters or less");
        return;
      }

      // Validate variable syntax
      const variablePattern = /{{(\w+(?:\.\w+)*)}}/g;
      const invalidVariables: string[] = [];
      let match;

      while ((match = variablePattern.exec(data.content)) !== null) {
        const variableName = match[1];
        if (!/^[a-zA-Z_][a-zA-Z0-9_.]*$/.test(variableName)) {
          invalidVariables.push(match[0]);
        }
      }

      if (invalidVariables.length > 0) {
        toast.error(`Invalid variable syntax: ${invalidVariables.join(', ')}`);
        return;
      }

      const formData = new FormData();
      formData.append("name", data.name.trim());
      formData.append("content", data.content.trim());

      // Convert object links to message actions format
      const messageActions = objectLinks.map(link => ({
        type_id: getActionTypeId(link.objectType),
        config: {
          route: link.route,
          objectType: link.objectType,
          objectId: link.objectId,
          displayText: link.displayText,
          // TODO: Consider adding metadata for richer client experience
          metadata: {
            objectName: link.objectName
          }
        },
        status: 'active'
      }));

      formData.append("actions", JSON.stringify(messageActions));

      if (preview) {
        const response = await fetch(preview);
        const blob = await response.blob();
        formData.append("image", blob);
      }

      await api.post("message-templates", formData);
      toast.success("Template created successfully");
      router.push("/messages/templates");
    } catch (error) {
      console.error("Error creating template:", error);
      toast.error("Failed to create template");
    }
  };

  // Helper function to get action type ID based on object type
  // For now, we'll use a generic object_route type and let the backend handle the specifics
  const getActionTypeId = (objectType: string): number => {
    // TODO: Replace with actual action type IDs from the backend
    // For now, using a generic ID that should work with the existing system
    return 2; // Assuming object_route action type has ID 2 based on API documentation
  };

  // Show loading state while session is loading
  if (status === "loading" || sessionLoading) {
    return (
      <div className="page-content p-5">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-default-900 dark:text-white">
            Create Message Template
          </h1>
          <p className="mt-1 text-default-600 dark:text-default-400">
            Loading...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-default-900 dark:text-white">
          Create Message Template
        </h1>
        <p className="mt-1 text-default-600 dark:text-default-400">
          Create a new reusable message template
        </p>
      </div>
      
      <div className="max-w-6xl">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Template Name */}
          <Card className="p-6">
            <Input
              label="Template Name"
              placeholder="Enter template name"
              {...register("name", {
                required: "Template name is required",
                minLength: { value: 3, message: "Template name must be at least 3 characters" },
                maxLength: { value: 100, message: "Template name must be 100 characters or less" }
              })}
              isRequired
              variant="bordered"
              isInvalid={!!errors.name}
              errorMessage={errors.name?.message}
              maxLength={100}
              description={`${watchedName?.length || 0}/100 characters`}
            />
          </Card>

          {/* Main Content Tabs */}
          <Card className="p-6">
            <Tabs
              selectedKey={selectedTab}
              onSelectionChange={(key) => setSelectedTab(key as string)}
              variant="underlined"
              className="w-full"
            >
              <Tab key="content" title="Content & Variables">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
                  {/* Content Editor */}
                  <div className="lg:col-span-2 space-y-6">
                    <EnhancedContentEditor
                      value={watchedContent || ''}
                      onChange={(value) => setValue('content', value)}
                      onVariableInsert={handleVariableInsert}
                      label="Template Content"
                      placeholder="Enter your message template content. Use {{variableName}} for dynamic content."
                      description="Create engaging messages with dynamic variables that will be replaced with actual data."
                      isRequired
                      isInvalid={!!errors.content}
                      errorMessage={errors.content?.message}
                      maxLength={10000}
                      contentType={contentType}
                      onContentTypeChange={setContentType}
                    />

                    {/* Image Upload */}
                    <div>
                      <label className="block text-sm font-medium text-default-700 mb-2">
                        Template Image (Optional)
                      </label>
                      <div className="space-y-4">
                        <input
                          type="file"
                          accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
                          onChange={handleUploadedFile}
                          className="block w-full text-sm text-default-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                        />
                        <p className="text-xs text-default-500">
                          Supported formats: JPEG, PNG, GIF, WebP. Maximum size: 5MB.
                        </p>
                        {preview && (
                          <div className="relative w-32 h-32">
                            <Image
                              src={preview}
                              alt="Preview"
                              fill
                              className="object-cover rounded-lg"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Variables Sidebar */}
                  <div className="lg:col-span-1">
                    <TemplateVariables
                      onVariableInsert={handleVariableInsert}
                      content={watchedContent || ''}
                    />
                  </div>
                </div>
              </Tab>

              <Tab key="preview" title="Preview">
                <div className="mt-6">
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-default-900">Template Preview</h4>
                    <div className="bg-default-50 p-4 rounded-lg border-l-4 border-primary">
                      <div className="whitespace-pre-wrap text-default-900 leading-relaxed">
                        {watchedContent || (
                          <span className="text-default-400 italic">
                            Enter template content to see preview...
                          </span>
                        )}
                      </div>
                    </div>
                    <p className="text-sm text-default-600">
                      Variables like {`{{customerName}}`} will be replaced with actual data when messages are sent.
                    </p>
                  </div>
                </div>
              </Tab>

              <Tab key="actions" title="Object Links">
                <div className="mt-6">
                  <TemplateActions
                    initialActions={objectLinks}
                    onActionsChange={setObjectLinks}
                    isAdmin={isAdmin}
                    branchId={branchId}
                    vendorId={vendorId}
                  />
                </div>
              </Tab>
            </Tabs>
          </Card>



          <div className="flex justify-end gap-4">
            <Button
              variant="light"
              onPress={() => router.back()}
              isDisabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              type="submit"
              isLoading={isSubmitting}
              isDisabled={!watchedName || !watchedContent || isSubmitting}
              startContent={!isSubmitting ? <Icon name="icon-[heroicons--plus-20-solid]" /> : undefined}
            >
              {isSubmitting ? "Creating..." : "Create Template"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
