"use client";

import { Chip } from "@nextui-org/react";

interface OrderStatusChipProps {
  status: string;
}

export default function OrderStatusChip({ status }: OrderStatusChipProps) {
  const getColor = (status: string) => {
    switch (status) {
      case "Completed":
      case "Delivered":
        return "success";
      case "Pending":
        return "warning";
      case "Placed":
        return "secondary";
      case "Processing":
      case "Delivering":
        return "primary";
      default:
        return "danger";
    }
  };

  return (
    <Chip
      color={getColor(status)}
      variant="flat"
    >
      {status}
    </Chip>
  );
}
