"use client";

import { Chip } from "@nextui-org/react";

interface InvoiceStatusChipProps {
  status: string;
}

export default function InvoiceStatusChip({ status }: InvoiceStatusChipProps) {
  const getColor = (status: string) => {
    switch (status) {
      case "Paid":
        return "success";
      case "Pending":
        return "warning";
      default:
        return "danger";
    }
  };

  return (
    <Chip
      color={getColor(status)}
      variant="flat"
      size="sm"
    >
      {status}
    </Chip>
  );
}
