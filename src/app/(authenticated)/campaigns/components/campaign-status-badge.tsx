"use client";

import { Chip, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@nextui-org/react";
import { Icon } from "@/components/icon";

interface CampaignStatusBadgeProps {
  status: Campaign['status'];
  onChange?: (newStatus: Campaign['status']) => void;
  readonly?: boolean;
}

export default function CampaignStatusBadge({ 
  status, 
  onChange, 
  readonly = false 
}: CampaignStatusBadgeProps) {
  const statusConfig = {
    Draft: {
      color: "secondary" as const,
      icon: "icon-[heroicons--document-text-20-solid]",
      nextStates: ["Pending"] as Campaign['status'][],
    },
    Pending: {
      color: "warning" as const,
      icon: "icon-[heroicons--clock-20-solid]",
      nextStates: ["Approved", "Draft"] as Campaign['status'][],
    },
    Approved: {
      color: "success" as const,
      icon: "icon-[heroicons--check-circle-20-solid]",
      nextStates: ["Expired"] as Campaign['status'][],
    },
    Expired: {
      color: "danger" as const,
      icon: "icon-[heroicons--x-circle-20-solid]",
      nextStates: [] as Campaign['status'][],
    },
  };

  const config = statusConfig[status];
  const availableStates = config.nextStates;

  if (readonly || !onChange || availableStates.length === 0) {
    return (
      <Chip
        color={config.color}
        variant="flat"
        className={`text-xs ${readonly ? "opacity-80" : ""}`}
        startContent={<Icon name={config.icon} classNames="h-3 w-3" />}
      >
        {status}
        {readonly && availableStates.length > 0 && (
          <span className="ml-1 text-xs opacity-60">(Admin only)</span>
        )}
      </Chip>
    );
  }

  return (
    <Dropdown>
      <DropdownTrigger>
        <Chip
          color={config.color}
          variant="flat"
          className="text-xs cursor-pointer hover:opacity-80 transition-opacity"
          startContent={<Icon name={config.icon} classNames="h-3 w-3" />}
          endContent={<Icon name="icon-[heroicons--chevron-down-20-solid]" classNames="h-3 w-3" />}
        >
          {status}
        </Chip>
      </DropdownTrigger>
      <DropdownMenu
        aria-label="Campaign status actions"
        onAction={(key) => onChange(key as Campaign['status'])}
      >
        {availableStates.map((state) => (
          <DropdownItem
            key={state}
            startContent={
              <Icon name={statusConfig[state].icon} classNames="h-4 w-4" />
            }
          >
            Change to {state}
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
}
