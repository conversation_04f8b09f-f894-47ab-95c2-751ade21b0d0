"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { Icon } from "@/components/icon";
import { deleteCampaign } from "../actions";

interface CampaignDeleteButtonProps {
  campaign: Campaign;
  canDelete: boolean;
  isAdmin: boolean;
}

export default function CampaignDeleteButton({
  campaign,
  canDelete,
  isAdmin,
}: CampaignDeleteButtonProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  const handleDelete = async () => {
    const confirmed = confirm(
      "Are you sure you want to delete this campaign? This action cannot be undone."
    );
    
    if (!confirmed) return;

    setIsDeleting(true);
    
    try {
      const formData = new FormData();
      formData.append("id", campaign.id);
      
      await deleteCampaign(formData);
      
      toast.success("Campaign deleted successfully!");
      router.push("/campaigns");
    } catch (error) {
      toast.error("Failed to delete campaign");
      console.error("Delete error:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  if (!canDelete) {
    return (
      <button
        disabled
        className="flex items-center space-x-2 rounded-lg border border-gray-200 px-4 py-2 text-gray-400 cursor-not-allowed"
        title={
          isAdmin 
            ? "Cannot delete draft campaigns (admin restriction)" 
            : "Can only delete campaigns in draft status"
        }
      >
        <Icon name="icon-[heroicons--trash-20-solid]" classNames="h-4 w-4" />
        <span>Delete</span>
      </button>
    );
  }

  return (
    <button
      onClick={handleDelete}
      disabled={isDeleting}
      className="flex items-center space-x-2 rounded-lg border border-red-200 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
      title={
        isAdmin 
          ? "Admin can delete non-draft campaigns" 
          : "You can only delete draft campaigns"
      }
    >
      <Icon name="icon-[heroicons--trash-20-solid]" classNames="h-4 w-4" />
      <span>{isDeleting ? "Deleting..." : "Delete"}</span>
    </button>
  );
}
