"use client";

import { useState, ReactNode, useEffect } from "react";
import { useF<PERSON>, Controller } from "react-hook-form";
import { Button, Input, Textarea } from "@nextui-org/react";
import AsyncSelect from "react-select/async";
import { showToastPromise } from "@/lib/toast-utils";
import Image from "next/image";

import { createCampaign, updateCampaign } from "../actions";
import { imagePath } from "@/lib/api";
import CustomDatePicker from "@/components/date-picker";
import { FormModalWithForm } from "@/components/ui/form-modal";

interface CampaignFormData {
  name: string;
  details: string;
  link?: string;
  startDate: Date;
  endDate: Date;
  imageUpload?: File;
  // Optional linking fields
  taskIds?: string[];
  productIds?: string[];
  serviceIds?: string[];
  specialityIds?: string[];
}

interface CampaignFormProps {
  defaultValues?: Partial<Campaign>;
  isEditing?: boolean;
  trigger?: ReactNode;
}

interface SessionInfo {
  user: {
    id: string;
    roles: any[];
  };
  branch?: any;
  vendor?: any;
  branchId?: string;
  vendorId?: string;
  isAdmin: boolean;
}

export default function CampaignForm({
  defaultValues,
  isEditing = false,
  trigger
}: CampaignFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(
    defaultValues?.image ? imagePath(defaultValues.image.url) : null
  );
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);

  // Fetch session info when component mounts
  useEffect(() => {
    const fetchSessionInfo = async () => {
      try {
        const response = await fetch('/api/auth/session-info');
        if (response.ok) {
          const data = await response.json();
          setSessionInfo(data);
        }
      } catch (error) {
        console.error('Error fetching session info:', error);
      }
    };

    fetchSessionInfo();
  }, []);

  // Lookup functions for async selects
  const loadTasks = async (inputValue: string) => {
    if (inputValue.length > 2) {
      const response = await fetch(`/api/tasks?s=${inputValue}`);
      const data = await response.json();
      return data.map((task: Task) => ({ value: task.id, label: task.name }));
    }
    return [];
  };

  const loadProducts = async (inputValue: string) => {
    if (inputValue.length > 2) {
      // Use branch products endpoint when branchId is available
      let endpoint = '/api/products';
      if (sessionInfo?.branchId) {
        endpoint = `/api/branches/${sessionInfo.branchId}/products`;
      }

      const response = await fetch(`${endpoint}?s=${inputValue}`);
      const data = await response.json();
      return data.map((product: Product) => ({ value: product.id, label: product.name }));
    }
    return [];
  };

  const loadServices = async (inputValue: string) => {
    if (inputValue.length > 2) {
      // Use vendor services endpoint when vendorId is available
      let endpoint = '/api/services';
      if (sessionInfo?.vendorId) {
        endpoint = `/api/vendors/${sessionInfo.vendorId}/services`;
      }

      const response = await fetch(`${endpoint}?s=${inputValue}`);
      const data = await response.json();
      return data.map((service: Service) => ({ value: service.id, label: service.name }));
    }
    return [];
  };

  const loadSpecialities = async (inputValue: string) => {
    if (inputValue.length > 2) {
      const response = await fetch(`/api/specialities?s=${inputValue}`);
      const data = await response.json();
      return data.map((speciality: Speciality) => ({ value: speciality.id, label: speciality.name }));
    }
    return [];
  };

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    control,
    formState: { errors, isSubmitting },
  } = useForm<CampaignFormData>({
    defaultValues: {
      name: defaultValues?.name || "",
      details: defaultValues?.details || "",
      link: defaultValues?.link || "",
      startDate: defaultValues?.startDate ? new Date(defaultValues.startDate) : new Date(),
      endDate: defaultValues?.endDate ? new Date(defaultValues.endDate) : new Date(),
    },
  });

  // Watch form values to generate link preview
  const watchedValues = watch();

  // Generate preview link based on current selections
  const getPreviewLink = () => {
    if (watchedValues.link && watchedValues.link.trim()) {
      return watchedValues.link;
    }

    if (watchedValues.taskIds && watchedValues.taskIds.length > 0) {
      return `/tasks/${watchedValues.taskIds[0]}`;
    } else if (watchedValues.productIds && watchedValues.productIds.length > 0) {
      return `/products/${watchedValues.productIds[0]}`;
    } else if (watchedValues.serviceIds && watchedValues.serviceIds.length > 0) {
      return `/services/${watchedValues.serviceIds[0]}`;
    } else if (watchedValues.specialityIds && watchedValues.specialityIds.length > 0) {
      return `/specialities/${watchedValues.specialityIds[0]}`;
    }

    return "No link will be generated";
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setValue("imageUpload", file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: CampaignFormData) => {
    // Validate dates
    if (data.startDate >= data.endDate) {
      toast.error("End date must be after start date");
      return;
    }

    const formData = new FormData();

    if (isEditing && defaultValues?.id) {
      formData.append("id", defaultValues.id);
    }

    formData.append("name", data.name);
    formData.append("details", data.details);

    // Generate link based on selected entities or use custom link
    let generatedLink = data.link || "";

    // Priority order: tasks > products > services > specialities
    // Use the first selected entity as the primary link
    if (data.taskIds && data.taskIds.length > 0) {
      generatedLink = `/tasks/${data.taskIds[0]}`;
    } else if (data.productIds && data.productIds.length > 0) {
      generatedLink = `/products/${data.productIds[0]}`;
    } else if (data.serviceIds && data.serviceIds.length > 0) {
      generatedLink = `/services/${data.serviceIds[0]}`;
    } else if (data.specialityIds && data.specialityIds.length > 0) {
      generatedLink = `/specialities/${data.specialityIds[0]}`;
    }

    formData.append("link", generatedLink);

    // Format dates properly
    const startDate = new Date(data.startDate);
    startDate.setHours(0, 0, 0, 0);
    formData.append("startDate", startDate.toISOString());

    const endDate = new Date(data.endDate);
    endDate.setHours(23, 59, 59, 999);
    formData.append("endDate", endDate.toISOString());

    if (data.imageUpload) {
      formData.append("image", data.imageUpload);
    }

    // Add linked entities if selected (for additional tracking/relationships)
    if (data.taskIds && data.taskIds.length > 0) {
      data.taskIds.forEach(taskId => formData.append("taskIds[]", taskId));
    }
    if (data.productIds && data.productIds.length > 0) {
      data.productIds.forEach(productId => formData.append("productIds[]", productId));
    }
    if (data.serviceIds && data.serviceIds.length > 0) {
      data.serviceIds.forEach(serviceId => formData.append("serviceIds[]", serviceId));
    }
    if (data.specialityIds && data.specialityIds.length > 0) {
      data.specialityIds.forEach(specialityId => formData.append("specialityIds[]", specialityId));
    }

    const action = isEditing ? updateCampaign : createCampaign;
    const operation = isEditing ? "update" : "create";

    await showToastPromise(
      action(formData).then(result => {
        if (!result.success) {
          throw new Error(result.error || `Failed to ${operation} campaign`);
        }
        return result;
      }),
      "campaign",
      operation
    );

    reset();
    setImagePreview(null);
    setIsOpen(false);
  };

  const handleClose = () => {
    setIsOpen(false);
    reset();
    setImagePreview(defaultValues?.image ? imagePath(defaultValues.image.url) : null);
  };

  return (
    <>
      {trigger ? (
        <div onClick={() => setIsOpen(true)}>
          {trigger}
        </div>
      ) : (
        <Button
          onPress={() => setIsOpen(true)}
          color="primary"
          startContent={<span>+</span>}
        >
          {isEditing ? "Edit Campaign" : "Create Campaign"}
        </Button>
      )}

      <FormModalWithForm
        isOpen={isOpen}
        onClose={handleClose}
        title={isEditing ? "Edit Campaign" : "Create New Campaign"}
        size="5xl"
        scrollBehavior="inside"
        onSubmit={handleSubmit(onSubmit)}
        isSubmitting={isSubmitting}
        submitLabel={isEditing ? "Update Campaign" : "Create Campaign"}
        cancelLabel="Cancel"
      >
              {/* Basic Information Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-default-900 border-b border-default-200 pb-2">
                  Campaign Information
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <Input
                    label="Campaign Name"
                    placeholder="Enter campaign name"
                    isRequired
                    {...register("name", { required: "Campaign name is required" })}
                    errorMessage={errors.name?.message}
                    isInvalid={!!errors.name}
                  />

                  <Input
                    label="Custom Link (Optional)"
                    placeholder="https://example.com or /custom/path"
                    type="text"
                    {...register("link")}
                    errorMessage={errors.link?.message}
                    isInvalid={!!errors.link}
                    description="If provided, this will override the auto-generated link from selected entities"
                  />
                </div>

                <Textarea
                  label="Description"
                  placeholder="Enter campaign description"
                  isRequired
                  {...register("details", { required: "Description is required" })}
                  errorMessage={errors.details?.message}
                  isInvalid={!!errors.details}
                  minRows={3}
                />
              </div>

              {/* Schedule Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-default-900 border-b border-default-200 pb-2">
                  Campaign Schedule
                </h3>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Start Date</label>
                    <Controller
                      name="startDate"
                      control={control}
                      rules={{ required: "Start date is required" }}
                      render={({ field }) => (
                        <CustomDatePicker
                          value={field.value}
                          onChange={field.onChange}
                          className={errors.startDate ? "border-red-500" : ""}
                        />
                      )}
                    />
                    {errors.startDate && (
                      <p className="text-sm text-red-500">{errors.startDate.message}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">End Date</label>
                    <Controller
                      name="endDate"
                      control={control}
                      rules={{ required: "End date is required" }}
                      render={({ field }) => (
                        <CustomDatePicker
                          value={field.value}
                          onChange={field.onChange}
                          className={errors.endDate ? "border-red-500" : ""}
                        />
                      )}
                    />
                    {errors.endDate && (
                      <p className="text-sm text-red-500">{errors.endDate.message}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Media Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-default-900 border-b border-default-200 pb-2">
                  Campaign Media
                </h3>

                <div className="space-y-3">
                  <label className="text-sm font-medium">Campaign Image</label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-white hover:file:bg-primary/90"
                  />
                  {imagePreview && (
                    <div className="mt-3">
                      <Image
                        src={imagePreview}
                        alt="Campaign preview"
                        width={200}
                        height={120}
                        className="rounded-lg object-cover border border-default-200"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Campaign Linking Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-default-900 border-b border-default-200 pb-2">
                  Link Campaign To (Optional)
                </h3>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <p className="text-sm text-blue-800 mb-2">
                    <strong>Link Priority:</strong> Tasks → Products → Services → Specialities
                  </p>
                  <p className="text-sm text-blue-700 mb-2">
                    The campaign will link to the first selected entity in priority order, or use your custom link if provided.
                  </p>
                  <div className="text-sm">
                    <span className="font-medium text-blue-800">Generated Link: </span>
                    <code className="bg-blue-100 px-2 py-1 rounded text-blue-900">
                      {getPreviewLink()}
                    </code>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-default-700">Tasks</label>
                    <Controller
                      name="taskIds"
                      control={control}
                      render={({ field }) => (
                        <AsyncSelect
                          isMulti
                          cacheOptions
                          loadOptions={loadTasks}
                          placeholder="Search and select tasks..."
                          value={field.value?.map(id => ({ value: id, label: id })) || []}
                          onChange={(selected) => field.onChange(selected?.map(option => option.value) || [])}
                          className="text-sm"
                          styles={{
                            control: (base) => ({
                              ...base,
                              minHeight: '40px',
                              borderColor: '#e4e4e7',
                            }),
                          }}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-default-700">Products</label>
                    <Controller
                      name="productIds"
                      control={control}
                      render={({ field }) => (
                        <AsyncSelect
                          isMulti
                          cacheOptions
                          loadOptions={loadProducts}
                          placeholder="Search and select products..."
                          value={field.value?.map(id => ({ value: id, label: id })) || []}
                          onChange={(selected) => field.onChange(selected?.map(option => option.value) || [])}
                          className="text-sm"
                          styles={{
                            control: (base) => ({
                              ...base,
                              minHeight: '40px',
                              borderColor: '#e4e4e7',
                            }),
                          }}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-default-700">Services</label>
                    <Controller
                      name="serviceIds"
                      control={control}
                      render={({ field }) => (
                        <AsyncSelect
                          isMulti
                          cacheOptions
                          loadOptions={loadServices}
                          placeholder="Search and select services..."
                          value={field.value?.map(id => ({ value: id, label: id })) || []}
                          onChange={(selected) => field.onChange(selected?.map(option => option.value) || [])}
                          className="text-sm"
                          styles={{
                            control: (base) => ({
                              ...base,
                              minHeight: '40px',
                              borderColor: '#e4e4e7',
                            }),
                          }}
                        />
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-default-700">Specialities</label>
                    <Controller
                      name="specialityIds"
                      control={control}
                      render={({ field }) => (
                        <AsyncSelect
                          isMulti
                          cacheOptions
                          loadOptions={loadSpecialities}
                          placeholder="Search and select specialities..."
                          value={field.value?.map(id => ({ value: id, label: id })) || []}
                          onChange={(selected) => field.onChange(selected?.map(option => option.value) || [])}
                          className="text-sm"
                          styles={{
                            control: (base) => ({
                              ...base,
                              minHeight: '40px',
                              borderColor: '#e4e4e7',
                            }),
                          }}
                        />
                      )}
                    />
                  </div>
                </div>
              </div>
      </FormModalWithForm>
    </>
  );
}
