"use client";

import Link from "next/link";
import Image from "next/image";
import { Session } from "next-auth";
import toast from "react-hot-toast";
import { format } from "date-fns";
import { useRouter } from "next/navigation";

import { imagePath } from "@/lib/api";
import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { deleteCampaign, updateCampaignStatus } from "../actions";
import CampaignStatusBadge from "./campaign-status-badge";
import CampaignForm from "./campaign-form";

export default function CampaignsTable({
  data,
  meta,
  session,
}: {
  data: Campaign[];
  meta: PaginationMeta;
  session: Session | null;
}) {
  const router = useRouter();

  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(deleteCampaign(formData), {
      loading: "Deleting campaign...",
      success: "Campaign deleted!",
      error: "Failed to delete campaign",
    });
  };

  // Check if user can delete a specific campaign
  const canDeleteCampaign = (campaign: Campaign) => {
    if (isAdmin) {
      // Admin can delete any campaign EXCEPT drafts
      return campaign.status !== "Draft";
    } else {
      // Non-admin (vendors) can only delete drafts
      return campaign.status === "Draft";
    }
  };

  const onStatusChange = async (campaign: Campaign, newStatus: Campaign['status']) => {
    try {
      await updateCampaignStatus(campaign.id, newStatus);
      toast.success("Campaign status updated!");
      router.refresh(); // Refresh the page to show updated status
    } catch (error) {
      toast.error("Failed to update campaign status");
      console.error("Status update error:", error);
    }
  };

  // Check if user is admin
  const isAdmin = session?.user?.roles?.some((role: any) => role.name === "admin") || false;

  const columns = [
    {
      uid: "name",
      name: "Campaign",
      renderCell: (campaign: Campaign) => (
        <Link href={`/campaigns/${campaign.id}`} className="flex items-center">
          {campaign.image && (
            <Image
              src={imagePath(campaign.image.url)}
              alt={campaign.name}
              width={240}
              height={240}
              className="size-16 rounded-lg mr-3"
            />
          )}
          <div>
            <p className="tb-lead font-semibold">{campaign.name}</p>
            <p className="text-sm text-default-500 truncate max-w-xs">
              {campaign.details}
            </p>
          </div>
        </Link>
      ),
    },

    {
      uid: "dates",
      name: "Duration",
      renderCell: (campaign: Campaign) => (
        <div className="space-y-1">
          <div className="text-sm">
            <span className="text-default-500">Start: </span>
            <span className="font-medium">
              {format(new Date(campaign.startDate), "MMM dd, yyyy")}
            </span>
          </div>
          <div className="text-sm">
            <span className="text-default-500">End: </span>
            <span className="font-medium">
              {format(new Date(campaign.endDate), "MMM dd, yyyy")}
            </span>
          </div>
        </div>
      ),
    },

    {
      uid: "status",
      name: "Status",
      renderCell: (campaign: Campaign) => (
        <CampaignStatusBadge
          status={campaign.status}
          onChange={isAdmin ? (newStatus) => onStatusChange(campaign, newStatus) : undefined}
          readonly={!isAdmin}
        />
      ),
    },

    {
      uid: "link",
      name: "Link",
      renderCell: (campaign: Campaign) => (
        campaign.link ? (
          <Link
            href={campaign.link}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:underline text-sm truncate max-w-xs block"
          >
            {campaign.link}
          </Link>
        ) : (
          <span className="text-default-400 text-sm">No link</span>
        )
      ),
    },

    {
      name: "Actions",
      uid: "actions",
      renderCell: (campaign: Campaign) => (
        <div className="flex w-fit items-center gap-3">
          <Link href={`/campaigns/${campaign.id}`}>
            <Icon name="icon-[heroicons--eye-20-solid]" classNames="text-primary" />
          </Link>

          <CampaignForm
            defaultValues={campaign}
            isEditing={true}
            trigger={
              <button type="button">
                <Icon name="icon-[heroicons--pencil-square-20-solid]" classNames="text-primary" />
              </button>
            }
          />

          {canDeleteCampaign(campaign) ? (
            <form
              onSubmit={(e) => {
                e.preventDefault();
                onDeleteSubmit(campaign.id);
              }}
            >
              <button
                type="submit"
                className="text-red-500 hover:text-red-700"
                title={
                  isAdmin
                    ? "Admin can delete non-draft campaigns"
                    : "You can only delete draft campaigns"
                }
              >
                <Icon name="icon-[heroicons--trash-20-solid]" />
              </button>
            </form>
          ) : (
            <button
              disabled
              className="text-gray-400 cursor-not-allowed"
              title={
                isAdmin
                  ? "Cannot delete draft campaigns (admin restriction)"
                  : "Can only delete campaigns in draft status"
              }
            >
              <Icon name="icon-[heroicons--trash-20-solid]" />
            </button>
          )}
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      title="Campaigns"
      columns={columns}
      data={data}
      meta={meta}
      filter={[
        {
          column: "status",
          displayName: "Status",
          values: [
            { name: "Draft", value: "Draft" },
            { name: "Pending", value: "Pending" },
            { name: "Approved", value: "Approved" },
            { name: "Expired", value: "Expired" },
          ],
        },
      ]}
      action={
        <CampaignForm
          trigger={
            <button className="flex items-center space-x-3 rounded-lg bg-primary px-6 py-2 text-white hover:bg-primary/90 transition-colors">
              <Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />
              <span>Add Campaign</span>
            </button>
          }
        />
      }
    />
  );
}
