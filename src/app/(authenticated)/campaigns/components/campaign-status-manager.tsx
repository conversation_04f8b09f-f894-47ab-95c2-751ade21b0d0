"use client";

import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import CampaignStatusBadge from "./campaign-status-badge";
import { updateCampaignStatus } from "../actions";

interface CampaignStatusManagerProps {
  campaign: Campaign;
  isAdmin: boolean;
}

export default function CampaignStatusManager({
  campaign,
  isAdmin,
}: CampaignStatusManagerProps) {
  const router = useRouter();

  const handleStatusChange = async (newStatus: Campaign['status']) => {
    try {
      await updateCampaignStatus(campaign.id, newStatus);
      toast.success("Campaign status updated successfully!");
      router.refresh(); // Refresh the page to show updated status
    } catch (error) {
      toast.error("Failed to update campaign status");
      console.error("Status update error:", error);
    }
  };

  return (
    <CampaignStatusBadge 
      status={campaign.status} 
      readonly={!isAdmin}
      onChange={isAdmin ? handleStatusChange : undefined}
    />
  );
}
