"use server";

import { api } from "@/lib/api";
import { auth } from "@/auth";
import { revalidatePath } from "next/cache";

export const createCampaign = async (data: FormData): Promise<{ success: boolean; campaign?: Campaign; error?: string }> => {
  try {
    const session = await auth();

    if (!session?.vendor?.id || !session?.branch?.id) {
      return {
        success: false,
        error: "Unauthorized: Missing vendor or branch information"
      };
    }

    // Add vendor and branch IDs to the form data
    data.append("vendorId", session.vendor.id);
    data.append("branchId", session.branch.id);

    const campaign = await api.post<Campaign>("campaigns", data);
    revalidatePath("/campaigns");
    return { success: true, campaign: campaign || undefined };
  } catch (error) {
    console.error("Failed to create campaign:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create campaign"
    };
  }
};

export const updateCampaign = async (data: FormData): Promise<{ success: boolean; campaign?: Campaign; error?: string }> => {
  try {
    const campaignId = data.get("id") as string;

    if (!campaignId) {
      return {
        success: false,
        error: "Campaign ID is required"
      };
    }

    const campaign = await api.put<Campaign>(`campaigns/${campaignId}`, data);
    revalidatePath("/campaigns");
    revalidatePath(`/campaigns/${campaignId}`);
    return { success: true, campaign: campaign || undefined };
  } catch (error) {
    console.error("Failed to update campaign:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update campaign"
    };
  }
};

export const deleteCampaign = async (data: FormData): Promise<{ success: boolean; error?: string }> => {
  try {
    const session = await auth();
    const campaignId = data.get("id") as string;

    if (!campaignId) {
      return {
        success: false,
        error: "Campaign ID is required"
      };
    }

    // Get campaign details to check status
    const campaign = await api.get<Campaign>(`campaigns/${campaignId}`);

    if (!campaign) {
      return {
        success: false,
        error: "Campaign not found"
      };
    }

    // Check user role
    const isAdmin = session?.user?.roles?.some((role: any) => role.name === "admin") || false;

    // Apply deletion rules
    if (isAdmin) {
      // Admin can delete any campaign EXCEPT drafts
      if (campaign.status === "Draft") {
        return {
          success: false,
          error: "Administrators cannot delete draft campaigns"
        };
      }
    } else {
      // Non-admin (vendors) can only delete drafts
      if (campaign.status !== "Draft") {
        return {
          success: false,
          error: "You can only delete campaigns in draft status"
        };
      }
    }

    await api.destroy(campaignId, "campaigns");
    revalidatePath("/campaigns");
    return { success: true };
  } catch (error) {
    console.error("Failed to delete campaign:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete campaign"
    };
  }
};

export const updateCampaignStatus = async (campaignId: string, status: Campaign['status']): Promise<{ success: boolean; error?: string }> => {
  try {
    const session = await auth();

    // Check if user is admin
    const isAdmin = session?.user?.roles?.some((role: any) => role.name === "admin") || false;

    if (!isAdmin) {
      return {
        success: false,
        error: "Unauthorized: Only administrators can change campaign status"
      };
    }

    const data = new FormData();
    data.append("id", campaignId);
    data.append("status", status);

    await api.put(`campaigns/${campaignId}`, data);
    revalidatePath("/campaigns");
    revalidatePath(`/campaigns/${campaignId}`);
    return { success: true };
  } catch (error) {
    console.error("Failed to update campaign status:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update campaign status"
    };
  }
};

// Helper function to auto-expire campaigns
export const checkAndExpireCampaigns = async () => {
  const session = await auth();
  
  if (!session?.branch?.id) {
    return;
  }

  const campaigns = await api.get<PaginatedData<Campaign>>(
    `branches/${session.branch.id}/campaigns`,
    { status: "Approved" }
  );

  const now = new Date();
  
  for (const campaign of campaigns?.data ?? []) {
    if (new Date(campaign.endDate) < now && campaign.status === "Approved") {
      await updateCampaignStatus(campaign.id, "Expired");
    }
  }
};
