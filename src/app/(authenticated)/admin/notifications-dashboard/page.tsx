import { getNotificationStats, getDeliveryRates } from "./actions";
import { formatDistanceToNow, format } from "date-fns";

// Helper function to parse string numbers
const parseCount = (count: string | number | undefined | null): number => {
  if (typeof count === 'number') return count;
  if (!count) return 0;
  return parseInt(count.replace(/[^0-9]/g, ''), 10) || 0;
};

// Helper function to calculate success rate
const calculateSuccessRate = (delivered: string | number | undefined | null, total: string | number | undefined | null): number => {
  const d = parseCount(delivered);
  const t = parseCount(total);
  return t > 0 ? (d / t) * 100 : 0;
};

// Helper function to get error details
const getErrorDetails = (error: string | undefined | null): { type: string; message: string } => {
  if (!error) {
    return {
      type: 'Unknown',
      message: 'An unknown error occurred during notification delivery.'
    };
  }

  if (error.includes('timeout')) {
    return {
      type: 'Timeout',
      message: 'The notification delivery timed out. This could be due to network issues or the recipient device being offline.'
    };
  }
  if (error.includes('invalid')) {
    return {
      type: 'Invalid',
      message: 'The notification could not be delivered due to invalid recipient information or configuration.'
    };
  }
  if (error.includes('quota')) {
    return {
      type: 'Quota',
      message: 'The notification delivery failed due to rate limiting or quota exceeded.'
    };
  }
  if (error.includes('permission')) {
    return {
      type: 'Permission',
      message: 'The notification delivery failed due to missing permissions or authorization.'
    };
  }
  return {
    type: 'Unknown',
    message: error
  };
};



export default async function NotificationDashboard() {
  // Get last 24 hours of data with proper date formatting
  const endDate = format(new Date(), 'yyyy-MM-dd');
  const startDate = format(new Date(Date.now() - 24 * 60 * 60 * 1000), 'yyyy-MM-dd');

  // Fetch data for last 24 hours
  const [statsResponse, ratesResponse] = await Promise.all([
    getNotificationStats({
      startDate,
      endDate
    }),
    getDeliveryRates({
      startDate,
      endDate
    })
  ]);

  // Handle errors by throwing to trigger error boundary
  if (statsResponse.error || ratesResponse.error) {
    throw new Error(statsResponse.error || ratesResponse.error || 'Failed to fetch notification data');
  }

  const stats = statsResponse.data;
  const rates = ratesResponse.data;

  // Transform stats data for metrics
  const totalNotifications = stats.reduce((sum, stat) => sum + parseCount(stat.count), 0);
  const successRate = calculateSuccessRate(
    stats.find(stat => stat.status === 'delivered')?.count,
    totalNotifications
  );
  const failedCount = stats
    .filter(stat => stat.status === 'failed')
    .reduce((sum, stat) => sum + parseCount(stat.count), 0);
  const averageDeliveryTime = 2.5; // This would come from a dedicated endpoint

  // Transform delivery rates data for failure analysis
  const failureAnalysis = {
    byChannel: Object.entries(rates).map(([key, data]) => ({
      channel: key.split('_')[1],
      successRate: calculateSuccessRate(data.delivered, data.total),
      failedCount: parseCount(data.failed),
      total: parseCount(data.total)
    })),
    byType: Object.entries(rates).map(([key, data]) => ({
      type: key.split('_')[0],
      successRate: calculateSuccessRate(data.delivered, data.total),
      failedCount: parseCount(data.failed),
      total: parseCount(data.total)
    })),
    recentFailures: stats
      .filter(stat => stat.status === 'failed')
      .slice(0, 5)
      .map(stat => ({
        id: `${stat.notification_type}_${stat.channel}`,
        type: stat.notification_type,
        channel: stat.channel,
        timestamp: new Date(),
        error: getErrorDetails(stat.error)
      }))
  };

  // Transform stats data for live feed
  const liveNotifications = stats
    .slice(0, 5)
    .map(stat => ({
      id: `${stat.notification_type}_${stat.channel}`,
      type: stat.notification_type,
      status: stat.status as 'delivered' | 'failed' | 'pending',
      timestamp: new Date()
    }));

  return (
    <div className="p-6 space-y-6">
      {/* Key Metrics Section */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Total Notifications</span>
              <span className="text-2xl font-semibold">{totalNotifications.toLocaleString()}</span>
              <div className="h-12 mt-2">
                {/* Placeholder for sparkline graph */}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Success Rate</span>
              <span className="text-2xl font-semibold">{successRate.toFixed(1)}%</span>
              <div className="h-12 mt-2">
                {/* Placeholder for sparkline graph */}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Failed Count</span>
              <span className="text-2xl font-semibold">{failedCount.toLocaleString()}</span>
              <div className="h-12 mt-2">
                {/* Placeholder for sparkline graph */}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <div className="p-4">
            <div className="flex flex-col">
              <span className="text-sm text-gray-500">Avg. Delivery Time</span>
              <span className="text-2xl font-semibold">{averageDeliveryTime}s</span>
              <div className="h-12 mt-2">
                {/* Placeholder for sparkline graph */}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Failure Analysis */}
        <div className="lg:col-span-2 space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div className="px-4 py-3 border-b">
              <h2 className="text-lg font-semibold">Failure Analysis</h2>
            </div>
            <div className="p-4">
              <div className="space-y-6">
                {/* By Channel */}
                <div>
                  <h3 className="text-sm font-medium mb-3">By Channel</h3>
                  <div className="space-y-2">
                    {failureAnalysis.byChannel.map((channel) => (
                      <div key={channel.channel} className="flex items-center justify-between">
                        <span className="text-sm">{channel.channel}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                channel.successRate > 90 ? 'bg-green-500' :
                                channel.successRate > 70 ? 'bg-yellow-500' : 'bg-red-500'
                              }`}
                              style={{ width: `${channel.successRate}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-500">
                            {channel.successRate.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Recent Failures */}
                <div>
                  <h3 className="text-sm font-medium mb-3">Recent Failures</h3>
                  <div className="space-y-2">
                    {failureAnalysis.recentFailures.map((failure) => (
                      <div key={failure.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-start">
                          <div>
                            <span className="font-medium">{failure.type}</span>
                            <span className="text-sm text-gray-500 ml-2">via {failure.channel}</span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {formatDistanceToNow(failure.timestamp, { addSuffix: true })}
                          </span>
                        </div>
                        <div className="mt-1">
                          <span className="text-sm font-medium text-red-600">{failure.error.type}</span>
                          <p className="text-sm text-gray-600 mt-1">{failure.error.message}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Live Feed */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg border border-gray-200 shadow-sm h-full">
            <div className="px-4 py-3 border-b">
              <h2 className="text-lg font-semibold">Live Feed</h2>
            </div>
            <div className="p-4">
              <div className="space-y-3">
                {liveNotifications.map((notification) => (
                  <div key={notification.id} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between items-start">
                      <div>
                        <span className="font-medium">{notification.type}</span>
                        <span className="text-sm text-gray-500 ml-2">
                          {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                        </span>
                      </div>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          notification.status === 'delivered'
                            ? 'bg-green-50 text-green-600'
                            : notification.status === 'failed'
                            ? 'bg-red-50 text-red-600'
                            : 'bg-yellow-50 text-yellow-600'
                        }`}
                      >
                        {notification.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 