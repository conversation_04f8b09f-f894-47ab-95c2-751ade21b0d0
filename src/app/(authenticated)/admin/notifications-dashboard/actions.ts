'use server';

import { api } from "@/lib/api";

interface NotificationStats {
  notification_type: string;
  channel: string;
  status: string;
  count: string;
  error?: string;
}

interface DeliveryRate {
  total: string;
  delivered: string;
  failed: number | string;
  rate: number;
}

interface DeliveryRates {
  [key: string]: DeliveryRate;
}

export async function getNotificationStats({
  startDate,
  endDate,
}: {
  startDate: string;
  endDate: string;
}): Promise<{ data: NotificationStats[]; error?: string }> {
  try {
    const response = await api.get<NotificationStats[]>(
      `/notification-tracking/stats`,
      {
        startDate,
        endDate
      }
    );

    if (!response) {
      throw new Error('No data received from notification stats API');
    }

    return { data: response, error: undefined };
  } catch (error) {
    console.error('Error fetching notification statistics:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to fetch notification statistics';
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        errorMessage = 'Network error: Unable to connect to notification service';
      } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
        errorMessage = 'Authentication error: Please log in again';
      } else if (error.message.includes('403') || error.message.includes('forbidden')) {
        errorMessage = 'Permission error: You do not have access to notification statistics';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timeout: The server took too long to respond';
      } else {
        errorMessage = `API error: ${error.message}`;
      }
    }

    return { data: [], error: errorMessage };
  }
}

export async function getDeliveryRates({
  startDate,
  endDate,
}: {
  startDate: string;
  endDate: string;
}): Promise<{ data: DeliveryRates; error?: string }> {
  try {
    const response = await api.get<DeliveryRates>(
      `/notification-tracking/delivery-rates`,
      {
        startDate,
        endDate
      }
    );

    if (!response) {
      throw new Error('No data received from delivery rates API');
    }

    return { data: response, error: undefined };
  } catch (error) {
    console.error('Error fetching delivery rates:', error);

    // Provide more specific error messages
    let errorMessage = 'Failed to fetch delivery rates';
    if (error instanceof Error) {
      if (error.message.includes('fetch')) {
        errorMessage = 'Network error: Unable to connect to delivery service';
      } else if (error.message.includes('401') || error.message.includes('unauthorized')) {
        errorMessage = 'Authentication error: Please log in again';
      } else if (error.message.includes('403') || error.message.includes('forbidden')) {
        errorMessage = 'Permission error: You do not have access to delivery rates';
      } else if (error.message.includes('timeout')) {
        errorMessage = 'Request timeout: The server took too long to respond';
      } else {
        errorMessage = `API error: ${error.message}`;
      }
    }

    return { data: {}, error: errorMessage };
  }
}