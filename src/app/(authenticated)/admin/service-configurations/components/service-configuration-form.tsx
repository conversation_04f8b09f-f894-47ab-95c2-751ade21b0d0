"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm, Controller } from "react-hook-form";
import {
  Input,
  Button,
  Select,
  SelectItem,
  Textarea,
  Card,
  CardBody,
  Switch,
  Divider,
  Autocomplete,
  AutocompleteItem
} from "@nextui-org/react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { Task, Service } from "@/types";

// Service Configuration interface
interface ServiceConfiguration {
  id?: string;
  name: string;
  description?: string;
  serviceId: string;
  active: boolean;
}

interface ServiceConfigurationFormProps {
  defaultValues?: ServiceConfiguration;
  isEdit?: boolean;
}

// Service option for autocomplete
interface ServiceOption {
  value: string;
  label: string;
}

export default function ServiceConfigurationForm({
  defaultValues,
  isEdit = false,
}: ServiceConfigurationFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Task and service state
  const [selectedTaskId, setSelectedTaskId] = useState<string>("");
  const [selectedTaskName, setSelectedTaskName] = useState<string>("");
  const [tasks, setTasks] = useState<Task[]>([]);
  const [serviceOptions, setServiceOptions] = useState<ServiceOption[]>([]);
  const [isLoadingTasks, setIsLoadingTasks] = useState(false);
  const [isLoadingServices, setIsLoadingServices] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch
  } = useForm<ServiceConfiguration>({
    defaultValues: defaultValues || {
      name: "",
      description: "",
      serviceId: "",
      active: true,
    },
  });

  // Load all tasks (since there are < 20)
  const loadTasks = useCallback(async (): Promise<void> => {
    setIsLoadingTasks(true);
    try {
      const response = await fetch('/api/tasks?per=50&active=true');
      if (!response.ok) {
        throw new Error('Failed to fetch tasks');
      }
      const data = await response.json();
      setTasks(data || []);
    } catch (error) {
      console.error('Error loading tasks:', error);
      toast.error('Failed to load tasks');
    } finally {
      setIsLoadingTasks(false);
    }
  }, []);

  // Load services filtered by task - fetch ALL services for the task
  const loadServices = useCallback(async (taskId: string, searchValue: string = ""): Promise<ServiceOption[]> => {
    if (!taskId) {
      return [];
    }

    setIsLoadingServices(true);
    try {
      let allServices: Service[] = [];
      let currentPage = 1;
      let hasMorePages = true;

      // Fetch all pages to ensure we get all services for this task
      while (hasMorePages) {
        const searchParam = searchValue ? `&s=${encodeURIComponent(searchValue)}` : '';
        const response = await fetch(`/api/services?task=${taskId}&active=true&per=100&page=${currentPage}${searchParam}`);

        if (!response.ok) {
          throw new Error('Failed to fetch services');
        }

        const result = await response.json();
        const services = result.data || result;

        if (Array.isArray(services)) {
          allServices = [...allServices, ...services];

          // Check if there are more pages (if result has meta pagination info)
          if (result.meta && result.meta.current_page < result.meta.last_page) {
            currentPage++;
          } else {
            hasMorePages = false;
          }
        } else {
          hasMorePages = false;
        }

        // Safety check to prevent infinite loops
        if (currentPage > 10) {
          console.warn('Stopped fetching services after 10 pages to prevent infinite loop');
          break;
        }
      }

      const options = allServices.map((service: Service) => ({
        value: service.id,
        label: service.name
      }));

      setServiceOptions(options);
      return options;
    } catch (error) {
      console.error('Error loading services:', error);
      toast.error('Failed to load services');
      return [];
    } finally {
      setIsLoadingServices(false);
    }
  }, []);

  // Load tasks on component mount
  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  // Load service details and associated task when editing
  useEffect(() => {
    if (isEdit && defaultValues?.serviceId) {
      const loadServiceDetails = async () => {
        try {
          // Fetch service details to get the task ID
          const response = await fetch(`/api/services/${defaultValues.serviceId}`);
          if (response.ok) {
            const service = await response.json();
            if (service?.taskId) {
              setSelectedTaskId(service.taskId);
              // Load services for this task
              await loadServices(service.taskId);
            }
          }
        } catch (error) {
          console.error('Error loading service details:', error);
        }
      };

      loadServiceDetails();
    }
  }, [isEdit, defaultValues?.serviceId, loadServices]);

  // Load services when task changes
  useEffect(() => {
    if (selectedTaskId) {
      loadServices(selectedTaskId);
      // Clear selected service when task changes
      setValue('serviceId', '');
    } else {
      setServiceOptions([]);
    }
  }, [selectedTaskId, loadServices, setValue]);

  const onSubmit = async (data: ServiceConfiguration) => {
    setIsSubmitting(true);
    
    try {
      const url = isEdit 
        ? `/api/service-configurations/${defaultValues?.id}`
        : '/api/service-configurations';
      
      const method = isEdit ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(
          isEdit 
            ? "Service configuration updated successfully" 
            : "Service configuration created successfully"
        );
        
        // Redirect to the configuration details page or back to list
        if (isEdit) {
          router.push(`/admin/service-configurations/${defaultValues?.id}`);
        } else {
          router.push(`/admin/service-configurations/${result.data.id}`);
        }
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || "Failed to save service configuration");
      }
    } catch (error) {
      console.error('Submit error:', error);
      toast.error("An error occurred while saving");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
            
            <div className="space-y-4">
              <Input
                {...register("name", { required: "Configuration name is required" })}
                label="Configuration Name"
                placeholder="e.g., Car Wash Premium Template"
                isInvalid={!!errors.name}
                errorMessage={errors.name?.message}
              />
              
              <Textarea
                {...register("description")}
                label="Description"
                placeholder="Brief description of this service configuration template"
                maxRows={3}
              />

              {/* Task Selection */}
              <Select
                selectedKeys={selectedTaskId ? new Set([selectedTaskId]) : new Set()}
                onSelectionChange={(keys) => {
                  const taskId = Array.from(keys)[0] as string;
                  setSelectedTaskId(taskId);
                  const selectedTask = tasks.find(task => task.id === taskId);
                  setSelectedTaskName(selectedTask?.name || "");
                }}
                label="Task Category"
                placeholder="Select a task category..."
                isLoading={isLoadingTasks}
                className="w-full"
              >
                {(tasks.map((task) =>
                  <SelectItem key={task.id} value={task.id}>
                    {task.name}
                  </SelectItem>
                ) as any)}
                {tasks.length === 0 && !isLoadingTasks &&
                  <SelectItem key="no-tasks" value="" isDisabled>
                    No tasks available
                  </SelectItem>
                }
              </Select>

              {/* Service Selection - Only show when task is selected */}
              <Controller
                name="serviceId"
                control={control}
                rules={{ required: "Service type is required" }}
                render={({ field }) => (
                  <Autocomplete
                    {...field}
                    label="Service Type"
                    placeholder={selectedTaskId ? "Search and select a service..." : "Please select a task first"}
                    selectedKey={field.value}
                    onSelectionChange={(key) => {
                      field.onChange(key as string);
                    }}
                    onInputChange={(value) => {
                      if (selectedTaskId && value.length >= 2) {
                        loadServices(selectedTaskId, value);
                      }
                    }}
                    isDisabled={!selectedTaskId}
                    isLoading={isLoadingServices}
                    items={serviceOptions}
                    isInvalid={!!errors.serviceId}
                    errorMessage={errors.serviceId?.message}
                    className="w-full"
                  >
                    {(item) => (
                      <AutocompleteItem key={item.value} value={item.value}>
                        {item.label}
                      </AutocompleteItem>
                    )}
                  </Autocomplete>
                )}
              />

              {/* Helper text */}
              {selectedTaskId && serviceOptions.length === 0 && !isLoadingServices && (
                <p className="text-sm text-gray-500">
                  No services found for &quot;{selectedTaskName}&quot;. Try searching or contact an administrator.
                </p>
              )}

              <Controller
                name="active"
                control={control}
                render={({ field }) => (
                  <Switch
                    isSelected={field.value}
                    onValueChange={field.onChange}
                  >
                    Active Configuration
                  </Switch>
                )}
              />
            </div>
          </CardBody>
        </Card>

        {/* Information Card */}
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold mb-2">Next Steps</h3>
            <p className="text-gray-600 mb-4">
              After creating this service configuration, you&apos;ll be able to:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
              <li>Add duration options (30 min, 1 hour, 2 hours, etc.)</li>
              <li>Configure add-on services and equipment requirements</li>
              <li>Set pricing adjustments for different options</li>
              <li>Define personnel and location constraints</li>
              <li>Create custom options specific to this service</li>
            </ul>
          </CardBody>
        </Card>

        {/* Form Actions */}
        <Card>
          <CardBody>
            <div className="flex justify-end gap-3">
              <Button
                variant="light"
                onPress={() => router.push('/admin/service-configurations')}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                type="submit" 
                color="primary"
                isLoading={isSubmitting}
                isDisabled={isSubmitting}
              >
                {isSubmitting 
                  ? (isEdit ? "Updating..." : "Creating...") 
                  : (isEdit ? "Update Configuration" : "Create Configuration")
                }
              </Button>
            </div>
          </CardBody>
        </Card>
      </form>
    </div>
  );
}
