"use client";

import Link from "next/link";
import { <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

// Service Configuration interface
interface ServiceConfiguration {
  id: string;
  name: string;
  description?: string;
  serviceId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  optionCount: number;
  durationOptionCount: number;
  activeOptionCount: number;
}

interface Service {
  id: string;
  name: string;
  slug: string;
  active: boolean;
}

interface ServiceConfigurationListProps {
  data: ServiceConfiguration[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
  availableServices: Service[];
}

export default function ServiceConfigurationList({ data, meta, availableServices }: ServiceConfigurationListProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [loadingId, setLoadingId] = useState<string | null>(null);

  const handleDelete = async (configId: string) => {
    if (!confirm("Are you sure you want to delete this service configuration?")) {
      return;
    }

    setLoadingId(configId);
    try {
      const response = await fetch(`/api/service-configurations/${configId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success("Service configuration deleted successfully");
        startTransition(() => {
          router.refresh();
        });
      } else {
        toast.error("Failed to delete service configuration");
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error("An error occurred while deleting");
    } finally {
      setLoadingId(null);
    }
  };

  const handleToggleStatus = async (configId: string, currentStatus: boolean) => {
    setLoadingId(configId);
    try {
      const response = await fetch(`/api/service-configurations/${configId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ active: !currentStatus }),
      });

      if (response.ok) {
        toast.success(`Service configuration ${!currentStatus ? 'activated' : 'deactivated'}`);
        startTransition(() => {
          router.refresh();
        });
      } else {
        toast.error("Failed to update service configuration status");
      }
    } catch (error) {
      console.error('Status update error:', error);
      toast.error("An error occurred while updating status");
    } finally {
      setLoadingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const columns = [
    {
      name: "NAME",
      uid: "name",
      sortable: true,
      renderCell: (config: ServiceConfiguration) => (
        <div>
          <p className="font-medium">{config.name}</p>
          {config.description && (
            <p className="text-sm text-gray-500">{config.description}</p>
          )}
        </div>
      ),
    },
    {
      name: "SERVICE TYPE",
      uid: "serviceId",
      sortable: true,
      renderCell: (config: ServiceConfiguration) => (
        <Chip size="sm" variant="flat">
          {config.serviceId}
        </Chip>
      ),
    },
    {
      name: "OPTIONS",
      uid: "options",
      renderCell: (config: ServiceConfiguration) => (
        <div className="flex flex-col gap-1">
          <span className="text-sm">
            {config.optionCount} total options
          </span>
          <span className="text-xs text-gray-500">
            {config.durationOptionCount} duration, {config.activeOptionCount} active
          </span>
        </div>
      ),
    },
    {
      name: "STATUS",
      uid: "active",
      sortable: true,
      renderCell: (config: ServiceConfiguration) => (
        <Chip
          size="sm"
          color={config.active ? "success" : "default"}
          variant="flat"
        >
          {config.active ? "Active" : "Inactive"}
        </Chip>
      ),
    },
    {
      name: "CREATED",
      uid: "createdAt",
      sortable: true,
      renderCell: (config: ServiceConfiguration) => formatDate(config.createdAt),
    },
    {
      name: "ACTIONS",
      uid: "actions",
      renderCell: (config: ServiceConfiguration) => (
        <div className="flex items-center gap-2">
          <Button
            as={Link}
            href={`/admin/service-configurations/${config.id}`}
            size="sm"
            variant="light"
            isIconOnly
          >
            <Icon name="icon-[heroicons--eye-20-solid]" />
          </Button>
          <Button
            as={Link}
            href={`/admin/service-configurations/${config.id}/edit`}
            size="sm"
            variant="light"
            isIconOnly
          >
            <Icon name="icon-[heroicons--pencil-20-solid]" />
          </Button>
          <Button
            as={Link}
            href={`/admin/service-configurations/${config.id}/options`}
            size="sm"
            variant="light"
            isIconOnly
          >
            <Icon name="icon-[heroicons--cog-6-tooth-20-solid]" />
          </Button>
          <Button
            size="sm"
            variant="light"
            color={config.active ? "warning" : "success"}
            onPress={() => handleToggleStatus(config.id, config.active)}
            isLoading={loadingId === config.id}
            isIconOnly
          >
            <Icon name="icon-[heroicons--power-20-solid]" />
          </Button>
          <Button
            size="sm"
            variant="light"
            color="danger"
            onPress={() => handleDelete(config.id)}
            isLoading={loadingId === config.id}
            isIconOnly
          >
            <Icon name="icon-[heroicons--trash-20-solid]" />
          </Button>
        </div>
      ),
    },
  ];

  // Generate service filter values from available services
  const serviceFilterValues = availableServices
    .filter(service => service.active)
    .map(service => ({
      name: service.name,
      value: service.slug || service.id,
    }));

  const filter = [
    {
      column: "active",
      displayName: "Status",
      values: [
        { name: "Active", value: "true" },
        { name: "Inactive", value: "false" },
      ],
    },
    ...(serviceFilterValues.length > 0 ? [{
      column: "serviceId",
      displayName: "Service Type",
      values: serviceFilterValues,
    }] : []),
  ];

  return (
    <CustomTable
      title="Service Configurations"
      columns={columns}
      data={data}
      meta={meta}
      filter={filter}
      action={
        <div className="flex items-center gap-3">
          <Button
            as={Link}
            href="/admin/service-configurations/create"
            color="primary"
            startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          >
            Create Configuration
          </Button>
        </div>
      }
      isLoading={isPending}
    />
  );
}
