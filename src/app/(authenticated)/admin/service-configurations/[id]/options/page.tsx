import { auth } from "@/auth";
import { redirect } from "next/navigation";
import ConfigurationOptionsClient from "./configuration-options-client";

export const metadata = {
  title: "Configuration Options",
  description: "Manage service configuration options",
};

export default async function ConfigurationOptionsPage({
  params
}: {
  params: { id: string }
}) {
  const session = await auth();

  if (!session?.user) {
    redirect('/login');
  }

  // Check if user has admin role
  if (!session.user.roles.some(role => role.name === "admin")) {
    redirect('/dashboard');
  }

  return (
    <ConfigurationOptionsClient
      configurationId={params.id}
      user={session.user}
    />
  );
}
