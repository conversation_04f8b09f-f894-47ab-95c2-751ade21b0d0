"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import {
  Button,
  Input,
  Select,
  SelectItem,
  Card,
  CardBody,
  Chip,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  useDisclosure,
  <PERSON><PERSON>,
  Tabs,
  Tab,
} from "@nextui-org/react";
import { Icon } from "@/components/icon";
import toast from "react-hot-toast";
import ConfigurationOptionForm from "@/components/admin/service-configurations/configuration-option-form";

// Service Configuration interface
interface ServiceConfiguration {
  id: string;
  name: string;
  description?: string;
  serviceId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  optionCount: number;
  durationOptionCount: number;
  activeOptionCount: number;
}

// Configuration Option interface
interface ConfigurationOption {
  id: string;
  serviceConfigurationId: string;
  name: string;
  type: string;
  description?: string;
  priceAdjustment: number;
  durationId?: string;
  isDefault: boolean;
  sortOrder: number;
  constraints: object;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  duration?: {
    id: string;
    name: string;
    minutes: number;
  };
}

interface User {
  id: string;
  roles: Array<{ name: string }>;
}

interface ConfigurationOptionsClientProps {
  configurationId: string;
  user: User;
}

// Option types from the API documentation
const OPTION_TYPES = [
  { key: "duration", label: "Duration" },
  { key: "location", label: "Location" },
  { key: "personnel", label: "Personnel" },
  { key: "equipment", label: "Equipment" },
  { key: "delivery_method", label: "Delivery Method" },
  { key: "expertise_level", label: "Expertise Level" },
  { key: "add_on", label: "Add-on Service" },
  { key: "scheduling", label: "Scheduling" },
  { key: "custom", label: "Custom" },
];

export default function ConfigurationOptionsClient({
  configurationId,
  user
}: ConfigurationOptionsClientProps) {
  const [configuration, setConfiguration] = useState<ServiceConfiguration | null>(null);
  const [options, setOptions] = useState<ConfigurationOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedOption, setSelectedOption] = useState<ConfigurationOption | null>(null);

  // Fetch configuration and options data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch configuration and options in parallel
        const [configResponse, optionsResponse] = await Promise.all([
          fetch(`/api/service-configurations/${configurationId}`),
          fetch(`/api/service-configurations/${configurationId}/options`)
        ]);

        if (configResponse.ok) {
          const configResult = await configResponse.json();
          if (configResult.success) {
            setConfiguration(configResult.data);
          }
        }

        if (optionsResponse.ok) {
          const optionsResult = await optionsResponse.json();
          if (optionsResult.success) {
            setOptions(optionsResult.data || []);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        toast.error('Failed to load configuration options');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [configurationId]);

  // Show loading state
  if (loading || !configuration) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Spinner size="lg" />
          <p className="mt-4 text-gray-600">Loading configuration options...</p>
        </div>
      </div>
    );
  }

  // Filter options based on search and filters
  const filteredOptions = options.filter(option => {
    const matchesSearch = option.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         option.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = typeFilter === "all" || option.type === typeFilter;
    const matchesStatus = statusFilter === "all" || 
                         (statusFilter === "active" && option.active) ||
                         (statusFilter === "inactive" && !option.active);
    
    return matchesSearch && matchesType && matchesStatus;
  });

  // Group options by type
  const optionsByType = filteredOptions.reduce((acc, option) => {
    if (!acc[option.type]) {
      acc[option.type] = [];
    }
    acc[option.type].push(option);
    return acc;
  }, {} as Record<string, ConfigurationOption[]>);

  // Handle successful option creation
  const handleOptionCreated = (newOption: any) => {
    setOptions(prev => [...prev, newOption]);
  };

  const handleDelete = async (optionId: string) => {
    if (!confirm("Are you sure you want to delete this option?")) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/service-configurations/${configurationId}/options/${optionId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setOptions(prev => prev.filter(option => option.id !== optionId));
        toast.success("Option deleted successfully");
      } else {
        toast.error("Failed to delete option");
      }
    } catch (error) {
      console.error('Delete error:', error);
      toast.error("An error occurred while deleting");
    } finally {
      setLoading(false);
    }
  };




  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getTypeLabel = (type: string) => {
    const optionType = OPTION_TYPES.find(t => t.key === type);
    return optionType ? optionType.label : type;
  };

  const typeFilterItems = [
    { key: "all", label: "All Types" },
    ...OPTION_TYPES
  ];

  return (
    <div className="page-content p-5">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Button
              as={Link}
              href="/admin/service-configurations"
              variant="light"
              size="sm"
              startContent={<Icon name="icon-[heroicons--arrow-left-20-solid]" />}
            >
              Back
            </Button>
            <span className="text-gray-400">/</span>
            <span className="text-sm text-gray-600">{configuration.name}</span>
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Configuration Options</h1>
          <p className="text-gray-600 mt-1">
            Manage duration options, add-ons, and service configurations for &quot;{configuration.name}&quot;
          </p>
        </div>
        
        <Button
          color="primary"
          onPress={onOpen}
          startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
        >
          Add Option
        </Button>
      </div>

      {/* Configuration Summary */}
      <Card className="mb-6">
        <CardBody>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-sm text-gray-600">Service Type</p>
              <Chip size="sm" variant="flat">{configuration.serviceId}</Chip>
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Options</p>
              <p className="font-semibold">{configuration.optionCount}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Duration Options</p>
              <p className="font-semibold">{configuration.durationOptionCount}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Active Options</p>
              <p className="font-semibold">{configuration.activeOptionCount}</p>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Filters */}
      <Card className="mb-6">
        <CardBody>
          <div className="flex flex-col sm:flex-row gap-4">
            <Input
              placeholder="Search options..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startContent={<Icon name="icon-[heroicons--magnifying-glass-20-solid]" />}
              className="sm:max-w-xs"
            />
            
            <Select
              placeholder="Filter by type"
              selectedKeys={typeFilter ? new Set([typeFilter]) : new Set()}
              onSelectionChange={(keys) => {
                const selectedValue = Array.from(keys)[0] as string;
                setTypeFilter(selectedValue || "all");
              }}
              className="sm:max-w-xs"
              items={typeFilterItems}
            >
              {(item) => (
                <SelectItem key={item.key} value={item.key}>
                  {item.label}
                </SelectItem>
              )}
            </Select>

            <Select
              placeholder="Filter by status"
              selectedKeys={statusFilter ? new Set([statusFilter]) : new Set()}
              onSelectionChange={(keys) => {
                const selectedValue = Array.from(keys)[0] as string;
                setStatusFilter(selectedValue || "all");
              }}
              className="sm:max-w-xs"
            >
              <SelectItem key="all" value="all">All Status</SelectItem>
              <SelectItem key="active" value="active">Active</SelectItem>
              <SelectItem key="inactive" value="inactive">Inactive</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Results Summary */}
      <div className="mb-4 flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredOptions.length} of {options.length} options
        </p>
      </div>

      {/* Options organized by type */}
      <div className="space-y-6">
        {Object.keys(optionsByType).length === 0 ? (
          <Card>
            <CardBody className="text-center py-12">
              <Icon name="icon-[heroicons--cog-6-tooth-20-solid]" classNames="w-12 h-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No options found</h3>
              <p className="text-gray-600 mb-4">
                {options.length === 0 
                  ? "This configuration doesn't have any options yet. Add your first option to get started."
                  : "No options match your current filters. Try adjusting your search criteria."
                }
              </p>
              <Button
                color="primary"
                onPress={onOpen}
                startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
              >
                Add First Option
              </Button>
            </CardBody>
          </Card>
        ) : (
          Object.entries(optionsByType).map(([type, typeOptions]) => (
            <Card key={type}>
              <CardBody>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">{getTypeLabel(type)} Options</h3>
                  <Chip size="sm" variant="flat">{typeOptions.length}</Chip>
                </div>
                
                <Table aria-label={`${type} options table`}>
                  <TableHeader>
                    <TableColumn>NAME</TableColumn>
                    <TableColumn>PRICE ADJUSTMENT</TableColumn>
                    <TableColumn>DEFAULT</TableColumn>
                    <TableColumn>STATUS</TableColumn>
                    <TableColumn>SORT ORDER</TableColumn>
                    <TableColumn>ACTIONS</TableColumn>
                  </TableHeader>
                  <TableBody 
                    emptyContent="No options found"
                    isLoading={loading}
                    loadingContent={<Spinner />}
                  >
                    {typeOptions.map((option) => (
                      <TableRow key={option.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{option.name}</p>
                            {option.description && (
                              <p className="text-sm text-gray-500">{option.description}</p>
                            )}
                            {option.duration && (
                              <p className="text-xs text-blue-600">
                                Duration: {option.duration.minutes} minutes
                              </p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            color={option.priceAdjustment > 0 ? "warning" : option.priceAdjustment < 0 ? "success" : "default"}
                            variant="flat"
                          >
                            {option.priceAdjustment > 0 ? '+' : ''}${option.priceAdjustment}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          {option.isDefault && (
                            <Chip size="sm" color="primary" variant="flat">
                              Default
                            </Chip>
                          )}
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            color={option.active ? "success" : "default"}
                            variant="flat"
                          >
                            {option.active ? "Active" : "Inactive"}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          {option.sortOrder}
                        </TableCell>
                        <TableCell>
                          <Dropdown>
                            <DropdownTrigger>
                              <Button
                                isIconOnly
                                size="sm"
                                variant="light"
                              >
                                <Icon name="icon-[heroicons--ellipsis-vertical-20-solid]" />
                              </Button>
                            </DropdownTrigger>
                            <DropdownMenu>
                              <DropdownItem
                                key="edit"
                                startContent={<Icon name="icon-[heroicons--pencil-20-solid]" />}
                              >
                                Edit Option
                              </DropdownItem>
                              <DropdownItem
                                key="toggle-status"
                                isDisabled
                                startContent={<Icon name="icon-[heroicons--power-20-solid]" />}
                              >
                                {option.active ? "Deactivate" : "Activate"} (Coming Soon)
                              </DropdownItem>
                              <DropdownItem
                                key="delete"
                                className="text-danger"
                                color="danger"
                                onPress={() => handleDelete(option.id)}
                                startContent={<Icon name="icon-[heroicons--trash-20-solid]" />}
                              >
                                Delete
                              </DropdownItem>
                            </DropdownMenu>
                          </Dropdown>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardBody>
            </Card>
          ))
        )}
      </div>

      {/* Add Option Form Modal */}
      <ConfigurationOptionForm
        isOpen={isOpen}
        onClose={onClose}
        configurationId={configurationId}
        onSuccess={handleOptionCreated}
        existingOptionsCount={options.length}
      />
    </div>
  );
}
