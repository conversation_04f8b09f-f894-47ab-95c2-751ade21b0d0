import { auth } from "@/auth";
import { redirect } from "next/navigation";
import ServiceConfigurationEditClient from "./edit-client";

export const metadata = {
  title: "Edit Service Configuration",
  description: "Edit an existing service configuration template",
};

export default async function EditServiceConfigurationPage({
  params
}: {
  params: { id: string }
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  return <ServiceConfigurationEditClient configurationId={params.id} />;
}
