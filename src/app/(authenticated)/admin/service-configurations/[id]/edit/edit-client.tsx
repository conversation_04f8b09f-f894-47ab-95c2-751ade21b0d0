'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { AdminPageLayout, AdminActionButtons } from "@/components/admin/common/admin-page-layout";
import ServiceConfigurationForm from "../../components/service-configuration-form";

// Service Configuration interface
interface ServiceConfiguration {
  id: string;
  name: string;
  description?: string;
  serviceId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ServiceConfigurationEditClientProps {
  configurationId: string;
}

export default function ServiceConfigurationEditClient({ configurationId }: ServiceConfigurationEditClientProps) {
  const [configuration, setConfiguration] = useState<ServiceConfiguration | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const fetchConfiguration = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/service-configurations/${configurationId}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch configuration');
        }
        
        const result = await response.json();
        
        if (result.success) {
          setConfiguration(result.data);
        } else {
          throw new Error(result.error || 'Failed to load configuration');
        }
      } catch (err) {
        console.error('Error fetching configuration:', err);
        setError(err instanceof Error ? err.message : 'Failed to load configuration');
      } finally {
        setLoading(false);
      }
    };

    fetchConfiguration();
  }, [configurationId]);

  if (loading) {
    return (
      <AdminPageLayout
        title="Edit Service Configuration"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Service Configurations", href: "/admin/service-configurations" },
          { label: "Loading...", href: "#" },
          { label: "Edit" }
        ]}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading configuration...</p>
          </div>
        </div>
      </AdminPageLayout>
    );
  }

  if (error || !configuration) {
    return (
      <AdminPageLayout
        title="Edit Service Configuration"
        breadcrumbs={[
          { label: "Admin", href: "/admin" },
          { label: "Service Configurations", href: "/admin/service-configurations" },
          { label: "Error", href: "#" },
          { label: "Edit" }
        ]}
      >
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-red-500 text-xl mb-4">⚠️</div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              Failed to Load Configuration
            </h3>
            <p className="text-gray-600 mb-4">
              {error || 'The service configuration could not be found.'}
            </p>
            <button
              onClick={() => router.push('/admin/service-configurations')}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
            >
              Back to Configurations
            </button>
          </div>
        </div>
      </AdminPageLayout>
    );
  }

  const actionButtons = (
    <>
      <AdminActionButtons.Back
        href={`/admin/service-configurations/${configurationId}`}
        label="Back to Configuration"
      />
      <AdminActionButtons.Back
        href="/admin/service-configurations"
        label="Back to List"
      />
    </>
  );

  return (
    <AdminPageLayout
      title="Edit Service Configuration"
      subtitle={`Update the service configuration template: ${configuration.name}`}
      breadcrumbs={[
        { label: "Admin", href: "/admin" },
        { label: "Service Configurations", href: "/admin/service-configurations" },
        { label: configuration.name, href: `/admin/service-configurations/${configurationId}` },
        { label: "Edit" }
      ]}
      actions={actionButtons}
    >
      <div className="max-w-4xl">
        <ServiceConfigurationForm 
          defaultValues={configuration}
          isEdit={true}
        />
      </div>
    </AdminPageLayout>
  );
}
