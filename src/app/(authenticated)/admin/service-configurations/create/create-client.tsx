"use client";

import { AdminPageLayout } from "@/components/admin/common/admin-page-layout";
import ServiceConfigurationForm from "../components/service-configuration-form";
import { Button } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import Link from "next/link";

export default function CreateServiceConfigurationClient() {
  return (
    <AdminPageLayout
      title="Create Service Configuration"
      subtitle="Create a new reusable service template with duration and option combinations"
      breadcrumbs={[
        { label: "Admin", href: "/admin" },
        { label: "Service Configurations", href: "/admin/service-configurations" },
        { label: "Create" }
      ]}
      actions={
        <Button
          as={Link}
          href="/admin/service-configurations"
          variant="light"
          startContent={<Icon name="icon-[heroicons--arrow-left-20-solid]" />}
        >
          Back to Configurations
        </Button>
      }
    >
      <div className="max-w-4xl">
        <ServiceConfigurationForm />
      </div>
    </AdminPageLayout>
  );
}
