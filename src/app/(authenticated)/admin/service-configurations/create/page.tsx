import { auth } from "@/auth";
import { redirect } from "next/navigation";
import CreateServiceConfigurationClient from "./create-client";

export const metadata = {
  title: "Create Service Configuration",
  description: "Create a new reusable service template with duration and option combinations",
};

export default async function CreateServiceConfigurationPage() {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  return <CreateServiceConfigurationClient />;
}
