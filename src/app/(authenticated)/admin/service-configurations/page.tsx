import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import ServiceConfigurationList from "./components/service-configuration-list";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

// Service Configuration interface based on API response
interface ServiceConfiguration {
  id: string;
  name: string;
  description?: string;
  serviceId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  optionCount: number;
  durationOptionCount: number;
  activeOptionCount: number;
}

interface ServiceConfigurationResponse {
  success: boolean;
  data: {
    data: ServiceConfiguration[];
    meta: {
      total: number;
      per_page: number;
      current_page: number;
      last_page: number;
    };
  };
}

const fetchServiceConfigurations = cache(
  () => api.get<ServiceConfigurationResponse>('service-options/service-configurations'),
);

// Fetch available services for filter options
const fetchServices = cache(
  () => api.get<{ success: boolean; data: Array<{ id: string; name: string; slug: string; active: boolean }> }>('services'),
);

export const generateMetadata = async () => {
  return {
    title: "Service Configuration Management",
    description: "Manage reusable service templates with duration and option combinations",
    keywords: ["admin", "service", "configurations", "templates", "management"],
  };
};

export default async function AdminServiceConfigurationsPage() {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch service configurations data and available services
  const [configurationsResponse, servicesResponse] = await Promise.all([
    fetchServiceConfigurations().catch(() => null),
    fetchServices().catch(() => null),
  ]);

  // Fallback demo data if API is not available
  const demoConfigurations: ServiceConfiguration[] = [
    {
      id: "demo-1",
      name: "Car Wash Premium Template",
      description: "Premium car wash service with multiple duration options",
      serviceId: "car-wash",
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      optionCount: 5,
      durationOptionCount: 3,
      activeOptionCount: 4,
    },
    {
      id: "demo-2",
      name: "Oil Change Standard",
      description: "Standard oil change service configuration",
      serviceId: "oil-change",
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      optionCount: 3,
      durationOptionCount: 2,
      activeOptionCount: 3,
    },
    {
      id: "demo-3",
      name: "Brake Service Complete",
      description: "Complete brake service with inspection and replacement options",
      serviceId: "brake-service",
      active: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      optionCount: 7,
      durationOptionCount: 4,
      activeOptionCount: 5,
    },
  ];

  const demoMeta = {
    total: 3,
    per_page: 10,
    current_page: 1,
    last_page: 1,
  };

  return (
    <div className="page-content p-5">
      <ServiceConfigurationList
        data={configurationsResponse?.data?.data || demoConfigurations}
        meta={configurationsResponse?.data?.meta || demoMeta}
        availableServices={servicesResponse?.data || []}
      />
    </div>
  );
}
