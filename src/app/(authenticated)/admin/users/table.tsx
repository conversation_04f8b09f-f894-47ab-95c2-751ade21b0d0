"use client";

import { useState } from "react";
import Link from "next/link";
import { CustomTable } from "@/components/custom-table";
import { ConfirmDialog } from "@/components/ui/dialog";
import { Icon } from "@/components/icon";
import toast from "react-hot-toast";

interface User {
  id: string;
  idpass: string;
  title: string;
  firstName: string;
  lastName: string;
  gender: string;
  dob: string;
  otp: string | null;
  email: string;
  phone: string;
  rememberMeToken: string | null;
  details: string | null;
  location: any;
  geom: string | null;
  avatar: any;
  meta: {
    username: string | null;
    online: boolean;
  };
  status: string;
  emailVerifiedAt: string | null;
  phoneVerifiedAt: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  roles: Role[];
  name: string;
  avatarUrl: string;
  initials: string;
  primaryDepartment: any;
  activeDepartments: any[];
  averageSkillLevel: number;
  canSupervise: boolean;
}

interface Role {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface UsersTableProps {
  users: User[];
  meta?: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
    firstPage: number;
    firstPageUrl: string;
    lastPageUrl: string;
    nextPageUrl?: string;
    previousPageUrl?: string;
  };
  deleteUser: (id: string) => Promise<{ success: boolean; error?: string }>;
}

export default function UsersTable({
  users,
  meta,
  deleteUser,
}: UsersTableProps) {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  // Edit functionality is now handled by Link navigation

  const handleDeleteClick = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!selectedUser) return;

    try {
      const result = await deleteUser(selectedUser.id);
      if (result.success) {
        toast.success("User deleted successfully");
        setDeleteDialogOpen(false);
        setSelectedUser(null);
      } else {
        toast.error(result.error || "Failed to delete user");
      }
    } catch (error) {
      toast.error("Failed to delete user");
    }
  };

  // TODO: Add status filtering functionality if needed

  const columns = [
    {
      name: 'User',
      uid: 'user',
      sortable: true,
      renderCell: (user: User) => (
        <Link href={`/admin/users/${user.id}`} className="block">
          <div className="flex items-center space-x-3 hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors cursor-pointer">
            <div className="flex-shrink-0">
              <img
                className="h-8 w-8 rounded-full"
                src={user.avatarUrl}
                alt={user.name}
              />
            </div>
            <div className="min-w-0 flex-1">
              <div className="text-sm font-medium text-gray-900">
                {user.name}
              </div>
              <div className="text-sm text-gray-500">{user.email}</div>
            </div>
          </div>
        </Link>
      ),
    },
    {
      name: 'Phone',
      uid: 'phone',
      renderCell: (user: User) => (
        <div className="text-sm text-gray-900">
          {user.phone || (
            <span className="text-gray-400 italic">No phone</span>
          )}
        </div>
      ),
    },
    {
      name: 'Status',
      uid: 'status',
      renderCell: (user: User) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
          user.status === 'Active'
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {user.status}
        </span>
      ),
    },
    {
      name: 'Roles',
      uid: 'roles',
      renderCell: (user: User) => (
        <div className="flex flex-wrap gap-1">
          {user.roles && user.roles.length > 0 ? (
            user.roles.map((role) => (
              <span
                key={role.id}
                className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                  role.name === 'vendor' ? 'bg-blue-100 text-blue-800' :
                  role.name === 'customer' ? 'bg-green-100 text-green-800' :
                  role.name === 'manager' ? 'bg-purple-100 text-purple-800' :
                  role.name === 'barista' ? 'bg-orange-100 text-orange-800' :
                  'bg-gray-100 text-gray-800'
                }`}
              >
                {role.name}
              </span>
            ))
          ) : (
            <span className="text-gray-400 italic text-sm">No roles</span>
          )}
        </div>
      ),
    },
    {
      name: 'Online',
      uid: 'online',
      renderCell: (user: User) => (
        <div className="flex items-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${
            user.meta.online ? 'bg-green-400' : 'bg-gray-300'
          }`}></div>
          <span className="text-sm text-gray-600">
            {user.meta.online ? 'Online' : 'Offline'}
          </span>
        </div>
      ),
    },
    {
      name: 'Joined',
      uid: 'joined',
      sortable: true,
      renderCell: (user: User) => (
        <div className="text-sm text-gray-900">
          {new Date(user.createdAt).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          })}
        </div>
      ),
    },
    {
      name: 'Actions',
      uid: 'actions',
      renderCell: (user: User) => (
        <div className="flex w-fit items-center gap-5">
          <Link href={`/admin/users/${user.id}`}>
            <Icon
              name="icon-[mingcute--eye-line]"
              classNames="text-primary"
            />
          </Link>
          <Link href={`/admin/users/${user.id}/edit`}>
            <Icon
              name="icon-[mage--edit-pen-fill]"
              classNames="text-primary"
            />
          </Link>
          <button
            onClick={() => handleDeleteClick(user)}
            className="text-red-500"
          >
            <Icon name="icon-[mingcute--delete-2-line]" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <>
      <CustomTable
        title={`Total ${meta?.total || 0} users`}
        columns={columns}
        data={users}
        meta={meta as any}
        filter={{
          column: "status",
          displayName: "Filter by Status",
          values: [
            { name: "All Users", value: "all" },
            { name: "Active", value: "Active" },
            { name: "Inactive", value: "Inactive" },
            { name: "Online", value: "online" },
            { name: "Offline", value: "offline" }
          ]
        }}

      />

      {/* TODO: Add Create and Edit User Modals */}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialogOpen}
        onClose={() => {
          setDeleteDialogOpen(false);
          setSelectedUser(null);
        }}
        onConfirm={handleDeleteConfirm}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setSelectedUser(null);
        }}
        title="Delete User"
        message={`Are you sure you want to delete "${selectedUser?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="error"
      />
    </>
  );
}
