import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import UsersTable from "./table";

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  active: boolean;
  emailVerifiedAt?: string;
  createdAt: string;
  updatedAt: string;
  roles?: Role[];
}

interface Role {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginatedUsers {
  data: User[];
  meta: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
    firstPage: number;
    firstPageUrl: string;
    lastPageUrl: string;
    nextPageUrl?: string;
    previousPageUrl?: string;
  };
}

export const metadata = {
  title: "Users Management",
  description: "Manage platform users",
  keywords: ["users", "management", "admin"],
};

export default async function UsersPage({
  searchParams,
}: {
  searchParams?: Record<string, string>;
}) {
  // Build query parameters
  const queryParams = new URLSearchParams();
  if (searchParams?.page) queryParams.set('page', searchParams.page);
  if (searchParams?.per) queryParams.set('per', searchParams.per);
  if (searchParams?.order) queryParams.set('order', searchParams.order);
  if (searchParams?.sort) queryParams.set('sort', searchParams.sort);
  if (searchParams?.s) queryParams.set('s', searchParams.s);
  if (searchParams?.status) queryParams.set('status', searchParams.status);

  const queryString = queryParams.toString();
  const apiUrl = queryString ? `users?${queryString}` : 'users';

  // Fetch users from API
  const users = await api.get<PaginatedUsers>(apiUrl);



  // Delete user action
  const deleteUser = async (id: string) => {
    "use server";
    
    try {
      await api.delete(`users/${id}`);
      revalidatePath('/admin/users');
      return { success: true };
    } catch (error) {
      console.error('Error deleting user:', error);
      return { success: false, error: 'Failed to delete user' };
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Users Management</h1>
        <p className="text-gray-600 mt-1">Manage platform users and their access</p>
      </div>

      <UsersTable
        users={users?.data || []}
        meta={users?.meta}
        deleteUser={deleteUser}
      />
    </div>
  );
}
