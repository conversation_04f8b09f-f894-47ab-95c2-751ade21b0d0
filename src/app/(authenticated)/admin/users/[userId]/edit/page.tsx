import { api } from "@/lib/api";
import { Metadata } from "next";
import { revalidatePath } from "next/cache";
import { redirect } from "next/navigation";
import UserEditForm from "./form";

interface User {
  id: string;
  idpass: string;
  title?: string;
  firstName: string;
  lastName: string;
  name: string;
  gender?: string;
  dob?: string;
  email: string;
  phone: string;
  status: string;
  emailVerifiedAt?: string;
  phoneVerifiedAt?: string;
  createdAt: string;
  updatedAt: string;
  roles?: Role[];
}

interface Role {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

const getUser = async (userId: string) => {
  try {
    return await api.get<User>(`users/${userId}`);
  } catch (error) {
    throw new Error('User not found');
  }
};

export const generateMetadata = async ({
  params,
}: {
  params: { userId: string };
}): Promise<Metadata> => {
  const user = await getUser(params.userId);

  return {
    title: `Edit ${user?.name} - User Management`,
    description: `Edit details for user ${user?.name}`,
  };
};

export default async function EditUserPage({
  params,
}: {
  params: { userId: string };
}) {
  const user = await getUser(params.userId);

  if (!user) {
    redirect('/admin/users');
  }

  // Update user action
  const updateUser = async (formData: FormData) => {
    "use server";

    try {
      const data = {
        title: formData.get('title') as string,
        firstName: formData.get('firstName') as string,
        lastName: formData.get('lastName') as string,
        gender: formData.get('gender') as string,
        dob: formData.get('dob') as string,
        email: formData.get('email') as string,
        phone: formData.get('phone') as string,
        status: formData.get('status') as string,
      };

      // Only include password if provided
      const password = formData.get('password') as string;
      if (password && password.trim()) {
        (data as any).password = password;
      }

      await api.put(`users/${params.userId}`, data);
      revalidatePath(`/admin/users/${params.userId}`);
      revalidatePath('/admin/users');

      return { success: true };
    } catch (error) {
      console.error('Error updating user:', error);
      return { success: false, error: 'Failed to update user' };
    }
  };

  return (
    <div className="p-6">
      <UserEditForm user={user} onSubmit={updateUser} />
    </div>
  );
}
