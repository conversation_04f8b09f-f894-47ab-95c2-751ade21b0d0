import { api } from "@/lib/api";
import { Metadata } from "next";
import Link from "next/link";
import { Icon } from "@/components/icon";
import { ActionButton } from "@/components/ui/action-button";

interface User {
  id: string;
  idpass: string;
  title?: string;
  firstName: string;
  lastName: string;
  name: string;
  gender?: string;
  dob?: string;
  email: string;
  phone: string;
  avatar?: string;
  avatarUrl?: string;
  initials?: string;
  meta?: {
    username?: string;
    online: boolean;
  };
  status: string;
  emailVerifiedAt?: string;
  phoneVerifiedAt?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
  roles?: Role[];
  permissions?: Permission[];
  devices?: Device[];
  primaryDepartment?: any;
  activeDepartments?: any[];
  averageSkillLevel?: number;
  canSupervise?: boolean;
}

interface Role {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface Permission {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface Device {
  id: string;
  name: string;
  type: string;
}

const getUser = async (userId: string) => {
  try {
    return await api.get<User>(`users/${userId}`);
  } catch (error) {
    throw new Error('User not found');
  }
};

export const generateMetadata = async ({
  params,
}: {
  params: { userId: string };
}): Promise<Metadata> => {
  const user = await getUser(params.userId);

  return {
    title: `${user?.name} - User Details`,
    description: `View details for user ${user?.name}`,
  };
};

export default async function UserDetailPage({
  params,
}: {
  params: { userId: string };
}) {
  const user = await getUser(params.userId);

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">User Not Found</h1>
          <p className="text-gray-600 mb-6">The user you're looking for doesn't exist.</p>
          <Link href="/admin/users">
            <ActionButton variant="teal">
              Back to Users
            </ActionButton>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Link href="/admin/users">
              <ActionButton variant="outline" size="sm">
                <Icon name="icon-[mingcute--arrow-left-line]" classNames="w-4 h-4 mr-2" />
                Back to Users
              </ActionButton>
            </Link>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{user.name}</h1>
              <p className="text-gray-600 mt-1">User Details</p>
            </div>
            <div className="flex items-center space-x-3">
              <Link href={`/admin/users/${user.id}/edit`}>
                <ActionButton variant="teal">
                  <Icon name="icon-[mage--edit-pen-fill]" classNames="w-4 h-4 mr-2" />
                  Edit User
                </ActionButton>
              </Link>
            </div>
          </div>
        </div>

        {/* First Row: Basic Information & Account Information side by side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Basic Information */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">Basic Information</h2>
            </div>
            <div className="px-6 py-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Full Name</label>
                <p className="text-gray-900 font-medium">{user.name}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Title</label>
                <p className="text-gray-900">{user.title || 'Not specified'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">First Name</label>
                <p className="text-gray-900">{user.firstName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Last Name</label>
                <p className="text-gray-900">{user.lastName}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Gender</label>
                <p className="text-gray-900">{user.gender || 'Not specified'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Date of Birth</label>
                <p className="text-gray-900">
                  {user.dob ? new Date(user.dob).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  }) : 'Not provided'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Email</label>
                <p className="text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Phone</label>
                <p className="text-gray-900">{user.phone || 'Not provided'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Status</label>
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.status === 'Active'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                      user.status === 'Active' ? 'bg-green-400' : 'bg-red-400'
                    }`}></div>
                    {user.status}
                  </span>
                  {user.emailVerifiedAt && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      <Icon name="icon-[mingcute--check-circle-line]" classNames="w-3 h-3 mr-1" />
                      Email Verified
                    </span>
                  )}
                  {user.phoneVerifiedAt && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      <Icon name="icon-[mingcute--check-circle-line]" classNames="w-3 h-3 mr-1" />
                      Phone Verified
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Account Information */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">Account Information</h2>
            </div>
            <div className="px-6 py-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">User ID</label>
                <p className="text-gray-900 font-mono text-sm bg-gray-50 px-3 py-2 rounded-md border">{user.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">ID Pass</label>
                <p className="text-gray-900 font-mono text-sm bg-gray-50 px-3 py-2 rounded-md border">{user.idpass}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Initials</label>
                <p className="text-gray-900">{user.initials || 'Not available'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Email Verified</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.emailVerifiedAt
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                    user.emailVerifiedAt ? 'bg-green-400' : 'bg-red-400'
                  }`}></div>
                  {user.emailVerifiedAt ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Phone Verified</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.phoneVerifiedAt
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                    user.phoneVerifiedAt ? 'bg-green-400' : 'bg-red-400'
                  }`}></div>
                  {user.phoneVerifiedAt ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Online Status</label>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.meta?.online
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                    user.meta?.online ? 'bg-green-400' : 'bg-gray-400'
                  }`}></div>
                  {user.meta?.online ? 'Online' : 'Offline'}
                </span>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Created At</label>
                <p className="text-gray-900">
                  {new Date(user.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Last Updated</label>
                <p className="text-gray-900">
                  {new Date(user.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </p>
              </div>
              {user.emailVerifiedAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-2">Email Verified At</label>
                  <p className="text-gray-900">
                    {new Date(user.emailVerifiedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              )}
              {user.phoneVerifiedAt && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-2">Phone Verified At</label>
                  <p className="text-gray-900">
                    {new Date(user.phoneVerifiedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              )}
          </div>
        </div>
        </div>

        {/* Second Row: Additional Information full width */}
        <div className="mt-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">Additional Information</h2>
            </div>
            <div className="px-6 py-6 space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Avatar</label>
                <div className="flex items-center space-x-3">
                  {user.avatarUrl && (
                    <img
                      src={user.avatarUrl}
                      alt={`${user.name} avatar`}
                      className="w-12 h-12 rounded-full border-2 border-gray-200"
                    />
                  )}
                  <div>
                    <p className="text-gray-900 font-medium">{user.initials || 'N/A'}</p>
                    <p className="text-sm text-gray-500">Initials</p>
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Skills & Supervision</label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Average Skill Level</span>
                    <span className="text-sm font-medium text-gray-900">{user.averageSkillLevel || 0}/10</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Can Supervise</span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      user.canSupervise
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {user.canSupervise ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Departments & Devices</label>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Active Departments</span>
                    <span className="text-sm font-medium text-gray-900">
                      {user.activeDepartments && user.activeDepartments.length > 0
                        ? user.activeDepartments.length
                        : 0}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Registered Devices</span>
                    <span className="text-sm font-medium text-gray-900">
                      {user.devices && user.devices.length > 0
                        ? user.devices.length
                        : 0}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Third Row: Roles & Permissions full width */}
        <div className="mt-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="px-6 py-5 border-b border-gray-200 bg-gray-50">
              <h2 className="text-lg font-semibold text-gray-900">Roles & Permissions</h2>
            </div>
            <div className="px-6 py-6">
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-3">Roles</label>
                  {user.roles && user.roles.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {user.roles.map((role) => (
                        <span
                          key={role.id}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                        >
                          <Icon name="icon-[mingcute--user-security-line]" classNames="w-4 h-4 mr-2" />
                          {role.name}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No roles assigned</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-3">Permissions</label>
                  {user.permissions && user.permissions.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {user.permissions.map((permission) => (
                        <span
                          key={permission.id}
                          className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                        >
                          <Icon name="icon-[lucide--shield-check]" classNames="w-4 h-4 mr-2" />
                          {permission.name}
                        </span>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500">No specific permissions assigned</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
