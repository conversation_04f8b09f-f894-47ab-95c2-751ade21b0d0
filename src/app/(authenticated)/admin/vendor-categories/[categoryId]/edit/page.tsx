import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import VendorCategoryForm from '../../../vendor-types/components/VendorCategoryForm';
import { getVendorCategory } from '../../../vendor-types/actions';
import { Icon } from '@/components/icon';
import Link from 'next/link';

interface EditVendorCategoryPageProps {
  params: {
    categoryId: string;
  };
}

export default async function EditVendorCategoryPage({ params }: EditVendorCategoryPageProps) {
  const { data: vendorCategory, error } = await getVendorCategory(params.categoryId);

  if (error || !vendorCategory) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link
          href={`/admin/vendor-categories/${params.categoryId}/vendors`}
          className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
        >
          <Icon name="icon-[lucide--arrow-left]" classNames="text-gray-600" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Category</h1>
          <p className="text-gray-600 mt-1">
            Update details for "{vendorCategory.name}"
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <Suspense fallback={<div>Loading...</div>}>
            <VendorCategoryForm 
              vendorTypeId={vendorCategory.vendorTypeId}
              vendorCategory={vendorCategory}
              isEdit={true}
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
