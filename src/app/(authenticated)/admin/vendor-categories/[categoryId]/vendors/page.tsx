import { Suspense } from 'react';
import { getVendorsInCategory, getVendorCategory } from '../../../vendor-types/actions';
import VendorsInCategoryTable from '../../components/VendorsInCategoryTable';
import Link from 'next/link';
import { Icon } from '@/components/icon';
import { notFound } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';

interface PageProps {
  params: { categoryId: string };
  searchParams: Record<string, string>;
}

export default async function VendorsInCategoryPage({ params, searchParams }: PageProps) {
  const { categoryId } = params;
  
  // Fetch vendor category details
  const { data: vendorCategory, error: categoryError } = await getVendorCategory(categoryId);
  
  if (categoryError || !vendorCategory) {
    notFound();
  }

  // Fetch vendors in category
  const { data, meta, error } = await getVendorsInCategory(categoryId, searchParams);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <Icon name="icon-[lucide--alert-circle]" classNames="text-red-500 text-6xl mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Vendors</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href={`/admin/vendor-categories/${categoryId}/vendors`}
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <Link href="/admin/vendor-types" className="hover:text-primary">
          Vendor Types
        </Link>
        <Icon name="icon-[mingcute--right-line]" className="text-gray-400" />
        <Link
          href={`/admin/vendor-types/${vendorCategory.vendorTypeId}/categories`}
          className="hover:text-primary"
        >
          Categories
        </Link>
        <Icon name="icon-[mingcute--right-line]" className="text-gray-400" />
        <span className="text-gray-900 font-medium">{vendorCategory.name}</span>
      </nav>

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {vendorCategory.name} Vendors
          </h1>
          <p className="text-gray-600 mt-1">
            Vendors in the {vendorCategory.name} category
          </p>
        </div>
        <div className="flex items-center gap-3">
          <ActionButton variant="outline" asChild>
            <Link href={`/admin/vendor-types/${vendorCategory.vendorTypeId}/categories`}>
              <Icon name="icon-[mingcute--arrow-left-line]" />
              Back to Categories
            </Link>
          </ActionButton>
        </div>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <VendorsInCategoryTable data={data} meta={meta} />
      </Suspense>
    </div>
  );
}
