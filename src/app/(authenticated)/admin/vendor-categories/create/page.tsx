import { Suspense } from 'react';
import VendorCategoryForm from '../../vendor-types/components/VendorCategoryForm';
import { getVendorTypes } from '../../vendor-types/actions';
import { Icon } from '@/components/icon';
import Link from 'next/link';
import { ActionButton } from '@/components/ui/action-button';

export default async function CreateVendorCategoryPage() {
  const { data: vendorTypes, error } = await getVendorTypes();

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <Icon name="icon-[lucide--alert-circle]" classNames="text-red-500 text-6xl mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Vendor Types</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin/vendor-categories"
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Back to Categories
          </Link>
        </div>
      </div>
    );
  }

  if (!vendorTypes || vendorTypes.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <Icon name="icon-[lucide--info]" classNames="text-blue-500 text-6xl mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">No Vendor Types Available</h2>
          <p className="text-gray-600 mb-4">You need to create vendor types before adding categories.</p>
          <div className="flex items-center gap-3">
            <ActionButton asChild>
              <Link href="/admin/vendor-types/create">
                Create Vendor Type
              </Link>
            </ActionButton>
            <ActionButton variant="outline" asChild>
              <Link href="/admin/vendor-categories">
                Back to Categories
              </Link>
            </ActionButton>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <ActionButton variant="outline" size="icon" asChild>
          <Link href="/admin/vendor-categories">
            <Icon name="icon-[lucide--arrow-left]" />
          </Link>
        </ActionButton>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Vendor Category</h1>
          <p className="text-gray-600 mt-1">
            Add a new category to organize vendors within a type
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <Suspense fallback={<div>Loading...</div>}>
            <VendorCategoryForm
              vendorTypes={vendorTypes}
              redirectPath="/admin/vendor-categories"
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
