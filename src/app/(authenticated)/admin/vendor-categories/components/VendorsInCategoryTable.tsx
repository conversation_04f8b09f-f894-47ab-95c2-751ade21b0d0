'use client';

import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { Icon } from '@/components/icon';
import { User, Chip } from '@nextui-org/react';
import Link from 'next/link';
import { VendorInCategory } from '../../admin/vendor-types/types';
import { PaginationMeta } from '@/types/pagination';
import { imagePath } from '@/lib/api';
import { deleteVendor } from '@/actions/vendors';
import { showToastPromise } from '@/lib/toast-utils';

interface VendorsInCategoryTableProps {
  data: VendorInCategory[];
  meta?: PaginationMeta;
}

export default function VendorsInCategoryTable({ data, meta }: VendorsInCategoryTableProps) {
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  const handleDeleteClick = (vendor: VendorInCategory) => {
    openDeleteDialog(vendor.id, vendor.name, async () => {
      const formData = new FormData();
      formData.append('id', vendor.id);

      const result = await deleteVendor(formData);

      if (result.success) {
        showToastPromise(
          Promise.resolve(),
          'vendor',
          'delete'
        );
      } else {
        throw new Error(result.error || 'Failed to delete vendor');
      }
    });
  };

  return (
    <>
      <DeleteConfirmationDialog />
      <CustomTable
        title="Vendors"
        columns={[
          {
            name: "Vendor",
            uid: "name",
            sortable: true,
            renderCell: (vendor: VendorInCategory) => (
              <Link
                href={`/vendors/${vendor.id}`}
                className="flex items-center space-x-2"
              >
                <User
                  name={vendor.name}
                  description={vendor.email}
                  avatarProps={{
                    src: imagePath(vendor.logo?.url, null),
                    classNames: { img: "object-contain" },
                  }}
                  classNames={{
                    description: "text-default-600",
                    name: "font-bold",
                  }}
                />
              </Link>
            ),
          },
          {
            name: "Email",
            uid: "email",
            sortable: true,
          },
          {
            name: "Phone",
            uid: "phone",
            sortable: true,
          },
          {
            name: "Active",
            uid: "active",
            renderCell: (vendor: VendorInCategory) => (
              <Chip
                color={vendor.active ? "success" : "danger"}
                variant="flat"
                size="sm"
              >
                {vendor.active ? "Active" : "Inactive"}
              </Chip>
            ),
          },
          {
            name: "Featured",
            uid: "featured",
            renderCell: (vendor: VendorInCategory) => (
              <Chip
                color={vendor.featured ? "primary" : "default"}
                variant="flat"
                size="sm"
              >
                {vendor.featured ? "Featured" : "Regular"}
              </Chip>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (vendor: VendorInCategory) => (
              <div className="flex w-fit items-center gap-3">
                <Link href={`/vendors/${vendor.id}`}>
                  <Icon
                    name="icon-[mingcute--eye-line]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <button
                  onClick={() => handleDeleteClick(vendor)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
      />
    </>
  );
}
