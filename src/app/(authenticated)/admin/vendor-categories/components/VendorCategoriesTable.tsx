'use client';

import { VendorCategory } from '../../vendor-types/types';
import { Icon } from '@/components/icon';
import Link from 'next/link';
import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { deleteVendorCategory } from '../../vendor-types/actions';
import { showToastPromise } from '@/lib/toast-utils';
import { formatDate } from '@/lib/date';

interface VendorCategoriesTableProps {
  data: (VendorCategory & { vendorTypeName: string })[];
  meta: any;
}

export default function VendorCategoriesTable({ data, meta }: VendorCategoriesTableProps) {
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  const handleDeleteClick = (category: VendorCategory & { vendorTypeName: string }) => {
    openDeleteDialog(category.id, category.name, async () => {
      const formData = new FormData();
      formData.append('id', category.id);
      formData.append('vendorTypeId', category.vendorTypeId);

      const result = await deleteVendorCategory(formData);

      if (result.success) {
        showToastPromise(
          Promise.resolve(),
          'vendor-category',
          'delete'
        );
      } else {
        throw new Error(result.error || 'Failed to delete vendor category');
      }
    });
  };

  return (
    <>
      <DeleteConfirmationDialog />
      <CustomTable
        title="Vendor Categories"
        columns={[
          {
            name: "Category Name",
            uid: "name",
            sortable: true,
            renderCell: (category: VendorCategory & { vendorTypeName: string }) => (
              <Link
                href={`/admin/vendor-categories/${category.id}/vendors`}
                className="font-medium text-primary hover:underline"
              >
                {category.name}
              </Link>
            ),
          },
          {
            name: "Slug",
            uid: "slug",
            sortable: true,
          },
          {
            name: "Vendor Type",
            uid: "vendorTypeName",
            sortable: true,
            renderCell: (category: VendorCategory & { vendorTypeName: string }) => (
              <Link
                href={`/admin/vendor-types/${category.vendorTypeId}/categories`}
                className="text-primary hover:text-primary-600 font-medium"
              >
                {category.vendorTypeName}
              </Link>
            ),
          },
          {
            name: "Details",
            uid: "details",
            renderCell: (category: VendorCategory & { vendorTypeName: string }) => (
              <div className="max-w-xs truncate">
                {category.details || '-'}
              </div>
            ),
          },
          {
            name: "Created At",
            uid: "createdAt",
            sortable: true,
            renderCell: (category: VendorCategory & { vendorTypeName: string }) => (
              <span className="text-sm text-gray-600">
                {formatDate(category.createdAt, 'MMM dd, yyyy')}
              </span>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (category: VendorCategory & { vendorTypeName: string }) => (
              <div className="flex w-fit items-center gap-3">
                <Link href={`/admin/vendor-categories/${category.id}/vendors`}>
                  <Icon
                    name="icon-[mingcute--eye-line]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <Link href={`/admin/vendor-categories/${category.id}/edit`}>
                  <Icon
                    name="icon-[mage--edit-pen-fill]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <button
                  onClick={() => handleDeleteClick(category)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
      />
    </>
  );

}
