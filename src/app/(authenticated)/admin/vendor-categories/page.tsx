import { Suspense } from 'react';
import VendorCategoriesTable from './components/VendorCategoriesTable';
import { Icon } from '@/components/icon';
import Link from 'next/link';
import { getAllVendorCategories } from '../vendor-types/actions';
import { ActionButton } from '@/components/ui/action-button';

interface PageProps {
  searchParams: Record<string, string>;
}

export default async function VendorCategoriesPage({ searchParams }: PageProps) {
  const { data, meta, error } = await getAllVendorCategories(searchParams);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <Icon name="icon-[lucide--alert-circle]" classNames="text-red-500 text-6xl mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Vendor Categories</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin/vendor-categories"
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendor Categories</h1>
          <p className="text-gray-600 mt-1">
            Manage all vendor categories across all types
          </p>
        </div>
        <div className="flex items-center gap-3">
          <ActionButton asChild>
            <Link href="/admin/vendor-categories/create">
              <Icon name="icon-[mingcute--add-line]" />
              Add Category
            </Link>
          </ActionButton>
          <ActionButton variant="outline" asChild>
            <Link href="/admin/vendor-types">
              <Icon name="icon-[mingcute--settings-line]" />
              Manage Types
            </Link>
          </ActionButton>
        </div>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <VendorCategoriesTable data={data} meta={meta} />
      </Suspense>
    </div>
  );
}
