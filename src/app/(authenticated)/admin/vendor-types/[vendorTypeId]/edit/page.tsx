import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import VendorTypeForm from '../../components/VendorTypeForm';
import { getVendorType } from '../../actions';
import { Icon } from '@/components/icon';
import Link from 'next/link';

interface EditVendorTypePageProps {
  params: {
    vendorTypeId: string;
  };
}

export default async function EditVendorTypePage({ params }: EditVendorTypePageProps) {
  const { data: vendorType, error } = await getVendorType(params.vendorTypeId);

  if (error || !vendorType) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link
          href="/admin/vendor-types"
          className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
        >
          <Icon name="icon-[lucide--arrow-left]" classNames="text-gray-600" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Vendor Type</h1>
          <p className="text-gray-600 mt-1">
            Update details for "{vendorType.name}"
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <Suspense fallback={<div>Loading...</div>}>
            <VendorTypeForm vendorType={vendorType} isEdit={true} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
