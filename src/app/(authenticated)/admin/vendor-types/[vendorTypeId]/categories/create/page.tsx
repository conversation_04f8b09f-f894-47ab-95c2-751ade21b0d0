import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import VendorCategoryForm from '../../../components/VendorCategoryForm';
import { getVendorType } from '../../../actions';
import { Icon } from '@/components/icon';
import Link from 'next/link';
import { ActionButton } from '@/components/ui/action-button';

interface CreateVendorCategoryPageProps {
  params: {
    vendorTypeId: string;
  };
}

export default async function CreateVendorCategoryPage({ params }: CreateVendorCategoryPageProps) {
  const { data: vendorType, error } = await getVendorType(params.vendorTypeId);

  if (error || !vendorType) {
    return notFound();
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <ActionButton variant="outline" size="icon" asChild>
          <Link href={`/admin/vendor-types/${params.vendorTypeId}/categories`}>
            <Icon name="icon-[lucide--arrow-left]" />
          </Link>
        </ActionButton>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Category</h1>
          <p className="text-gray-600 mt-1">
            Add a new category to "{vendorType.name}"
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <Suspense fallback={<div>Loading...</div>}>
            <VendorCategoryForm vendorTypeId={params.vendorTypeId} />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
