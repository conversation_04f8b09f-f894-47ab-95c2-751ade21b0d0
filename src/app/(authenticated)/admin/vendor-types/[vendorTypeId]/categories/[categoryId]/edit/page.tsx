import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import VendorCategoryForm from '../../../../components/VendorCategoryForm';
import { getVendorType, getVendorCategory } from '../../../../actions';
import { Icon } from '@/components/icon';
import Link from 'next/link';

interface EditVendorCategoryPageProps {
  params: {
    vendorTypeId: string;
    categoryId: string;
  };
}

export default async function EditVendorCategoryPage({ params }: EditVendorCategoryPageProps) {
  const [vendorTypeResult, vendorCategoryResult] = await Promise.all([
    getVendorType(params.vendorTypeId),
    getVendorCategory(params.categoryId)
  ]);

  if (vendorTypeResult.error || !vendorTypeResult.data || 
      vendorCategoryResult.error || !vendorCategoryResult.data) {
    return notFound();
  }

  const vendorType = vendorTypeResult.data;
  const vendorCategory = vendorCategoryResult.data;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link
          href={`/admin/vendor-types/${params.vendorTypeId}/categories`}
          className="flex items-center justify-center w-10 h-10 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors"
        >
          <Icon name="icon-[lucide--arrow-left]" classNames="text-gray-600" />
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Category</h1>
          <p className="text-gray-600 mt-1">
            Update "{vendorCategory.name}" in "{vendorType.name}"
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <Suspense fallback={<div>Loading...</div>}>
            <VendorCategoryForm 
              vendorTypeId={params.vendorTypeId}
              vendorCategory={vendorCategory}
              isEdit={true}
            />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
