import { Suspense } from 'react';
import { getVendorCategories, getVendorType } from '../../actions';
import VendorCategoriesTable from '../../components/VendorCategoriesTable';
import Link from 'next/link';
import { Icon } from '@/components/icon';
import { notFound } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';

interface PageProps {
  params: { vendorTypeId: string };
  searchParams: Record<string, string>;
}

export default async function VendorCategoriesPage({ params, searchParams }: PageProps) {
  const { vendorTypeId } = params;
  
  // Fetch vendor type details
  const { data: vendorType, error: vendorTypeError } = await getVendorType(vendorTypeId);
  
  if (vendorTypeError || !vendorType) {
    notFound();
  }

  // Fetch vendor categories
  const { data, meta, error } = await getVendorCategories(vendorTypeId, searchParams);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <Icon name="icon-[lucide--alert-circle]" classNames="text-red-500 text-6xl mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Vendor Categories</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href={`/admin/vendor-types/${vendorTypeId}/categories`}
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <Link href="/admin/vendor-types" className="hover:text-primary">
          Vendor Types
        </Link>
        <Icon name="icon-[mingcute--right-line]" className="text-gray-400" />
        <span className="text-gray-900 font-medium">{vendorType.name}</span>
      </nav>

      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {vendorType.name} Categories
          </h1>
          <p className="text-gray-600 mt-1">
            Manage categories for {vendorType.name}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <ActionButton variant="outline" asChild>
            <Link href="/admin/vendor-types">
              <Icon name="icon-[mingcute--arrow-left-line]" />
              Back to Vendor Types
            </Link>
          </ActionButton>
          <ActionButton asChild>
            <Link href={`/admin/vendor-types/${vendorTypeId}/categories/create`}>
              <Icon name="icon-[mingcute--add-line]" />
              Add Category
            </Link>
          </ActionButton>
        </div>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <VendorCategoriesTable data={data} meta={meta} vendorTypeId={vendorTypeId} />
      </Suspense>
    </div>
  );
}
