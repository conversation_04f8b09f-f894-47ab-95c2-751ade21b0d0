'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createVendorType, updateVendorType } from '../actions';
import { VendorType } from '../types';
import { showToastPromise } from '@/lib/toast-utils';
import { Icon } from '@/components/icon';

interface VendorTypeFormProps {
  vendorType?: VendorType;
  isEdit?: boolean;
}

export default function VendorTypeForm({ vendorType, isEdit = false }: VendorTypeFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);
    
    try {
      if (isEdit && vendorType) {
        formData.append('id', vendorType.id);
        const result = await showToastPromise(
          updateVendorType(formData),
          'vendor-type',
          'update'
        );
        
        if (result.success) {
          router.push('/admin/vendor-types');
        }
      } else {
        const result = await showToastPromise(
          createVendorType(formData),
          'vendor-type',
          'create'
        );
        
        if (result.success) {
          router.push('/admin/vendor-types');
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form action={handleSubmit} className="space-y-6">
      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Name *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          defaultValue={vendorType?.name}
          required
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="Enter vendor type name"
        />
      </div>

      {/* Slug Field */}
      <div>
        <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
          Slug
        </label>
        <input
          type="text"
          id="slug"
          name="slug"
          defaultValue={vendorType?.slug}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="auto-generated-from-name"
        />
        <p className="text-sm text-gray-500 mt-1">
          URL-friendly version of the name. Leave empty to auto-generate.
        </p>
      </div>

      {/* Details Field */}
      <div>
        <label htmlFor="details" className="block text-sm font-medium text-gray-700 mb-2">
          Details
        </label>
        <textarea
          id="details"
          name="details"
          rows={4}
          defaultValue={vendorType?.details || ''}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="Describe this vendor type..."
        />
      </div>

      {/* Service ID Field */}
      <div>
        <label htmlFor="serviceId" className="block text-sm font-medium text-gray-700 mb-2">
          Service ID
        </label>
        <input
          type="text"
          id="serviceId"
          name="serviceId"
          defaultValue={vendorType?.serviceId}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="Enter service ID"
        />
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-4 pt-6 border-t border-gray-200">
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center gap-2 px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isSubmitting && <Icon name="icon-[lucide--loader-2]" classNames="animate-spin" />}
          {isEdit ? 'Update' : 'Create'} Vendor Type
        </button>
        
        <button
          type="button"
          onClick={() => router.back()}
          className="inline-flex items-center gap-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
      </div>
    </form>
  );
}
