'use client';

import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { Icon } from '@/components/icon';
import { formatDate } from 'date-fns';
import Link from 'next/link';
import { VendorType } from '../types';
import { PaginationMeta } from '@/types/pagination';
import { deleteVendorType } from '../actions';
import { showToastPromise } from '@/lib/toast-utils';

interface VendorTypesTableProps {
  data: VendorType[];
  meta?: PaginationMeta;
}

export default function VendorTypesTable({ data, meta }: VendorTypesTableProps) {
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  const handleDeleteClick = (vendorType: VendorType) => {
    openDeleteDialog(vendorType.id, vendorType.name, async () => {
      const formData = new FormData();
      formData.append('id', vendorType.id);

      const result = await deleteVendorType(formData);

      if (result.success) {
        showToastPromise(
          Promise.resolve(),
          'vendor-type',
          'delete'
        );
      } else {
        throw new Error(result.error || 'Failed to delete vendor type');
      }
    });
  };

  return (
    <>
      <DeleteConfirmationDialog />
      <CustomTable
        title="Vendor Types"
        columns={[
          {
            name: "Name",
            uid: "name",
            sortable: true,
            renderCell: (vendorType: VendorType) => (
              <Link
                href={`/admin/vendor-types/${vendorType.id}/categories`}
                className="font-medium text-primary hover:underline"
              >
                {vendorType.name}
              </Link>
            ),
          },
          {
            name: "Slug",
            uid: "slug",
            sortable: true,
          },
          {
            name: "Details",
            uid: "details",
            renderCell: (vendorType: VendorType) => (
              <div className="max-w-xs truncate">
                {vendorType.details || '-'}
              </div>
            ),
          },
          {
            name: "Created At",
            uid: "createdAt",
            sortable: true,
            renderCell: (vendorType: VendorType) => (
              <span className="text-sm text-gray-600">
                {formatDate(new Date(vendorType.createdAt), 'MMM dd, yyyy')}
              </span>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (vendorType: VendorType) => (
              <div className="flex w-fit items-center gap-3">
                <Link href={`/admin/vendor-types/${vendorType.id}/categories`}>
                  <Icon
                    name="icon-[mingcute--eye-line]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <Link href={`/admin/vendor-types/${vendorType.id}/edit`}>
                  <Icon
                    name="icon-[mage--edit-pen-fill]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <button
                  onClick={() => handleDeleteClick(vendorType)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
      />
    </>
  );
}
