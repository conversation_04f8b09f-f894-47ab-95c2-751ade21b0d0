'use client';

import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { Icon } from '@/components/icon';
import { formatDate } from 'date-fns';
import Link from 'next/link';
import { VendorCategory } from '../types';
import { PaginationMeta } from '@/types/pagination';
import { deleteVendorCategory } from '../actions';
import { showToastPromise } from '@/lib/toast-utils';

interface VendorCategoriesTableProps {
  data: VendorCategory[];
  meta?: PaginationMeta;
  vendorTypeId: string;
}

export default function VendorCategoriesTable({ data, meta, vendorTypeId }: VendorCategoriesTableProps) {
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  const handleDeleteClick = (vendorCategory: VendorCategory) => {
    openDeleteDialog(vendorCategory.id, vendorCategory.name, async () => {
      const formData = new FormData();
      formData.append('id', vendorCategory.id);
      formData.append('vendorTypeId', vendorTypeId);

      const result = await deleteVendorCategory(formData);

      if (result.success) {
        showToastPromise(
          Promise.resolve(),
          'vendor-category',
          'delete'
        );
      } else {
        throw new Error(result.error || 'Failed to delete vendor category');
      }
    });
  };

  return (
    <>
      <DeleteConfirmationDialog />
      <CustomTable
        title="Vendor Categories"
        columns={[
          {
            name: "Name",
            uid: "name",
            sortable: true,
            renderCell: (vendorCategory: VendorCategory) => (
              <Link
                href={`/admin/vendor-categories/${vendorCategory.id}/vendors`}
                className="font-medium text-primary hover:underline"
              >
                {vendorCategory.name}
              </Link>
            ),
          },
          {
            name: "Slug",
            uid: "slug",
            sortable: true,
            renderCell: (vendorCategory: VendorCategory) => (
              <span>{vendorCategory.slug || '-'}</span>
            ),
          },
          {
            name: "Details",
            uid: "details",
            renderCell: (vendorCategory: VendorCategory) => (
              <div className="max-w-xs truncate">
                {vendorCategory.details || '-'}
              </div>
            ),
          },
          {
            name: "Created At",
            uid: "createdAt",
            sortable: true,
            renderCell: (vendorCategory: VendorCategory) => (
              <span className="text-sm text-gray-600">
                {formatDate(new Date(vendorCategory.createdAt), 'MMM dd, yyyy')}
              </span>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (vendorCategory: VendorCategory) => (
              <div className="flex w-fit items-center gap-3">
                <Link href={`/admin/vendor-categories/${vendorCategory.id}/vendors`}>
                  <Icon
                    name="icon-[mingcute--eye-line]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <Link href={`/admin/vendor-categories/${vendorCategory.id}/edit`}>
                  <Icon
                    name="icon-[mage--edit-pen-fill]"
                    classNames="text-primary hover:text-primary-600"
                  />
                </Link>
                <button
                  onClick={() => handleDeleteClick(vendorCategory)}
                  className="text-red-500 hover:text-red-700"
                >
                  <Icon name="icon-[mingcute--delete-2-line]" />
                </button>
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
      />
    </>
  );
}
