'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { createVendorCategory, updateVendorCategory } from '../actions';
import { VendorCategory, VendorType } from '../types';
import { showToastPromise } from '@/lib/toast-utils';
import { Icon } from '@/components/icon';

interface VendorCategoryFormProps {
  vendorTypeId?: string;
  vendorCategory?: VendorCategory;
  isEdit?: boolean;
  vendorTypes?: VendorType[];
  redirectPath?: string;
}

export default function VendorCategoryForm({
  vendorTypeId,
  vendorCategory,
  isEdit = false,
  vendorTypes,
  redirectPath
}: VendorCategoryFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedVendorTypeId, setSelectedVendorTypeId] = useState(vendorTypeId || vendorCategory?.vendorTypeId || '');

  const handleSubmit = async (formData: FormData) => {
    setIsSubmitting(true);

    try {
      const typeId = selectedVendorTypeId || formData.get('vendorTypeId') as string;
      formData.append('vendorTypeId', typeId);

      if (isEdit && vendorCategory) {
        formData.append('id', vendorCategory.id);
        const result = await showToastPromise(
          updateVendorCategory(formData),
          'vendor-category',
          'update'
        );

        if (result.success) {
          router.push(redirectPath || `/admin/vendor-types/${typeId}/categories`);
        }
      } else {
        const result = await showToastPromise(
          createVendorCategory(formData),
          'vendor-category',
          'create'
        );

        if (result.success) {
          router.push(redirectPath || `/admin/vendor-types/${typeId}/categories`);
        }
      }
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form action={handleSubmit} className="space-y-6">
      {/* Vendor Type Selection - only show if vendorTypes are provided */}
      {vendorTypes && (
        <div>
          <label htmlFor="vendorTypeId" className="block text-sm font-medium text-gray-700 mb-2">
            Vendor Type *
          </label>
          <select
            id="vendorTypeId"
            name="vendorTypeId"
            value={selectedVendorTypeId}
            onChange={(e) => setSelectedVendorTypeId(e.target.value)}
            required
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          >
            <option value="">Select a vendor type</option>
            {vendorTypes.map((type) => (
              <option key={type.id} value={type.id}>
                {type.name}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Name Field */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Name *
        </label>
        <input
          type="text"
          id="name"
          name="name"
          defaultValue={vendorCategory?.name}
          required
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="Enter category name"
        />
      </div>

      {/* Slug Field */}
      <div>
        <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
          Slug
        </label>
        <input
          type="text"
          id="slug"
          name="slug"
          defaultValue={vendorCategory?.slug || ''}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="auto-generated-from-name"
        />
        <p className="text-sm text-gray-500 mt-1">
          URL-friendly version of the name. Leave empty to auto-generate.
        </p>
      </div>

      {/* Details Field */}
      <div>
        <label htmlFor="details" className="block text-sm font-medium text-gray-700 mb-2">
          Details
        </label>
        <textarea
          id="details"
          name="details"
          rows={4}
          defaultValue={vendorCategory?.details || ''}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
          placeholder="Describe this category..."
        />
      </div>

      {/* Action Buttons */}
      <div className="flex items-center gap-4 pt-6 border-t border-gray-200">
        <button
          type="submit"
          disabled={isSubmitting}
          className="inline-flex items-center gap-2 px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isSubmitting && <Icon name="icon-[lucide--loader-2]" classNames="animate-spin" />}
          {isEdit ? 'Update' : 'Create'} Category
        </button>
        
        <button
          type="button"
          onClick={() => router.back()}
          className="inline-flex items-center gap-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          Cancel
        </button>
      </div>
    </form>
  );
}
