'use server';

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";
import { VendorType, VendorCategory, VendorInCategory, ApiResponse, VendorTypeFormData, VendorCategoryFormData } from "./types";

// Vendor Types Actions
export async function getVendorTypes(searchParams?: Record<string, string>) {
  try {
    const response = await api.get<ApiResponse<VendorType>>("vendor-types", searchParams);
    if (!response) {
      return { data: [], meta: null, error: 'No response from server' };
    }
    return { data: response.data, meta: response.meta, error: null };
  } catch (error) {
    console.error('Error fetching vendor types:', error);
    return { data: [], meta: null, error: 'Failed to load vendor types' };
  }
}

export async function getVendorType(id: string) {
  try {
    const response = await api.get<VendorType>(`vendor-types/${id}`);
    if (!response) {
      return { data: null, error: 'Vendor type not found' };
    }
    return { data: response, error: null };
  } catch (error) {
    console.error('Error fetching vendor type:', error);
    return { data: null, error: 'Failed to load vendor type' };
  }
}

export async function createVendorType(data: FormData) {
  try {
    const formData: VendorTypeFormData = {
      name: data.get('name') as string,
      slug: data.get('slug') as string || undefined,
      details: data.get('details') as string || undefined,
      serviceId: data.get('serviceId') as string || undefined,
    };

    const response = await api.post<VendorType>("vendor-types", formData);
    revalidatePath("/admin/vendor-types");
    return { success: true, data: response, error: null };
  } catch (error) {
    console.error('Error creating vendor type:', error);
    return { success: false, data: null, error: 'Failed to create vendor type' };
  }
}

export async function updateVendorType(data: FormData) {
  try {
    const id = data.get('id') as string;
    const formData: VendorTypeFormData = {
      name: data.get('name') as string,
      slug: data.get('slug') as string || undefined,
      details: data.get('details') as string || undefined,
      serviceId: data.get('serviceId') as string || undefined,
    };

    const response = await api.put<VendorType>(`vendor-types/${id}`, formData);
    revalidatePath("/admin/vendor-types");
    revalidatePath(`/admin/vendor-types/${id}`);
    return { success: true, data: response, error: null };
  } catch (error) {
    console.error('Error updating vendor type:', error);
    return { success: false, data: null, error: 'Failed to update vendor type' };
  }
}

export async function deleteVendorType(data: FormData) {
  try {
    const id = data.get('id') as string;
    // Use the correct endpoint format: /vendor-types/{id}
    await api.destroy(id, "vendor-types");
    revalidatePath("/admin/vendor-types");
    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting vendor type:', error);
    return { success: false, error: 'Failed to delete vendor type' };
  }
}

// Vendor Categories Actions
export async function getVendorCategories(vendorTypeId: string, searchParams?: Record<string, string>) {
  try {
    const response = await api.get<ApiResponse<VendorCategory>>(`vendor-types/${vendorTypeId}/categories`, searchParams);
    if (!response) {
      return { data: [], meta: null, error: 'No response from server' };
    }
    return { data: response.data, meta: response.meta, error: null };
  } catch (error) {
    console.error('Error fetching vendor categories:', error);
    return { data: [], meta: null, error: 'Failed to load vendor categories' };
  }
}

export async function getVendorCategory(id: string) {
  try {
    const response = await api.get<VendorCategory>(`vendor-categories/${id}`);
    if (!response) {
      return { data: null, error: 'Vendor category not found' };
    }
    return { data: response, error: null };
  } catch (error) {
    console.error('Error fetching vendor category:', error);
    return { data: null, error: 'Failed to load vendor category' };
  }
}

export async function createVendorCategory(data: FormData) {
  try {
    const vendorTypeId = data.get('vendorTypeId') as string;
    const formData: VendorCategoryFormData = {
      name: data.get('name') as string,
      slug: data.get('slug') as string || undefined,
      details: data.get('details') as string || undefined,
      vendorTypeId,
    };

    const response = await api.post<VendorCategory>(`vendor-types/${vendorTypeId}/categories`, formData);
    revalidatePath(`/admin/vendor-types/${vendorTypeId}/categories`);
    return { success: true, data: response, error: null };
  } catch (error) {
    console.error('Error creating vendor category:', error);
    return { success: false, data: null, error: 'Failed to create vendor category' };
  }
}

export async function updateVendorCategory(data: FormData) {
  try {
    const id = data.get('id') as string;
    const vendorTypeId = data.get('vendorTypeId') as string;
    const formData: VendorCategoryFormData = {
      name: data.get('name') as string,
      slug: data.get('slug') as string || undefined,
      details: data.get('details') as string || undefined,
      vendorTypeId,
    };

    const response = await api.put<VendorCategory>(`vendor-categories/${id}`, formData);
    revalidatePath(`/admin/vendor-types/${vendorTypeId}/categories`);
    revalidatePath(`/admin/vendor-categories/${id}`);
    return { success: true, data: response, error: null };
  } catch (error) {
    console.error('Error updating vendor category:', error);
    return { success: false, data: null, error: 'Failed to update vendor category' };
  }
}

export async function deleteVendorCategory(data: FormData) {
  try {
    const id = data.get('id') as string;
    const vendorTypeId = data.get('vendorTypeId') as string;
    // Use the correct endpoint format: /vendor-categories/{id}
    await api.destroy(id, "vendor-categories");
    revalidatePath(`/admin/vendor-types/${vendorTypeId}/categories`);
    revalidatePath("/admin/vendor-categories"); // Also revalidate the global categories page
    return { success: true, error: null };
  } catch (error) {
    console.error('Error deleting vendor category:', error);
    return { success: false, error: 'Failed to delete vendor category' };
  }
}

// Get all vendor categories across all vendor types
export async function getAllVendorCategories(searchParams?: Record<string, string>) {
  try {
    const response = await api.get<ApiResponse<VendorCategory & { vendorTypeName: string }>>("vendor-categories", searchParams);
    if (!response) {
      return { data: [], meta: null, error: 'No response from server' };
    }
    return { data: response.data, meta: response.meta, error: null };
  } catch (error) {
    console.error('Error fetching all vendor categories:', error);
    return { data: [], meta: null, error: 'Failed to load vendor categories' };
  }
}

// Vendors in Category Actions
export async function getVendorsInCategory(categoryId: string, searchParams?: Record<string, string>) {
  try {
    const response = await api.get<ApiResponse<VendorInCategory>>(`vendor-categories/${categoryId}/vendors`, searchParams);
    if (!response) {
      return { data: [], meta: null, error: 'No response from server' };
    }
    return { data: response.data, meta: response.meta, error: null };
  } catch (error) {
    console.error('Error fetching vendors in category:', error);
    return { data: [], meta: null, error: 'Failed to load vendors' };
  }
}
