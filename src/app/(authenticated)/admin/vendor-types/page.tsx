import { Suspense } from 'react';
import { getVendorTypes } from './actions';
import VendorTypesTable from './components/VendorTypesTable';
import Link from 'next/link';
import { Icon } from '@/components/icon';
import { ActionButton } from '@/components/ui/action-button';

interface PageProps {
  searchParams: Record<string, string>;
}

export default async function VendorTypesPage({ searchParams }: PageProps) {
  const { data, meta, error } = await getVendorTypes(searchParams);

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8">
        <div className="text-center">
          <Icon name="icon-[lucide--alert-circle]" classNames="text-red-500 text-6xl mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Vendor Types</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link
            href="/admin/vendor-types"
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendor Types</h1>
          <p className="text-gray-600 mt-1">
            Manage vendor types and their categories
          </p>
        </div>
        <ActionButton asChild>
          <Link href="/admin/vendor-types/create">
            <Icon name="icon-[mingcute--add-line]" />
            Add Vendor Type
          </Link>
        </ActionButton>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <VendorTypesTable data={data} meta={meta} />
      </Suspense>
    </div>
  );
}
