import { Suspense } from 'react';
import VendorTypeForm from '../components/VendorTypeForm';
import { Icon } from '@/components/icon';
import Link from 'next/link';
import { ActionButton } from '@/components/ui/action-button';

export default function CreateVendorTypePage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <ActionButton variant="outline" size="icon" asChild>
          <Link href="/admin/vendor-types">
            <Icon name="icon-[lucide--arrow-left]" />
          </Link>
        </ActionButton>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Vendor Type</h1>
          <p className="text-gray-600 mt-1">
            Add a new vendor type to organize vendors
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <Suspense fallback={<div>Loading...</div>}>
            <VendorTypeForm />
          </Suspense>
        </div>
      </div>
    </div>
  );
}
