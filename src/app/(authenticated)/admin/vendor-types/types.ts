export interface VendorType {
  id: string;
  name: string;
  slug: string;
  details: string | null;
  image: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  serviceId: string;
  createdAt: string;
  updatedAt: string;
}

export interface VendorCategory {
  id: string;
  name: string;
  slug: string | null;
  details: string | null;
  image: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  vendorTypeId: string;
  createdAt: string;
  updatedAt: string;
}

export interface VendorInCategory {
  id: string;
  userId: string;
  serviceId: string;
  name: string;
  slug: string;
  details: string;
  email: string;
  phone: string;
  reg: string;
  permit: string;
  kra: string;
  logo: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  cover: {
    url: string;
    name: string;
    extname: string;
    size: number;
    mimeType: string;
  } | null;
  active: boolean;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  deliveryPreferences: any;
  availabilitySettings: any;
  pricingStructure: any;
  verificationStatus: string;
  verificationNotes: string | null;
  verifiedAt: string | null;
  qrCodePreferences: any;
  code: string | null;
  canProvideDeliveryServices: boolean;
  canManageDeliveryServiceAreas: boolean;
  canAccessDeliverySettings: boolean;
  deliveryCapabilities: {
    canDeliver: boolean;
    hasDeliveryProfile: boolean;
    profileStatus: string | null;
    maxDistance: number | null;
    vehicleTypes: any;
    maxConcurrentOrders: number | null;
  };
  hasDeliveryEnabled: boolean;
}

export interface ApiResponse<T> {
  meta: {
    total: number;
    perPage: number;
    currentPage: number;
    lastPage: number;
    firstPage: number;
    firstPageUrl: string;
    lastPageUrl: string;
    nextPageUrl: string | null;
    previousPageUrl: string | null;
  };
  data: T[];
}

export interface VendorTypeFormData {
  name: string;
  slug?: string;
  details?: string;
  serviceId?: string;
}

export interface VendorCategoryFormData {
  name: string;
  slug?: string;
  details?: string;
  vendorTypeId: string;
}
