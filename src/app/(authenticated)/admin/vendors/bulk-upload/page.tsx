'use client';

import { But<PERSON> } from '@nextui-org/react';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import VendorBulkUploadForm from '@/components/vendors/VendorBulkUploadForm';
import VendorUploadHistory from '@/components/vendors/VendorUploadHistory';

export default function VendorBulkUploadPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button
          as={Link}
          href="/admin/vendors"
          variant="light"
          startContent={<ArrowLeft className="size-4" />}
        >
          Back to Vendors
        </Button>
      </div>

      <div className="mb-8">
        <h1 className="text-2xl font-bold">Bulk Upload Vendors</h1>
        <p className="text-default-500">
          Upload multiple vendors at once using a CSV file.
        </p>
      </div>

      <div className="grid gap-6">
        <VendorBulkUploadForm />
        <VendorUploadHistory />
      </div>
    </div>
  );
} 