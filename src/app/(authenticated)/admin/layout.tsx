import { auth } from "@/auth";
import { redirect } from "next/navigation";

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  return (
    <div className="min-h-screen bg-default-50">
      {children}
    </div>
  );
} 