import { auth } from "@/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import DurationForm from "../components/duration-form-new";

export const metadata = {
  title: "Create Duration Template",
  description: "Create a new service duration template",
};

export default async function CreateDurationPage() {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  return (
    <div className="page-content p-5">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Create Duration Template</h1>
          <p className="text-gray-600 mt-1">
            Create a new service duration template that vendors can use for their service options
          </p>
        </div>
        
        <Link
          href="/admin/durations"
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
        >
          ← Back to Durations
        </Link>
      </div>

      {/* Form */}
      <div className="max-w-4xl">
        <DurationForm />
      </div>
    </div>
  );
}
