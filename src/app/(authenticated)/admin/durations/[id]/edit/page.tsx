import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect, notFound } from "next/navigation";
import EditDurationClient from "./edit-duration-client";

// Duration interface
interface Duration {
  id: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: string[];
    blackoutDays: string[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: string[];
  };
  active: boolean;
  createdAt: string;
  updatedAt: string;
  calendarBlockMinutes: number;
  totalHours: number;
  isShortDuration: boolean;
  isMediumDuration: boolean;
  isLongDuration: boolean;
  isFullDay: boolean;
}

const fetchDuration = cache((id: string) =>
  api.get<{ success: boolean; data: Duration }>(`service-options/durations/${id}`)
);

export const generateMetadata = async ({
  params: { id },
}: {
  params: { id: string };
}) => {
  try {
    const response = await fetchDuration(id);
    const duration = response?.data;

    return {
      title: duration ? `Edit ${duration.name}` : "Edit Duration",
      description: `Edit duration template: ${duration?.name || id}`,
    };
  } catch (error) {
    return {
      title: "Edit Duration",
      description: "Edit duration template",
    };
  }
};

export default async function EditDurationPage({
  params: { id },
}: {
  params: { id: string };
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch duration data
  let duration: Duration | null = null;
  
  try {
    const response = await fetchDuration(id);
    if (response?.success && response.data) {
      duration = response.data;
    }
  } catch (error) {
    console.error("Error fetching duration:", error);
  }

  // If duration not found, show 404
  if (!duration) {
    notFound();
  }

  return <EditDurationClient duration={duration} />;
}
