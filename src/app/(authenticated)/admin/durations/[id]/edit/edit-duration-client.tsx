"use client";

import Link from "next/link";
import { Button } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import DurationForm from "../../components/duration-form-new";

// Form Duration interface (matches the form's expected structure)
interface FormDuration {
  id?: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: { value: string }[];
    blackoutDays: { value: string }[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: { value: string }[];
  };
  active: boolean;
}

// API Duration interface (what we receive from the API)
interface Duration {
  id: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: string[];
    blackoutDays: string[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: string[];
  };
  active: boolean;
  createdAt: string;
  updatedAt: string;
  calendarBlockMinutes: number;
  totalHours: number;
  isShortDuration: boolean;
  isMediumDuration: boolean;
  isLongDuration: boolean;
  isFullDay: boolean;
}

// Transform API duration to form duration
function transformDurationForForm(duration: Duration): FormDuration {
  return {
    ...duration,
    schedulingRules: {
      ...duration.schedulingRules,
      timeSlots: duration.schedulingRules.timeSlots.map(slot => ({ value: slot })),
      blackoutDays: duration.schedulingRules.blackoutDays.map(day => ({ value: day })),
    },
    branchConstraints: {
      ...duration.branchConstraints,
      equipmentRequired: duration.branchConstraints.equipmentRequired.map(equipment => ({ value: equipment })),
    },
  };
}

interface EditDurationClientProps {
  duration: Duration;
}

export default function EditDurationClient({ duration }: EditDurationClientProps) {
  return (
    <div className="page-content p-5">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Duration Template</h1>
          <p className="text-gray-600 mt-1">
            Edit the &quot;{duration.name}&quot; duration template
          </p>
        </div>
        
        <Button
          as={Link}
          href="/admin/durations"
          variant="light"
          startContent={<Icon name="icon-[heroicons--arrow-left-20-solid]" />}
        >
          Back to Durations
        </Button>
      </div>

      {/* Form */}
      <div className="max-w-4xl">
        <DurationForm defaultValues={transformDurationForForm(duration)} isEdit={true} />
      </div>
    </div>
  );
}
