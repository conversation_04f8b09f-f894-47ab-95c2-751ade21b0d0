import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import DurationList from "./components/duration-list";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

// Duration interface based on API response
interface Duration {
  id: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: string[];
    blackoutDays: string[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: string[];
  };
  active: boolean;
  createdAt: string;
  updatedAt: string;
  calendarBlockMinutes: number;
  totalHours: number;
  isShortDuration: boolean;
  isMediumDuration: boolean;
  isLongDuration: boolean;
  isFullDay: boolean;
}

interface DurationResponse {
  success: boolean;
  data: {
    data: Duration[];
    meta: {
      total: number;
      per_page: number;
      current_page: number;
      last_page: number;
    };
  };
}

const fetchDurations = cache(
  () => api.get<DurationResponse>('service-options/durations'),
);

export const generateMetadata = async () => {
  return {
    title: "Duration Management",
    description: "Manage service duration templates for the platform",
    keywords: ["admin", "durations", "service", "management"],
  };
};

export default async function AdminDurationsPage() {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch durations data
  const durationsResponse = await fetchDurations().catch(() => null);

  // Fallback demo data if API is not available
  const demoDurations: Duration[] = [
    {
      id: "demo-1",
      name: "Express Service",
      description: "Quick 15-minute service",
      minutes: 15,
      bufferMinutes: 5,
      category: "short",
      maxConcurrent: 3,
      allowsBackToBack: true,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 1,
        maxPerDay: 20,
        timeSlots: ["09:00", "10:00", "11:00", "14:00", "15:00", "16:00"],
        blackoutDays: [],
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: ["basic"],
      },
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      calendarBlockMinutes: 20,
      totalHours: 0.33,
      isShortDuration: true,
      isMediumDuration: false,
      isLongDuration: false,
      isFullDay: false,
    },
    {
      id: "demo-2",
      name: "Standard Service",
      description: "Standard 1-hour service",
      minutes: 60,
      bufferMinutes: 15,
      category: "medium",
      maxConcurrent: 2,
      allowsBackToBack: false,
      requiredBreakAfter: 15,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 8,
        timeSlots: ["09:00", "11:00", "14:00", "16:00"],
        blackoutDays: [],
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 2,
        equipmentRequired: ["standard", "premium"],
      },
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      calendarBlockMinutes: 75,
      totalHours: 1.25,
      isShortDuration: false,
      isMediumDuration: true,
      isLongDuration: false,
      isFullDay: false,
    },
    {
      id: "demo-3",
      name: "Premium Service",
      description: "Premium 3-hour service",
      minutes: 180,
      bufferMinutes: 30,
      category: "long",
      maxConcurrent: 1,
      allowsBackToBack: false,
      requiredBreakAfter: 60,
      schedulingRules: {
        minAdvanceHours: 24,
        maxPerDay: 2,
        timeSlots: ["09:00", "14:00"],
        blackoutDays: ["sunday"],
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 3,
        equipmentRequired: ["premium", "specialized"],
      },
      active: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      calendarBlockMinutes: 210,
      totalHours: 3.5,
      isShortDuration: false,
      isMediumDuration: false,
      isLongDuration: true,
      isFullDay: false,
    },
  ];

  const demoMeta = {
    total: 3,
    per_page: 10,
    current_page: 1,
    last_page: 1,
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Duration Management</h1>
        <p className="text-gray-600 mt-1">
          Manage service duration templates that vendors can use for their service options
        </p>
      </div>

      <DurationList
        data={durationsResponse?.data?.data || demoDurations}
        meta={durationsResponse?.data?.meta || demoMeta}
      />
    </div>
  );
}
