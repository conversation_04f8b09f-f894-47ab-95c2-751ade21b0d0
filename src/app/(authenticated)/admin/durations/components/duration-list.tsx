"use client";

import Link from "next/link";
import { <PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import { CustomTable } from "@/components/custom-table";
import { Icon } from "@/components/icon";
import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

// Duration interface
interface Duration {
  id: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: string[];
    blackoutDays: string[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: string[];
  };
  active: boolean;
  createdAt: string;
  updatedAt: string;
  calendarBlockMinutes: number;
  totalHours: number;
  isShortDuration: boolean;
  isMediumDuration: boolean;
  isLongDuration: boolean;
  isFullDay: boolean;
}

interface DurationListProps {
  data: Duration[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
}

// Helper function to format duration display
const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes} min`;
  } else if (minutes < 1440) {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  } else {
    const days = Math.floor(minutes / 1440);
    const remainingHours = Math.floor((minutes % 1440) / 60);
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
  }
};

// Helper function to get category color
const getCategoryColor = (category: string) => {
  switch (category) {
    case "short":
      return "success";
    case "medium":
      return "primary";
    case "long":
      return "warning";
    case "full-day":
      return "danger";
    default:
      return "default";
  }
};

export default function DurationList({ data, meta }: DurationListProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this duration? This action cannot be undone.")) {
      return;
    }

    startTransition(async () => {
      try {
        const response = await fetch(`/api/admin/durations/${id}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete duration');
        }

        toast.success("Duration deleted successfully!");
        router.refresh();
      } catch (error) {
        console.error("Error deleting duration:", error);
        toast.error("Failed to delete duration");
      }
    });
  };

  const handleToggleActive = async (duration: Duration) => {
    startTransition(async () => {
      try {
        const response = await fetch(`/api/admin/durations/${duration.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...duration,
            active: !duration.active,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update duration');
        }

        toast.success(`Duration ${duration.active ? 'deactivated' : 'activated'} successfully!`);
        router.refresh();
      } catch (error) {
        console.error("Error updating duration:", error);
        toast.error("Failed to update duration");
      }
    });
  };

  const columns = [
    {
      uid: "name",
      name: "Name",
      sortable: true,
      renderCell: (duration: Duration) => (
        <div>
          <p className="font-semibold">{duration.name}</p>
          {duration.description && (
            <p className="text-sm text-gray-500">{duration.description}</p>
          )}
        </div>
      ),
    },
    {
      uid: "duration",
      name: "Duration",
      sortable: true,
      renderCell: (duration: Duration) => (
        <div>
          <p className="font-medium">{formatDuration(duration.minutes)}</p>
          <p className="text-sm text-gray-500">
            +{duration.bufferMinutes}m buffer
          </p>
        </div>
      ),
    },
    {
      uid: "category",
      name: "Category",
      sortable: true,
      renderCell: (duration: Duration) => (
        <Chip
          color={getCategoryColor(duration.category) as any}
          variant="flat"
          size="sm"
        >
          {duration.category.charAt(0).toUpperCase() + duration.category.slice(1)}
        </Chip>
      ),
    },
    {
      uid: "scheduling",
      name: "Scheduling",
      renderCell: (duration: Duration) => (
        <div className="text-sm">
          <p>Max/day: {duration.schedulingRules.maxPerDay}</p>
          <p>Concurrent: {duration.maxConcurrent}</p>
          <p>Advance: {duration.schedulingRules.minAdvanceHours}h</p>
        </div>
      ),
    },
    {
      uid: "constraints",
      name: "Constraints",
      renderCell: (duration: Duration) => (
        <div className="text-sm">
          <p>Staff: {duration.branchConstraints.staffRequired}</p>
          <p>Back-to-back: {duration.allowsBackToBack ? "Yes" : "No"}</p>
          {duration.requiredBreakAfter > 0 && (
            <p>Break: {duration.requiredBreakAfter}m</p>
          )}
        </div>
      ),
    },
    {
      uid: "status",
      name: "Status",
      sortable: true,
      renderCell: (duration: Duration) => (
        <Chip
          color={duration.active ? "success" : "default"}
          variant="flat"
          size="sm"
        >
          {duration.active ? "Active" : "Inactive"}
        </Chip>
      ),
    },
    {
      uid: "actions",
      name: "Actions",
      renderCell: (duration: Duration) => (
        <div className="flex items-center gap-2">
          <Button
            as={Link}
            href={`/admin/durations/${duration.id}/edit`}
            isIconOnly
            variant="light"
            color="primary"
            size="sm"
          >
            <Icon name="icon-[heroicons--pencil-square-20-solid]" />
          </Button>
          
          <Button
            isIconOnly
            variant="light"
            color={duration.active ? "warning" : "success"}
            size="sm"
            onPress={() => handleToggleActive(duration)}
            isDisabled={isPending}
          >
            <Icon 
              name={duration.active 
                ? "icon-[heroicons--eye-slash-20-solid]" 
                : "icon-[heroicons--eye-20-solid]"
              } 
            />
          </Button>

          <Button
            isIconOnly
            variant="light"
            color="danger"
            size="sm"
            onPress={() => handleDelete(duration.id)}
            isDisabled={isPending}
          >
            <Icon name="icon-[heroicons--trash-20-solid]" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      title="Duration Templates"
      columns={columns}
      data={data}
      meta={meta}
      filter={[
        {
          column: "category",
          displayName: "Category",
          values: [
            { name: "Short", value: "short" },
            { name: "Medium", value: "medium" },
            { name: "Long", value: "long" },
            { name: "Full Day", value: "full-day" },
          ],
        },
        {
          column: "active",
          displayName: "Status",
          values: [
            { name: "Active", value: "true" },
            { name: "Inactive", value: "false" },
          ],
        },
      ]}
      action={
        <div className="flex items-center gap-3">
          <Button
            as={Link}
            href="/admin/durations/create"
            color="primary"
            startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          >
            Create Duration
          </Button>
        </div>
      }
    />
  );
}
