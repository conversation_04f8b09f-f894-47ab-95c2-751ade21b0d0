"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, Controller, useFieldArray } from "react-hook-form";
import {
  Input,
  Button,
  Select,
  SelectItem,
  Textarea,
  Card,
  CardBody,
  Switch,
  Chip,
  Divider,
  Accordion,
  AccordionItem
} from "@nextui-org/react";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { Icon } from "@/components/icon";

interface Duration {
  id?: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: { value: string }[];
    blackoutDays: { value: string }[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: { value: string }[];
  };
  active: boolean;
}

interface DurationFormProps {
  defaultValues?: Duration;
  isEdit?: boolean;
}

export default function DurationForm({ defaultValues, isEdit = false }: DurationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    control,
    watch,
    formState: { errors },
  } = useForm<Duration>({
    defaultValues: defaultValues || {
      name: "",
      description: "",
      minutes: 60,
      bufferMinutes: 15,
      category: "medium",
      maxConcurrent: 1,
      allowsBackToBack: false,
      requiredBreakAfter: 0,
      schedulingRules: {
        minAdvanceHours: 2,
        maxPerDay: 8,
        timeSlots: [],
        blackoutDays: [],
      },
      branchConstraints: {
        respectBranchHours: true,
        staffRequired: 1,
        equipmentRequired: [],
      },
      active: true,
    },
  });

  const {
    fields: timeSlotsFields,
    append: appendTimeSlot,
    remove: removeTimeSlot,
  } = useFieldArray({
    control,
    name: "schedulingRules.timeSlots",
  });

  const {
    fields: blackoutDaysFields,
    append: appendBlackoutDay,
    remove: removeBlackoutDay,
  } = useFieldArray({
    control,
    name: "schedulingRules.blackoutDays",
  });

  const {
    fields: equipmentFields,
    append: appendEquipment,
    remove: removeEquipment,
  } = useFieldArray({
    control,
    name: "branchConstraints.equipmentRequired",
  });

  // Watch category to provide smart defaults
  const watchedCategory = watch("category");
  const watchedMinutes = watch("minutes");

  const onSubmit = async (data: Duration) => {
    setIsSubmitting(true);
    
    try {
      const url = isEdit ? `/api/admin/durations/${defaultValues?.id}` : '/api/admin/durations';
      const method = isEdit ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log("Duration saved successfully:", result);
      
      toast.success(`Duration ${isEdit ? 'updated' : 'created'} successfully!`);
      router.push('/admin/durations');
      
    } catch (error) {
      console.error("Error saving duration:", error);
      toast.error(`Failed to ${isEdit ? 'update' : 'create'} duration: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to add time slot
  const addTimeSlot = () => {
    appendTimeSlot({ value: "" });
  };

  // Helper function to add blackout day
  const addBlackoutDay = () => {
    appendBlackoutDay({ value: "" });
  };

  // Helper function to add equipment
  const addEquipment = () => {
    appendEquipment({ value: "" });
  };

  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold mb-4">Basic Information</h3>

            <div className="space-y-4">
              <Input
                {...register("name", { required: "Duration name is required" })}
                label="Duration Name"
                placeholder="e.g., Standard Service"
                isInvalid={!!errors.name}
                errorMessage={errors.name?.message}
              />

              <Textarea
                {...register("description")}
                label="Description"
                placeholder="Brief description of this duration template"
                maxRows={3}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Input
                  {...register("minutes", {
                    required: "Duration is required",
                    min: { value: 1, message: "Duration must be at least 1 minute" },
                    max: { value: 1440, message: "Duration cannot exceed 24 hours (1440 minutes)" },
                    valueAsNumber: true
                  })}
                  type="number"
                  label="Duration (minutes)"
                  placeholder="60"
                  min="1"
                  max="1440"
                  isInvalid={!!errors.minutes}
                  errorMessage={errors.minutes?.message}
                />

                <Input
                  {...register("bufferMinutes", {
                    required: "Buffer time is required",
                    min: { value: 0, message: "Buffer time cannot be negative" },
                    max: { value: 240, message: "Buffer time cannot exceed 4 hours (240 minutes)" },
                    valueAsNumber: true
                  })}
                  type="number"
                  label="Buffer Time (minutes)"
                  placeholder="15"
                  min="0"
                  max="240"
                  isInvalid={!!errors.bufferMinutes}
                  errorMessage={errors.bufferMinutes?.message}
                />

                <Controller
                  name="category"
                  control={control}
                  rules={{ required: "Category is required" }}
                  render={({ field }) => (
                    <Select
                      selectedKeys={field.value ? new Set([field.value]) : new Set()}
                      onSelectionChange={(keys) => {
                        const selectedValue = Array.from(keys)[0] as string;
                        field.onChange(selectedValue);
                      }}
                      label="Category"
                      placeholder="Select category"
                      isInvalid={!!errors.category}
                      errorMessage={errors.category?.message}
                    >
                      <SelectItem key="short" value="short">Short (15-60 min)</SelectItem>
                      <SelectItem key="medium" value="medium">Medium (1-3 hours)</SelectItem>
                      <SelectItem key="long" value="long">Long (3-6 hours)</SelectItem>
                      <SelectItem key="full-day" value="full-day">Full Day (6+ hours)</SelectItem>
                    </Select>
                  )}
                />
              </div>

              <div className="flex items-center justify-between">
                <Controller
                  name="active"
                  control={control}
                  render={({ field }) => (
                    <Switch
                      isSelected={field.value}
                      onValueChange={field.onChange}
                    >
                      Active
                    </Switch>
                  )}
                />

                {watchedMinutes && (
                  <div className="text-sm text-gray-600">
                    Total calendar block: {watchedMinutes + (watch("bufferMinutes") || 0)} minutes
                  </div>
                )}
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Scheduling & Concurrency */}
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold mb-4">Scheduling & Concurrency</h3>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  {...register("maxConcurrent", {
                    required: "Max concurrent bookings is required",
                    min: { value: 1, message: "Must allow at least 1 concurrent booking" },
                    max: { value: 50, message: "Cannot exceed 50 concurrent bookings" },
                    valueAsNumber: true
                  })}
                  type="number"
                  label="Max Concurrent Bookings"
                  placeholder="1"
                  min="1"
                  max="50"
                  isInvalid={!!errors.maxConcurrent}
                  errorMessage={errors.maxConcurrent?.message}
                />

                <Input
                  {...register("requiredBreakAfter", {
                    required: "Required break time is required",
                    min: { value: 0, message: "Break time cannot be negative" },
                    max: { value: 480, message: "Break time cannot exceed 8 hours (480 minutes)" },
                    valueAsNumber: true
                  })}
                  type="number"
                  label="Required Break After (minutes)"
                  placeholder="0"
                  min="0"
                  max="480"
                  isInvalid={!!errors.requiredBreakAfter}
                  errorMessage={errors.requiredBreakAfter?.message}
                />
              </div>

              <Controller
                name="allowsBackToBack"
                control={control}
                render={({ field }) => (
                  <Switch
                    isSelected={field.value}
                    onValueChange={field.onChange}
                  >
                    Allow Back-to-Back Bookings
                  </Switch>
                )}
              />
            </div>
          </CardBody>
        </Card>

        {/* Advanced Scheduling Rules */}
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold mb-4">Advanced Scheduling Rules</h3>

            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  {...register("schedulingRules.minAdvanceHours", {
                    required: "Minimum advance hours is required",
                    min: { value: 0, message: "Advance hours cannot be negative" },
                    valueAsNumber: true
                  })}
                  type="number"
                  label="Minimum Advance Hours"
                  placeholder="2"
                  min="0"
                  isInvalid={!!errors.schedulingRules?.minAdvanceHours}
                  errorMessage={errors.schedulingRules?.minAdvanceHours?.message}
                />

                <Input
                  {...register("schedulingRules.maxPerDay", {
                    required: "Max bookings per day is required",
                    min: { value: 1, message: "Must allow at least 1 booking per day" },
                    max: { value: 100, message: "Cannot exceed 100 bookings per day" },
                    valueAsNumber: true
                  })}
                  type="number"
                  label="Max Bookings Per Day"
                  placeholder="8"
                  min="1"
                  max="100"
                  isInvalid={!!errors.schedulingRules?.maxPerDay}
                  errorMessage={errors.schedulingRules?.maxPerDay?.message}
                />
              </div>

              {/* Time Slots */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">Available Time Slots</label>
                  <Button
                    size="sm"
                    variant="light"
                    onPress={addTimeSlot}
                    startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="w-4 h-4" />}
                  >
                    Add Time Slot
                  </Button>
                </div>

                <div className="space-y-2">
                  {timeSlotsFields.map((field, index) => (
                    <div key={field.id} className="flex gap-2">
                      <Input
                        {...register(`schedulingRules.timeSlots.${index}.value` as const, {
                          pattern: {
                            value: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
                            message: "Time must be in HH:MM format (24-hour)"
                          }
                        })}
                        placeholder="09:00"
                        size="sm"
                        isInvalid={!!errors.schedulingRules?.timeSlots?.[index]?.value}
                        errorMessage={errors.schedulingRules?.timeSlots?.[index]?.value?.message}
                      />
                      <Button
                        size="sm"
                        variant="light"
                        color="danger"
                        onPress={() => removeTimeSlot(index)}
                        isIconOnly
                      >
                        <Icon name="icon-[heroicons--x-mark-20-solid]" classNames="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  {timeSlotsFields.length === 0 && (
                    <p className="text-sm text-gray-500">No time slots specified - all business hours available</p>
                  )}
                </div>
              </div>

              {/* Blackout Days */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">Blackout Days</label>
                  <Button
                    size="sm"
                    variant="light"
                    onPress={addBlackoutDay}
                    startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="w-4 h-4" />}
                  >
                    Add Blackout Day
                  </Button>
                </div>

                <div className="space-y-2">
                  {blackoutDaysFields.map((field, index) => (
                    <div key={field.id} className="flex gap-2">
                      <Controller
                        name={`schedulingRules.blackoutDays.${index}.value` as const}
                        control={control}
                        render={({ field }) => (
                          <Select
                            selectedKeys={field.value ? new Set([field.value]) : new Set()}
                            onSelectionChange={(keys) => {
                              const selectedValue = Array.from(keys)[0] as string;
                              field.onChange(selectedValue);
                            }}
                            placeholder="Select day"
                            size="sm"
                          >
                            <SelectItem key="monday" value="monday">Monday</SelectItem>
                            <SelectItem key="tuesday" value="tuesday">Tuesday</SelectItem>
                            <SelectItem key="wednesday" value="wednesday">Wednesday</SelectItem>
                            <SelectItem key="thursday" value="thursday">Thursday</SelectItem>
                            <SelectItem key="friday" value="friday">Friday</SelectItem>
                            <SelectItem key="saturday" value="saturday">Saturday</SelectItem>
                            <SelectItem key="sunday" value="sunday">Sunday</SelectItem>
                          </Select>
                        )}
                      />
                      <Button
                        size="sm"
                        variant="light"
                        color="danger"
                        onPress={() => removeBlackoutDay(index)}
                        isIconOnly
                      >
                        <Icon name="icon-[heroicons--x-mark-20-solid]" classNames="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  {blackoutDaysFields.length === 0 && (
                    <p className="text-sm text-gray-500">No blackout days - available all week</p>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Branch Constraints */}
        <Card>
          <CardBody>
            <h3 className="text-lg font-semibold mb-4">Branch Constraints</h3>

            <div className="space-y-4">
              <Controller
                name="branchConstraints.respectBranchHours"
                control={control}
                render={({ field }) => (
                  <Switch
                    isSelected={field.value}
                    onValueChange={field.onChange}
                  >
                    Respect Branch Operating Hours
                  </Switch>
                )}
              />

              <Input
                {...register("branchConstraints.staffRequired", {
                  required: "Staff required is required",
                  min: { value: 0, message: "Staff required cannot be negative" },
                  max: { value: 20, message: "Staff required cannot exceed 20" },
                  valueAsNumber: true
                })}
                type="number"
                label="Staff Required"
                placeholder="1"
                min="0"
                max="20"
                isInvalid={!!errors.branchConstraints?.staffRequired}
                errorMessage={errors.branchConstraints?.staffRequired?.message}
              />

              {/* Equipment Required */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium">Equipment Required</label>
                  <Button
                    size="sm"
                    variant="light"
                    onPress={addEquipment}
                    startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="w-4 h-4" />}
                  >
                    Add Equipment
                  </Button>
                </div>

                <div className="space-y-2">
                  {equipmentFields.map((field, index) => (
                    <div key={field.id} className="flex gap-2">
                      <Input
                        {...register(`branchConstraints.equipmentRequired.${index}.value` as const, {
                          required: "Equipment name is required"
                        })}
                        placeholder="e.g., scanner, tools, covered-bay"
                        size="sm"
                        isInvalid={!!errors.branchConstraints?.equipmentRequired?.[index]?.value}
                        errorMessage={errors.branchConstraints?.equipmentRequired?.[index]?.value?.message}
                      />
                      <Button
                        size="sm"
                        variant="light"
                        color="danger"
                        onPress={() => removeEquipment(index)}
                        isIconOnly
                      >
                        <Icon name="icon-[heroicons--x-mark-20-solid]" classNames="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                  {equipmentFields.length === 0 && (
                    <p className="text-sm text-gray-500">No special equipment required</p>
                  )}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Form Actions */}
        <Card>
          <CardBody>
            <div className="flex justify-end gap-3">
              <Button
                variant="light"
                onPress={() => router.push('/admin/durations')}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                color="primary"
                isLoading={isSubmitting}
                isDisabled={isSubmitting}
              >
                {isSubmitting ? (isEdit ? "Updating..." : "Creating...") : (isEdit ? "Update Duration" : "Create Duration")}
              </Button>
            </div>
          </CardBody>
        </Card>
      </form>
    </div>
  );
}
