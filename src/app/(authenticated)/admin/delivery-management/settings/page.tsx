import { auth } from "@/auth";
import { redirect } from "next/navigation";
import AdminDeliverySettings from "@/components/admin/delivery/admin-delivery-settings";

export const generateMetadata = async () => {
  return {
    title: "Delivery Management Settings",
    description: "System-wide delivery management configuration and settings",
    keywords: ["admin", "delivery", "settings", "configuration"],
  };
};

export default async function AdminDeliverySettingsPage() {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Management Settings</h1>
        <p className="text-gray-600 mt-1">
          Configure system-wide delivery policies, verification requirements, and operational settings
        </p>
      </div>

      <AdminDeliverySettings />
    </div>
  );
}
