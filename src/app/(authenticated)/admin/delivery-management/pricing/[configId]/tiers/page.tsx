import { Suspense } from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { getDeliveryPricingConfig, getConfigTiers } from '../../actions';
import TiersTable from './components/TiersTable';

interface PageProps {
  params: { configId: string };
}

export default async function ConfigurationTiersPage({ params }: PageProps) {
  return (
    <div className="p-6 space-y-6">
      <Suspense fallback={
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      }>
        <TiersPageContent configId={params.configId} />
      </Suspense>
    </div>
  );
}

async function TiersPageContent({ configId }: { configId: string }) {
  try {
    const [config, tiers] = await Promise.all([
      getDeliveryPricingConfig(configId),
      getConfigTiers(configId)
    ]);

    if (!config) {
      notFound();
    }

    return (
      <>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/admin/delivery-management/pricing/${configId}`}>
              <ActionButton variant="outline" size="sm">
                <Icon name="icon-[mingcute--arrow-left-line]" className="w-4 h-4" />
              </ActionButton>
            </Link>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Pricing Zones</h1>
              <p className="text-gray-600 mt-1">
                Manage distance-based pricing tiers for "{config.name}"
              </p>
            </div>
          </div>
          <Link href={`/admin/delivery-management/pricing/${configId}/tiers/create`}>
            <ActionButton>
              <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
              Add Zone
            </ActionButton>
          </Link>
        </div>

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/admin/delivery-management/pricing" className="hover:text-primary">
            Delivery Pricing
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <Link href={`/admin/delivery-management/pricing/${configId}`} className="hover:text-primary">
            {config.name}
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <span className="text-gray-900">Pricing Zones</span>
        </nav>

        {/* Configuration Summary */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Icon name="icon-[mingcute--info-circle-line]" className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900">Configuration Overview</h3>
              <p className="text-sm text-blue-700">
                Base: KES {config.basePrice} + KES {config.pricePerKm}/km
                {config.minimumFee && ` • Min: KES ${config.minimumFee}`}
                {config.maximumFee && ` • Max: KES ${config.maximumFee}`}
              </p>
            </div>
          </div>
        </div>

        {/* Zones Table */}
        <div className="bg-white rounded-lg border">
          <div className="p-6 border-b">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Distance-Based Zones</h2>
                <p className="text-sm text-gray-600 mt-1">
                  Define pricing tiers based on delivery distance ranges
                </p>
              </div>
              <div className="text-sm text-gray-600">
                {tiers.length} zone{tiers.length !== 1 ? 's' : ''} configured
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <TiersTable data={tiers} configId={configId} />
          </div>
        </div>

        {/* Zone Guidelines */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
          <div className="flex items-start gap-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <Icon name="icon-[mingcute--lightbulb-line]" className="w-5 h-5 text-gray-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Zone Configuration Tips</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Zone 1 (0-5km): Short distance deliveries within the city center</li>
                <li>• Zone 2 (5-15km): Medium distance deliveries to suburbs</li>
                <li>• Zone 3 (15-25km): Long distance deliveries to outer areas</li>
                <li>• Ensure zones don't overlap and cover all expected delivery distances</li>
                <li>• Higher priority zones are checked first when calculating pricing</li>
              </ul>
            </div>
          </div>
        </div>
      </>
    );
  } catch (error) {
    return (
      <div className="p-8 text-center">
        <Icon name="icon-[mingcute--alert-circle-line]" className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load pricing zones</h3>
        <p className="text-gray-600 mb-4">
          {error instanceof Error ? error.message : 'An unexpected error occurred'}
        </p>
        <Link href="/admin/delivery-management/pricing">
          <ActionButton>
            Back to Configurations
          </ActionButton>
        </Link>
      </div>
    );
  }
}
