'use client';

import { useState } from 'react';
import Link from 'next/link';
import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { Chip, Switch } from '@nextui-org/react';
import { DeliveryPricingTier } from '../../../types';
import { deletePricingTier, toggleTierStatus, reorderTiers } from '../../../actions';
import toast from 'react-hot-toast';

interface TiersTableProps {
  data: DeliveryPricingTier[];
  configId: string;
}

export default function TiersTable({ data, configId }: TiersTableProps) {
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const handleDeleteClick = (tier: DeliveryPricingTier) => {
    openDeleteDialog(tier.id, tier.tierName, async () => {
      const formData = new FormData();
      formData.append('tierId', tier.id);
      
      const result = await deletePricingTier(formData);
      
      if (result.success) {
        toast.success('Pricing zone deleted successfully');
      } else {
        throw new Error(result.error || 'Failed to delete pricing zone');
      }
    });
  };

  const handleStatusToggle = async (tier: DeliveryPricingTier, isActive: boolean) => {
    const tierId = tier.id;
    setLoadingStates(prev => ({ ...prev, [tierId]: true }));
    
    try {
      const result = await toggleTierStatus(tierId, isActive);
      
      if (result.success) {
        toast.success(`Zone ${isActive ? 'activated' : 'deactivated'} successfully`);
      } else {
        toast.error(result.error || 'Failed to update status');
      }
    } catch (error) {
      toast.error('Failed to update status');
    } finally {
      setLoadingStates(prev => ({ ...prev, [tierId]: false }));
    }
  };

  const handleReorder = async (tierId: string, direction: 'up' | 'down') => {
    setLoadingStates(prev => ({ ...prev, [tierId]: true }));
    
    try {
      const currentTier = data.find(t => t.id === tierId);
      if (!currentTier) return;

      const currentIndex = data.findIndex(t => t.id === tierId);
      const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
      
      if (newIndex < 0 || newIndex >= data.length) return;

      const newOrder = [...data];
      [newOrder[currentIndex], newOrder[newIndex]] = [newOrder[newIndex], newOrder[currentIndex]];
      
      const orderUpdates = newOrder.map((tier, index) => ({
        id: tier.id,
        priority: index + 1
      }));

      const result = await reorderTiers(orderUpdates);
      
      if (result.success) {
        toast.success('Zone order updated successfully');
      } else {
        toast.error(result.error || 'Failed to update order');
      }
    } catch (error) {
      toast.error('Failed to update order');
    } finally {
      setLoadingStates(prev => ({ ...prev, [tierId]: false }));
    }
  };

  const columns = [
    {
      name: "Zone",
      uid: "zone",
      renderCell: (tier: DeliveryPricingTier) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Icon name="icon-[mingcute--location-line]" className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <p className="font-medium text-gray-900">{tier.tierName}</p>
            {tier.description && (
              <p className="text-sm text-gray-600">{tier.description}</p>
            )}
          </div>
        </div>
      ),
    },
    {
      name: "Distance Range",
      uid: "distance",
      renderCell: (tier: DeliveryPricingTier) => (
        <div>
          <p className="font-medium text-gray-900">
            {tier.distanceFromKm}km - {tier.distanceToKm || '∞'}km
          </p>
          <p className="text-sm text-gray-600">
            {tier.distanceToKm ? 
              `${tier.distanceToKm - tier.distanceFromKm}km range` : 
              'No upper limit'
            }
          </p>
        </div>
      ),
    },
    {
      name: "Pricing",
      uid: "pricing",
      renderCell: (tier: DeliveryPricingTier) => (
        <div>
          <p className="font-medium text-gray-900">
            KES {tier.basePrice} + KES {tier.pricePerKm}/km
          </p>
          {tier.minimumFee && (
            <p className="text-sm text-gray-600">Min: KES {tier.minimumFee}</p>
          )}
        </div>
      ),
    },
    {
      name: "Priority",
      uid: "priority",
      renderCell: (tier: DeliveryPricingTier) => (
        <div className="flex items-center gap-2">
          <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-600 rounded-full text-sm font-medium">
            {tier.priority}
          </span>
          <div className="flex flex-col gap-1">
            <ActionButton
              variant="outline"
              size="sm"
              onClick={() => handleReorder(tier.id, 'up')}
              disabled={loadingStates[tier.id] || tier.priority === 1}
              className="p-1 h-6 w-6"
            >
              <Icon name="icon-[mingcute--up-line]" className="w-3 h-3" />
            </ActionButton>
            <ActionButton
              variant="outline"
              size="sm"
              onClick={() => handleReorder(tier.id, 'down')}
              disabled={loadingStates[tier.id] || tier.priority === data.length}
              className="p-1 h-6 w-6"
            >
              <Icon name="icon-[mingcute--down-line]" className="w-3 h-3" />
            </ActionButton>
          </div>
        </div>
      ),
    },
    {
      name: "Status",
      uid: "status",
      renderCell: (tier: DeliveryPricingTier) => (
        <div className="flex items-center gap-2">
          <Switch
            size="sm"
            isSelected={tier.isActive}
            onValueChange={(isActive) => handleStatusToggle(tier, isActive)}
            isDisabled={loadingStates[tier.id]}
          />
          <span className={`text-sm ${tier.isActive ? 'text-green-600' : 'text-gray-500'}`}>
            {tier.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      ),
    },
    {
      name: "Actions",
      uid: "actions",
      renderCell: (tier: DeliveryPricingTier) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/delivery-management/pricing/${configId}/tiers/${tier.id}/edit`}>
            <ActionButton variant="outline" size="sm">
              <Icon name="icon-[mingcute--edit-line]" className="w-4 h-4" />
            </ActionButton>
          </Link>
          <ActionButton
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(tier)}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            <Icon name="icon-[mingcute--delete-2-line]" className="w-4 h-4" />
          </ActionButton>
        </div>
      ),
    },
  ];

  // Sort data by priority for display
  const sortedData = [...data].sort((a, b) => a.priority - b.priority);

  return (
    <>
      <CustomTable
        title="Pricing Zones"
        columns={columns}
        data={sortedData}
        searchable
        searchPlaceholder="Search zones..."
        emptyContent={
          <div className="text-center py-8">
            <Icon name="icon-[mingcute--location-line]" className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No pricing zones found</h3>
            <p className="text-gray-600 mb-4">Create your first pricing zone to define distance-based pricing.</p>
            <Link href={`/admin/delivery-management/pricing/${configId}/tiers/create`}>
              <ActionButton>
                <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
                Create Zone
              </ActionButton>
            </Link>
          </div>
        }
      />
      <DeleteConfirmationDialog />
    </>
  );
}
