'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { Input, Textarea, Switch, Card, CardBody } from '@nextui-org/react';
import { DeliveryPricingTier } from '../../../types';
import { createPricingTier, updatePricingTier } from '../../../actions';
import toast from 'react-hot-toast';

interface TierFormProps {
  configId: string;
  tier?: DeliveryPricingTier;
  isEditing?: boolean;
}

export default function TierForm({ configId, tier, isEditing = false }: TierFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    tierName: tier?.tierName || '',
    description: tier?.description || '',
    distanceFromKm: tier?.distanceFromKm?.toString() || '',
    distanceToKm: tier?.distanceToKm?.toString() || '',
    basePrice: tier?.basePrice?.toString() || '',
    pricePerKm: tier?.pricePerKm?.toString() || '',
    minimumFee: tier?.minimumFee?.toString() || '',
    maximumFee: tier?.maximumFee?.toString() || '',
    isActive: tier?.isActive ?? true,
    priority: tier?.priority?.toString() || '1',
    baseTimeMinutes: tier?.baseTimeMinutes?.toString() || '',
    timePerKmMinutes: tier?.timePerKmMinutes?.toString() || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formDataObj = new FormData();
      formDataObj.append('configId', configId);
      
      // Add all form fields to FormData
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          formDataObj.append(key, value.toString());
        }
      });

      let result;
      if (isEditing && tier) {
        result = await updatePricingTier(tier.id, formDataObj);
      } else {
        result = await createPricingTier(formDataObj);
      }

      if (result.success) {
        toast.success(`Pricing zone ${isEditing ? 'updated' : 'created'} successfully`);
        router.push(`/admin/delivery-management/pricing/${configId}/tiers`);
      } else {
        toast.error(result.error || `Failed to ${isEditing ? 'update' : 'create'} pricing zone`);
      }
    } catch (error) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} pricing zone`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Zone Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Zone Name"
              placeholder="e.g., Zone 1 - City Center"
              value={formData.tierName}
              onValueChange={(value) => handleInputChange('tierName', value)}
              isRequired
              startContent={<Icon name="icon-[mingcute--location-line]" className="w-4 h-4 text-gray-400" />}
            />
            
            <Input
              label="Priority"
              placeholder="1"
              value={formData.priority}
              onValueChange={(value) => handleInputChange('priority', value)}
              type="number"
              min="1"
              isRequired
              description="Lower numbers are checked first"
            />
          </div>

          <Textarea
            label="Description"
            placeholder="Brief description of this pricing zone"
            value={formData.description}
            onValueChange={(value) => handleInputChange('description', value)}
            maxRows={3}
          />

          <Switch
            isSelected={formData.isActive}
            onValueChange={(value) => handleInputChange('isActive', value)}
          >
            <span className="text-sm">Active Zone</span>
          </Switch>
        </CardBody>
      </Card>

      {/* Distance Range */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Distance Range</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="From Distance (km)"
              placeholder="0"
              value={formData.distanceFromKm}
              onValueChange={(value) => handleInputChange('distanceFromKm', value)}
              type="number"
              min="0"
              step="0.1"
              isRequired
              endContent={<span className="text-gray-400">km</span>}
            />
            
            <Input
              label="To Distance (km)"
              placeholder="5"
              value={formData.distanceToKm}
              onValueChange={(value) => handleInputChange('distanceToKm', value)}
              type="number"
              min="0"
              step="0.1"
              endContent={<span className="text-gray-400">km</span>}
              description="Leave empty for no upper limit"
            />
          </div>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2">
              <Icon name="icon-[mingcute--info-circle-line]" className="w-4 h-4 text-blue-600" />
              <span className="text-sm text-blue-800">
                This zone will apply to deliveries from {formData.distanceFromKm || '0'}km to {formData.distanceToKm || '∞'}km
              </span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Pricing Configuration */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Pricing Configuration</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Base Price (KES)"
              placeholder="150"
              value={formData.basePrice}
              onValueChange={(value) => handleInputChange('basePrice', value)}
              type="number"
              min="0"
              step="0.01"
              isRequired
              startContent={<span className="text-gray-400">KES</span>}
            />
            
            <Input
              label="Price per Kilometer (KES)"
              placeholder="20"
              value={formData.pricePerKm}
              onValueChange={(value) => handleInputChange('pricePerKm', value)}
              type="number"
              min="0"
              step="0.01"
              isRequired
              startContent={<span className="text-gray-400">KES</span>}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Minimum Fee (KES)"
              placeholder="100"
              value={formData.minimumFee}
              onValueChange={(value) => handleInputChange('minimumFee', value)}
              type="number"
              min="0"
              step="0.01"
              startContent={<span className="text-gray-400">KES</span>}
            />
            
            <Input
              label="Maximum Fee (KES)"
              placeholder="500"
              value={formData.maximumFee}
              onValueChange={(value) => handleInputChange('maximumFee', value)}
              type="number"
              min="0"
              step="0.01"
              startContent={<span className="text-gray-400">KES</span>}
            />
          </div>

          {/* Pricing Preview */}
          {formData.basePrice && formData.pricePerKm && formData.distanceFromKm && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Icon name="icon-[mingcute--calculator-line]" className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Pricing Preview</span>
              </div>
              <div className="text-sm text-green-700">
                <p>At {formData.distanceFromKm}km: KES {(parseFloat(formData.basePrice) + parseFloat(formData.pricePerKm) * parseFloat(formData.distanceFromKm)).toFixed(2)}</p>
                {formData.distanceToKm && (
                  <p>At {formData.distanceToKm}km: KES {(parseFloat(formData.basePrice) + parseFloat(formData.pricePerKm) * parseFloat(formData.distanceToKm)).toFixed(2)}</p>
                )}
              </div>
            </div>
          )}
        </CardBody>
      </Card>

      {/* Time Configuration */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Time Configuration</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Base Time (minutes)"
              placeholder="30"
              value={formData.baseTimeMinutes}
              onValueChange={(value) => handleInputChange('baseTimeMinutes', value)}
              type="number"
              min="0"
              endContent={<span className="text-gray-400">min</span>}
            />
            
            <Input
              label="Time per Kilometer (minutes)"
              placeholder="3"
              value={formData.timePerKmMinutes}
              onValueChange={(value) => handleInputChange('timePerKmMinutes', value)}
              type="number"
              min="0"
              endContent={<span className="text-gray-400">min</span>}
            />
          </div>
        </CardBody>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 pt-6 border-t">
        <ActionButton
          variant="outline"
          onClick={() => router.back()}
          disabled={isLoading}
        >
          Cancel
        </ActionButton>
        
        <ActionButton
          type="submit"
          isLoading={isLoading}
          disabled={!formData.tierName || !formData.basePrice || !formData.pricePerKm || !formData.distanceFromKm}
        >
          {isLoading ? 'Saving...' : (isEditing ? 'Update Zone' : 'Create Zone')}
        </ActionButton>
      </div>
    </form>
  );
}
