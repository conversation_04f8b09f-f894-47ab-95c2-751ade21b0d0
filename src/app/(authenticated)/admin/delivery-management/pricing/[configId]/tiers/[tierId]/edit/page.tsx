import { Suspense } from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { getDeliveryPricingConfig, getPricingTier } from '../../../../actions';
import TierForm from '../../components/TierForm';

interface PageProps {
  params: { configId: string; tierId: string };
}

export default async function EditTierPage({ params }: PageProps) {
  return (
    <div className="p-6 space-y-6">
      <Suspense fallback={
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      }>
        <EditTierContent configId={params.configId} tierId={params.tierId} />
      </Suspense>
    </div>
  );
}

async function EditTierContent({ configId, tierId }: { configId: string; tierId: string }) {
  try {
    const [config, tier] = await Promise.all([
      getDeliveryPricingConfig(configId),
      getPricingTier(tierId)
    ]);

    if (!config || !tier) {
      notFound();
    }

    return (
      <>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/admin/delivery-management/pricing/${configId}/tiers`}>
              <ActionButton variant="outline" size="sm">
                <Icon name="icon-[mingcute--arrow-left-line]" className="w-4 h-4" />
              </ActionButton>
            </Link>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Edit Pricing Zone</h1>
              <p className="text-gray-600 mt-1">
                Update settings for "{tier.tierName}"
              </p>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/admin/delivery-management/pricing" className="hover:text-primary">
            Delivery Pricing
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <Link href={`/admin/delivery-management/pricing/${configId}`} className="hover:text-primary">
            {config.name}
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <Link href={`/admin/delivery-management/pricing/${configId}/tiers`} className="hover:text-primary">
            Pricing Zones
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <span className="text-gray-900">Edit {tier.tierName}</span>
        </nav>

        {/* Configuration Context */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Icon name="icon-[mingcute--info-circle-line]" className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium text-blue-900">Configuration: {config.name}</h3>
              <p className="text-sm text-blue-700">
                Base pricing: KES {config.basePrice} + KES {config.pricePerKm}/km
                {config.minimumFee && ` • Min: KES ${config.minimumFee}`}
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-4xl">
          <TierForm configId={configId} tier={tier} isEditing={true} />
        </div>
      </>
    );
  } catch (error) {
    return (
      <div className="p-8 text-center">
        <Icon name="icon-[mingcute--alert-circle-line]" className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load pricing zone</h3>
        <p className="text-gray-600 mb-4">
          {error instanceof Error ? error.message : 'An unexpected error occurred'}
        </p>
        <Link href="/admin/delivery-management/pricing">
          <ActionButton>
            Back to Configurations
          </ActionButton>
        </Link>
      </div>
    );
  }
}
