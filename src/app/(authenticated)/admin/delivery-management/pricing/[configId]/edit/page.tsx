import { Suspense } from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { getDeliveryPricingConfig } from '../../actions';
import ConfigurationForm from '../../components/ConfigurationForm';

interface PageProps {
  params: { configId: string };
}

export default async function EditConfigurationPage({ params }: PageProps) {
  return (
    <div className="p-6 space-y-6">
      <Suspense fallback={
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      }>
        <EditConfigurationContent configId={params.configId} />
      </Suspense>
    </div>
  );
}

async function EditConfigurationContent({ configId }: { configId: string }) {
  try {
    const config = await getDeliveryPricingConfig(configId);

    if (!config) {
      notFound();
    }

    return (
      <>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Link href={`/admin/delivery-management/pricing/${configId}`}>
              <ActionButton variant="outline" size="sm">
                <Icon name="icon-[mingcute--arrow-left-line]" className="w-4 h-4" />
              </ActionButton>
            </Link>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Edit Configuration</h1>
              <p className="text-gray-600 mt-1">
                Update the settings for "{config.name}"
              </p>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600">
          <Link href="/admin/delivery-management/pricing" className="hover:text-primary">
            Delivery Pricing
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <Link href={`/admin/delivery-management/pricing/${configId}`} className="hover:text-primary">
            {config.name}
          </Link>
          <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
          <span className="text-gray-900">Edit</span>
        </nav>

        {/* Form */}
        <div className="max-w-4xl">
          <ConfigurationForm config={config} isEditing={true} />
        </div>
      </>
    );
  } catch (error) {
    return (
      <div className="p-8 text-center">
        <Icon name="icon-[mingcute--alert-circle-line]" className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load configuration</h3>
        <p className="text-gray-600 mb-4">
          {error instanceof Error ? error.message : 'An unexpected error occurred'}
        </p>
        <Link href="/admin/delivery-management/pricing">
          <ActionButton>
            Back to Configurations
          </ActionButton>
        </Link>
      </div>
    );
  }
}
