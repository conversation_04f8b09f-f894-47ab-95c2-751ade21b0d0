'use client';

import { useState } from 'react';
import Link from 'next/link';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { Input, Card, CardBody, Chip } from '@nextui-org/react';
import { testDeliveryPricing } from '../actions';
import { DeliveryPricingTestResponse } from '../types';
import toast from 'react-hot-toast';

const vehicleTypeIcons = {
  motorcycle: 'icon-[mingcute--motorcycle-line]',
  car: 'icon-[mingcute--car-line]',
  bicycle: 'icon-[mingcute--bike-line]',
  van: 'icon-[mingcute--truck-line]'
} as const;

export default function TestDeliveryPricingPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [testData, setTestData] = useState({
    distance: '',
    orderValue: ''
  });
  const [results, setResults] = useState<DeliveryPricingTestResponse | null>(null);

  const handleTest = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!testData.distance) {
      toast.error('Please enter a distance');
      return;
    }

    setIsLoading(true);
    
    try {
      const result = await testDeliveryPricing({
        distance: parseFloat(testData.distance),
        orderValue: testData.orderValue ? parseFloat(testData.orderValue) : undefined
      });

      if (result.success && result.result) {
        setResults(result.result);
        toast.success('Pricing calculated successfully');
      } else {
        toast.error(result.error || 'Failed to calculate pricing');
      }
    } catch (error) {
      toast.error('Failed to calculate pricing');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setTestData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/delivery-management/pricing">
            <ActionButton variant="outline" size="sm">
              <Icon name="icon-[mingcute--arrow-left-line]" className="w-4 h-4" />
            </ActionButton>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Test Delivery Pricing</h1>
            <p className="text-gray-600 mt-1">
              Test your delivery pricing configurations with custom parameters
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <Link href="/admin/delivery-management/pricing" className="hover:text-primary">
          Delivery Pricing
        </Link>
        <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
        <span className="text-gray-900">Test Pricing</span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Test Form */}
        <div className="lg:col-span-1">
          <Card>
            <CardBody className="p-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Icon name="icon-[mingcute--calculator-line]" className="w-5 h-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">Test Parameters</h3>
              </div>

              <form onSubmit={handleTest} className="space-y-4">
                <Input
                  label="Distance (km)"
                  placeholder="12.5"
                  value={testData.distance}
                  onValueChange={(value) => handleInputChange('distance', value)}
                  type="number"
                  min="0"
                  step="0.1"
                  isRequired
                  endContent={<span className="text-gray-400">km</span>}
                  description="Enter the delivery distance in kilometers"
                />

                <Input
                  label="Order Value (KES)"
                  placeholder="1500"
                  value={testData.orderValue}
                  onValueChange={(value) => handleInputChange('orderValue', value)}
                  type="number"
                  min="0"
                  step="0.01"
                  startContent={<span className="text-gray-400">KES</span>}
                  description="Optional: Enter order value to test free delivery thresholds"
                />

                <ActionButton
                  type="submit"
                  isLoading={isLoading}
                  disabled={!testData.distance}
                  className="w-full"
                >
                  {isLoading ? 'Calculating...' : 'Calculate Pricing'}
                </ActionButton>
              </form>

              {/* Example Locations */}
              <div className="mt-6 pt-6 border-t">
                <h4 className="text-sm font-medium text-gray-900 mb-3">Example Locations</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Westlands:</span>
                    <button
                      type="button"
                      onClick={() => handleInputChange('distance', '2.99')}
                      className="text-primary hover:underline"
                    >
                      2.99km
                    </button>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Karen:</span>
                    <button
                      type="button"
                      onClick={() => handleInputChange('distance', '11.61')}
                      className="text-primary hover:underline"
                    >
                      11.61km
                    </button>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Kiambu:</span>
                    <button
                      type="button"
                      onClick={() => handleInputChange('distance', '15.49')}
                      className="text-primary hover:underline"
                    >
                      15.49km
                    </button>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Results */}
        <div className="lg:col-span-2">
          {results ? (
            <div className="space-y-6">
              {/* Test Summary */}
              <Card>
                <CardBody className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Icon name="icon-[mingcute--check-circle-line]" className="w-5 h-5 text-green-600" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900">Test Results</h3>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-600">Test Distance</p>
                      <p className="text-lg font-semibold text-gray-900">{results.distance}km</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Available Options</p>
                      <p className="text-lg font-semibold text-gray-900">{results.deliveryOptions.length}</p>
                    </div>
                  </div>

                  {results.fallbackUsed && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Icon name="icon-[mingcute--alert-triangle-line]" className="w-4 h-4 text-yellow-600" />
                        <span className="text-sm text-yellow-800">Fallback pricing was used</span>
                      </div>
                    </div>
                  )}

                  {results.errors.length > 0 && (
                    <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon name="icon-[mingcute--alert-circle-line]" className="w-4 h-4 text-red-600" />
                        <span className="text-sm font-medium text-red-800">Errors:</span>
                      </div>
                      <ul className="text-sm text-red-700 space-y-1">
                        {results.errors.map((error, index) => (
                          <li key={index}>• {error}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CardBody>
              </Card>

              {/* Delivery Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Available Delivery Options</h3>
                
                {results.deliveryOptions.map((option) => (
                  <Card key={option.id}>
                    <CardBody className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div className="p-2 bg-gray-100 rounded-lg">
                            <Icon 
                              name={vehicleTypeIcons[option.vehicleType as keyof typeof vehicleTypeIcons] || 'icon-[mingcute--truck-line]'} 
                              className="w-5 h-5 text-gray-600" 
                            />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{option.name}</h4>
                            <p className="text-sm text-gray-600">{option.description}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-gray-900">KES {option.fee}</p>
                          <p className="text-sm text-gray-600">{option.estimatedTime} minutes</p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-600">Base Price</p>
                          <p className="font-medium">KES {option.priceBreakdown.basePrice}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Distance Price</p>
                          <p className="font-medium">KES {option.priceBreakdown.distancePrice}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Zone Used</p>
                          <p className="font-medium">{option.priceBreakdown.tierUsed}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Priority</p>
                          <p className="font-medium">#{option.priority}</p>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Chip
                          color={option.isAvailable ? 'success' : 'danger'}
                          variant="flat"
                          size="sm"
                        >
                          {option.isAvailable ? 'Available' : 'Unavailable'}
                        </Chip>
                        <Chip
                          color="primary"
                          variant="flat"
                          size="sm"
                        >
                          {option.vehicleType}
                        </Chip>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <Card>
              <CardBody className="p-12 text-center">
                <Icon name="icon-[mingcute--calculator-line]" className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Test</h3>
                <p className="text-gray-600">
                  Enter a distance and optional order value to see how your pricing configurations work.
                </p>
              </CardBody>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
