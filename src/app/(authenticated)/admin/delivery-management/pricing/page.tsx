import { Suspense } from 'react';
import Link from 'next/link';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { getDeliveryPricingConfigs } from './actions';
import ConfigurationsTable from './components/ConfigurationsTable';

interface PageProps {
  searchParams: { [key: string]: string | string[] | undefined };
}

export default async function DeliveryPricingPage({ searchParams }: PageProps) {
  const searchParamsObj = Object.fromEntries(
    Object.entries(searchParams).map(([key, value]) => [
      key,
      Array.isArray(value) ? value[0] : value || ''
    ])
  );

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Delivery Pricing</h1>
          <p className="text-gray-600 mt-1">
            Manage distance-based delivery pricing configurations and zones
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Link href="/admin/delivery-management/pricing/test">
            <ActionButton variant="outline">
              <Icon name="icon-[mingcute--calculator-line]" className="w-4 h-4" />
              Test Pricing
            </ActionButton>
          </Link>
          <Link href="/admin/delivery-management/pricing/create">
            <ActionButton>
              <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
              Create Configuration
            </ActionButton>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Configurations</p>
              <p className="text-2xl font-semibold text-gray-900">-</p>
            </div>
            <div className="p-2 bg-blue-100 rounded-lg">
              <Icon name="icon-[mingcute--settings-3-line]" className="w-5 h-5 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Configurations</p>
              <p className="text-2xl font-semibold text-green-600">-</p>
            </div>
            <div className="p-2 bg-green-100 rounded-lg">
              <Icon name="icon-[mingcute--check-circle-line]" className="w-5 h-5 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Vehicle Types</p>
              <p className="text-2xl font-semibold text-purple-600">-</p>
            </div>
            <div className="p-2 bg-purple-100 rounded-lg">
              <Icon name="icon-[mingcute--truck-line]" className="w-5 h-5 text-purple-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Pricing Zones</p>
              <p className="text-2xl font-semibold text-orange-600">-</p>
            </div>
            <div className="p-2 bg-orange-100 rounded-lg">
              <Icon name="icon-[mingcute--location-line]" className="w-5 h-5 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Configurations Table */}
      <div className="bg-white rounded-lg border">
        <div className="p-4 border-b">
          <h2 className="text-lg font-medium text-gray-900">Pricing Configurations</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage your delivery pricing configurations and their zones
          </p>
        </div>
        
        <Suspense fallback={
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading configurations...</p>
          </div>
        }>
          <ConfigurationsTableWrapper searchParams={searchParamsObj} />
        </Suspense>
      </div>
    </div>
  );
}

async function ConfigurationsTableWrapper({ searchParams }: { searchParams: Record<string, string> }) {
  try {
    const configs = await getDeliveryPricingConfigs(searchParams);
    return <ConfigurationsTable data={configs} />;
  } catch (error) {
    return (
      <div className="p-8 text-center">
        <Icon name="icon-[mingcute--alert-circle-line]" className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load configurations</h3>
        <p className="text-gray-600 mb-4">
          {error instanceof Error ? error.message : 'An unexpected error occurred'}
        </p>
        <ActionButton onClick={() => window.location.reload()}>
          Try Again
        </ActionButton>
      </div>
    );
  }
}
