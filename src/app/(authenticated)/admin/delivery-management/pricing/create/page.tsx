import Link from 'next/link';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import ConfigurationForm from '../components/ConfigurationForm';

export default function CreateDeliveryPricingConfigPage() {
  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/delivery-management/pricing">
            <ActionButton variant="outline" size="sm">
              <Icon name="icon-[mingcute--arrow-left-line]" className="w-4 h-4" />
            </ActionButton>
          </Link>
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Create Delivery Pricing Configuration</h1>
            <p className="text-gray-600 mt-1">
              Set up a new delivery pricing configuration with distance-based zones
            </p>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <nav className="flex items-center space-x-2 text-sm text-gray-600">
        <Link href="/admin/delivery-management/pricing" className="hover:text-primary">
          Delivery Pricing
        </Link>
        <Icon name="icon-[mingcute--right-line]" className="w-4 h-4" />
        <span className="text-gray-900">Create Configuration</span>
      </nav>

      {/* Form */}
      <div className="max-w-4xl">
        <ConfigurationForm />
      </div>
    </div>
  );
}
