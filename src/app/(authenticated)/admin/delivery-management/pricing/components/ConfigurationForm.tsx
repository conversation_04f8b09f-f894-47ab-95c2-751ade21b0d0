'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { Input, Textarea, Select, SelectItem, Switch, Card, CardBody } from '@nextui-org/react';
import { DeliveryPricingConfig } from '../types';
import { createDeliveryPricingConfig, updateDeliveryPricingConfig } from '../actions';
import toast from 'react-hot-toast';

interface ConfigurationFormProps {
  config?: DeliveryPricingConfig;
  isEditing?: boolean;
}

const vehicleTypes = [
  { key: 'motorcycle', label: 'Motorcycle', icon: 'icon-[mingcute--motorcycle-line]' },
  { key: 'car', label: 'Car', icon: 'icon-[mingcute--car-line]' },
  { key: 'bicycle', label: 'Bicycle', icon: 'icon-[mingcute--bike-line]' },
  { key: 'van', label: 'Van', icon: 'icon-[mingcute--truck-line]' },
];

export default function ConfigurationForm({ config, isEditing = false }: ConfigurationFormProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: config?.name || '',
    description: config?.description || '',
    vehicleType: config?.vehicleType || 'motorcycle',
    basePrice: config?.basePrice?.toString() || '',
    pricePerKm: config?.pricePerKm?.toString() || '',
    isActive: config?.isActive ?? true,
    isDefault: config?.isDefault ?? false,
    priority: config?.priority?.toString() || '1',
    minimumFee: config?.minimumFee?.toString() || '',
    maximumFee: config?.maximumFee?.toString() || '',
    maxDistanceKm: config?.maxDistanceKm?.toString() || '',
    minDistanceKm: config?.minDistanceKm?.toString() || '',
    minimumOrderValue: config?.minimumOrderValue?.toString() || '',
    freeDeliveryThreshold: config?.freeDeliveryThreshold?.toString() || '',
    baseTimeMinutes: config?.baseTimeMinutes?.toString() || '',
    timePerKmMinutes: config?.timePerKmMinutes?.toString() || '',
    preparationBufferMinutes: config?.preparationBufferMinutes?.toString() || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const formDataObj = new FormData();
      
      // Add all form fields to FormData
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== '' && value !== null && value !== undefined) {
          formDataObj.append(key, value.toString());
        }
      });

      let result;
      if (isEditing && config) {
        result = await updateDeliveryPricingConfig(config.id, formDataObj);
      } else {
        result = await createDeliveryPricingConfig(formDataObj);
      }

      if (result.success) {
        toast.success(`Configuration ${isEditing ? 'updated' : 'created'} successfully`);
        router.push('/admin/delivery-management/pricing');
      } else {
        toast.error(result.error || `Failed to ${isEditing ? 'update' : 'create'} configuration`);
      }
    } catch (error) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} configuration`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Configuration Name"
              placeholder="e.g., Standard Motorcycle Delivery"
              value={formData.name}
              onValueChange={(value) => handleInputChange('name', value)}
              isRequired
              startContent={<Icon name="icon-[mingcute--tag-line]" className="w-4 h-4 text-gray-400" />}
            />
            
            <Select
              label="Vehicle Type"
              placeholder="Select vehicle type"
              selectedKeys={[formData.vehicleType]}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                handleInputChange('vehicleType', value);
              }}
              isRequired
            >
              {vehicleTypes.map((type) => (
                <SelectItem key={type.key} value={type.key} startContent={
                  <Icon name={type.icon} className="w-4 h-4" />
                }>
                  {type.label}
                </SelectItem>
              ))}
            </Select>
          </div>

          <Textarea
            label="Description"
            placeholder="Brief description of this delivery configuration"
            value={formData.description}
            onValueChange={(value) => handleInputChange('description', value)}
            maxRows={3}
          />

          <div className="flex items-center gap-6">
            <Switch
              isSelected={formData.isActive}
              onValueChange={(value) => handleInputChange('isActive', value)}
            >
              <span className="text-sm">Active Configuration</span>
            </Switch>
            
            <Switch
              isSelected={formData.isDefault}
              onValueChange={(value) => handleInputChange('isDefault', value)}
            >
              <span className="text-sm">Default Option</span>
            </Switch>
          </div>
        </CardBody>
      </Card>

      {/* Pricing Configuration */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Pricing Configuration</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Base Price (KES)"
              placeholder="150"
              value={formData.basePrice}
              onValueChange={(value) => handleInputChange('basePrice', value)}
              type="number"
              min="0"
              step="0.01"
              isRequired
              startContent={<span className="text-gray-400">KES</span>}
            />
            
            <Input
              label="Price per Kilometer (KES)"
              placeholder="20"
              value={formData.pricePerKm}
              onValueChange={(value) => handleInputChange('pricePerKm', value)}
              type="number"
              min="0"
              step="0.01"
              isRequired
              startContent={<span className="text-gray-400">KES</span>}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Minimum Fee (KES)"
              placeholder="100"
              value={formData.minimumFee}
              onValueChange={(value) => handleInputChange('minimumFee', value)}
              type="number"
              min="0"
              step="0.01"
              startContent={<span className="text-gray-400">KES</span>}
            />
            
            <Input
              label="Maximum Fee (KES)"
              placeholder="1000"
              value={formData.maximumFee}
              onValueChange={(value) => handleInputChange('maximumFee', value)}
              type="number"
              min="0"
              step="0.01"
              startContent={<span className="text-gray-400">KES</span>}
            />
          </div>
        </CardBody>
      </Card>

      {/* Distance & Order Limits */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Distance & Order Limits</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Minimum Distance (km)"
              placeholder="0"
              value={formData.minDistanceKm}
              onValueChange={(value) => handleInputChange('minDistanceKm', value)}
              type="number"
              min="0"
              step="0.1"
              endContent={<span className="text-gray-400">km</span>}
            />
            
            <Input
              label="Maximum Distance (km)"
              placeholder="25"
              value={formData.maxDistanceKm}
              onValueChange={(value) => handleInputChange('maxDistanceKm', value)}
              type="number"
              min="0"
              step="0.1"
              endContent={<span className="text-gray-400">km</span>}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Minimum Order Value (KES)"
              placeholder="0"
              value={formData.minimumOrderValue}
              onValueChange={(value) => handleInputChange('minimumOrderValue', value)}
              type="number"
              min="0"
              step="0.01"
              startContent={<span className="text-gray-400">KES</span>}
            />
            
            <Input
              label="Free Delivery Threshold (KES)"
              placeholder="2000"
              value={formData.freeDeliveryThreshold}
              onValueChange={(value) => handleInputChange('freeDeliveryThreshold', value)}
              type="number"
              min="0"
              step="0.01"
              startContent={<span className="text-gray-400">KES</span>}
            />
          </div>
        </CardBody>
      </Card>

      {/* Time Configuration */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Time Configuration</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Input
              label="Base Time (minutes)"
              placeholder="30"
              value={formData.baseTimeMinutes}
              onValueChange={(value) => handleInputChange('baseTimeMinutes', value)}
              type="number"
              min="0"
              endContent={<span className="text-gray-400">min</span>}
            />
            
            <Input
              label="Time per Kilometer (minutes)"
              placeholder="3"
              value={formData.timePerKmMinutes}
              onValueChange={(value) => handleInputChange('timePerKmMinutes', value)}
              type="number"
              min="0"
              endContent={<span className="text-gray-400">min</span>}
            />
            
            <Input
              label="Preparation Buffer (minutes)"
              placeholder="15"
              value={formData.preparationBufferMinutes}
              onValueChange={(value) => handleInputChange('preparationBufferMinutes', value)}
              type="number"
              min="0"
              endContent={<span className="text-gray-400">min</span>}
            />
          </div>
        </CardBody>
      </Card>

      {/* Priority */}
      <Card>
        <CardBody className="p-6 space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Display Settings</h3>
          
          <Input
            label="Priority"
            placeholder="1"
            value={formData.priority}
            onValueChange={(value) => handleInputChange('priority', value)}
            type="number"
            min="1"
            description="Lower numbers appear first in the delivery options list"
            className="max-w-xs"
          />
        </CardBody>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-3 pt-6 border-t">
        <ActionButton
          variant="outline"
          onClick={() => router.back()}
          disabled={isLoading}
        >
          Cancel
        </ActionButton>
        
        <ActionButton
          type="submit"
          isLoading={isLoading}
          disabled={!formData.name || !formData.basePrice || !formData.pricePerKm}
        >
          {isLoading ? 'Saving...' : (isEditing ? 'Update Configuration' : 'Create Configuration')}
        </ActionButton>
      </div>
    </form>
  );
}
