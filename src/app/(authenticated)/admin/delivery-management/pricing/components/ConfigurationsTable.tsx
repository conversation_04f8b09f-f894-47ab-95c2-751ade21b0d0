'use client';

import { useState } from 'react';
import Link from 'next/link';
import { CustomTable, useDeleteConfirmation } from '@/components/custom-table';
import { ActionButton } from '@/components/ui/action-button';
import { Icon } from '@/components/icon';
import { Chip, Switch } from '@nextui-org/react';
import { DeliveryPricingConfig } from '../types';
import { deleteDeliveryPricingConfig, toggleConfigStatus } from '../actions';
import toast from 'react-hot-toast';

interface ConfigurationsTableProps {
  data: DeliveryPricingConfig[];
}

const vehicleTypeColors = {
  motorcycle: 'primary',
  car: 'success',
  bicycle: 'warning',
  van: 'secondary'
} as const;

const vehicleTypeIcons = {
  motorcycle: 'icon-[mingcute--motorcycle-line]',
  car: 'icon-[mingcute--car-line]',
  bicycle: 'icon-[mingcute--bike-line]',
  van: 'icon-[mingcute--truck-line]'
} as const;

export default function ConfigurationsTable({ data }: ConfigurationsTableProps) {
  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const handleDeleteClick = (config: DeliveryPricingConfig) => {
    openDeleteDialog(config.id, config.name, async () => {
      const formData = new FormData();
      formData.append('id', config.id);
      
      const result = await deleteDeliveryPricingConfig(formData);
      
      if (result.success) {
        toast.success('Configuration deleted successfully');
      } else {
        throw new Error(result.error || 'Failed to delete configuration');
      }
    });
  };

  const handleStatusToggle = async (config: DeliveryPricingConfig, isActive: boolean) => {
    const configId = config.id;
    setLoadingStates(prev => ({ ...prev, [configId]: true }));
    
    try {
      const result = await toggleConfigStatus(configId, isActive);
      
      if (result.success) {
        toast.success(`Configuration ${isActive ? 'activated' : 'deactivated'} successfully`);
      } else {
        toast.error(result.error || 'Failed to update status');
      }
    } catch (error) {
      toast.error('Failed to update status');
    } finally {
      setLoadingStates(prev => ({ ...prev, [configId]: false }));
    }
  };

  const columns = [
    {
      name: "Configuration",
      uid: "name",
      renderCell: (config: DeliveryPricingConfig) => (
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gray-100 rounded-lg">
            <Icon 
              name={vehicleTypeIcons[config.vehicleType]} 
              className="w-5 h-5 text-gray-600" 
            />
          </div>
          <div>
            <p className="font-medium text-gray-900">{config.name}</p>
            {config.description && (
              <p className="text-sm text-gray-600">{config.description}</p>
            )}
          </div>
        </div>
      ),
    },
    {
      name: "Vehicle Type",
      uid: "vehicleType",
      renderCell: (config: DeliveryPricingConfig) => (
        <Chip
          color={vehicleTypeColors[config.vehicleType]}
          variant="flat"
          size="sm"
          startContent={
            <Icon 
              name={vehicleTypeIcons[config.vehicleType]} 
              className="w-3 h-3" 
            />
          }
        >
          {config.vehicleType.charAt(0).toUpperCase() + config.vehicleType.slice(1)}
        </Chip>
      ),
    },
    {
      name: "Pricing",
      uid: "pricing",
      renderCell: (config: DeliveryPricingConfig) => (
        <div>
          <p className="font-medium text-gray-900">
            KES {config.basePrice} + KES {config.pricePerKm}/km
          </p>
          {config.minimumFee && (
            <p className="text-sm text-gray-600">Min: KES {config.minimumFee}</p>
          )}
        </div>
      ),
    },
    {
      name: "Distance Range",
      uid: "distance",
      renderCell: (config: DeliveryPricingConfig) => (
        <div>
          <p className="text-sm text-gray-900">
            {config.minDistanceKm || 0}km - {config.maxDistanceKm || '∞'}km
          </p>
        </div>
      ),
    },
    {
      name: "Status",
      uid: "status",
      renderCell: (config: DeliveryPricingConfig) => (
        <div className="flex items-center gap-2">
          <Switch
            size="sm"
            isSelected={config.isActive}
            onValueChange={(isActive) => handleStatusToggle(config, isActive)}
            isDisabled={loadingStates[config.id]}
          />
          <span className={`text-sm ${config.isActive ? 'text-green-600' : 'text-gray-500'}`}>
            {config.isActive ? 'Active' : 'Inactive'}
          </span>
          {config.isDefault && (
            <Chip color="primary" variant="flat" size="sm">
              Default
            </Chip>
          )}
        </div>
      ),
    },
    {
      name: "Zones",
      uid: "zones",
      renderCell: (config: DeliveryPricingConfig) => (
        <div className="text-center">
          <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-medium">
            {config.tiers?.length || 0}
          </span>
        </div>
      ),
    },
    {
      name: "Actions",
      uid: "actions",
      renderCell: (config: DeliveryPricingConfig) => (
        <div className="flex items-center gap-2">
          <Link href={`/admin/delivery-management/pricing/${config.id}`}>
            <ActionButton variant="outline" size="sm">
              <Icon name="icon-[mingcute--eye-line]" className="w-4 h-4" />
            </ActionButton>
          </Link>
          <Link href={`/admin/delivery-management/pricing/${config.id}/tiers`}>
            <ActionButton variant="outline" size="sm">
              <Icon name="icon-[mingcute--location-line]" className="w-4 h-4" />
            </ActionButton>
          </Link>
          <Link href={`/admin/delivery-management/pricing/${config.id}/edit`}>
            <ActionButton variant="outline" size="sm">
              <Icon name="icon-[mingcute--edit-line]" className="w-4 h-4" />
            </ActionButton>
          </Link>
          <ActionButton
            variant="outline"
            size="sm"
            onClick={() => handleDeleteClick(config)}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            <Icon name="icon-[mingcute--delete-2-line]" className="w-4 h-4" />
          </ActionButton>
        </div>
      ),
    },
  ];

  return (
    <>
      <CustomTable
        title="Delivery Pricing Configurations"
        columns={columns}
        data={data}
        searchable
        searchPlaceholder="Search configurations..."
        emptyContent={
          <div className="text-center py-8">
            <Icon name="icon-[mingcute--settings-3-line]" className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No configurations found</h3>
            <p className="text-gray-600 mb-4">Get started by creating your first delivery pricing configuration.</p>
            <Link href="/admin/delivery-management/pricing/create">
              <ActionButton>
                <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
                Create Configuration
              </ActionButton>
            </Link>
          </div>
        }
      />
      <DeleteConfirmationDialog />
    </>
  );
}
