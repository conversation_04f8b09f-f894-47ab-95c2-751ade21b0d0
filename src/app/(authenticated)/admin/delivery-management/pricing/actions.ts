'use server';

import { api } from '@/lib/api';
import { revalidatePath } from 'next/cache';
import { auth } from '@/auth';
import {
  DeliveryPricingConfig,
  DeliveryPricingTier,
  CreateDeliveryPricingConfigRequest,
  CreateDeliveryPricingTierRequest,
  DeliveryPricingTestResponse,
  BulkUpdateRequest,
  TierReorderRequest
} from './types';

// Configuration management actions
export async function getDeliveryPricingConfigs(searchParams?: Record<string, string>) {
  try {
    const session = await auth();
    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    const configs = await api.get<DeliveryPricingConfig[]>('admin/delivery-pricing', searchParams);
    return configs || [];
  } catch (error) {
    console.error('Failed to fetch delivery pricing configs:', error);
    throw error;
  }
}

export async function getDeliveryPricingConfig(id: string) {
  try {
    const session = await auth();
    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    const config = await api.get<DeliveryPricingConfig>(`admin/delivery-pricing/${id}`);
    return config;
  } catch (error) {
    console.error('Failed to fetch delivery pricing config:', error);
    throw error;
  }
}

export async function createDeliveryPricingConfig(data: FormData): Promise<{ success: boolean; config?: DeliveryPricingConfig; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const configData: CreateDeliveryPricingConfigRequest = {
      name: data.get('name') as string,
      description: data.get('description') as string || undefined,
      vehicleType: data.get('vehicleType') as 'motorcycle' | 'car' | 'bicycle' | 'van',
      basePrice: parseFloat(data.get('basePrice') as string),
      pricePerKm: parseFloat(data.get('pricePerKm') as string),
      isActive: data.get('isActive') === 'true',
      isDefault: data.get('isDefault') === 'true',
      priority: data.get('priority') ? parseInt(data.get('priority') as string) : undefined,
      minimumFee: data.get('minimumFee') ? parseFloat(data.get('minimumFee') as string) : undefined,
      maximumFee: data.get('maximumFee') ? parseFloat(data.get('maximumFee') as string) : undefined,
      maxDistanceKm: data.get('maxDistanceKm') ? parseFloat(data.get('maxDistanceKm') as string) : undefined,
      minDistanceKm: data.get('minDistanceKm') ? parseFloat(data.get('minDistanceKm') as string) : undefined,
      minimumOrderValue: data.get('minimumOrderValue') ? parseFloat(data.get('minimumOrderValue') as string) : undefined,
      freeDeliveryThreshold: data.get('freeDeliveryThreshold') ? parseFloat(data.get('freeDeliveryThreshold') as string) : undefined,
      baseTimeMinutes: data.get('baseTimeMinutes') ? parseInt(data.get('baseTimeMinutes') as string) : undefined,
      timePerKmMinutes: data.get('timePerKmMinutes') ? parseInt(data.get('timePerKmMinutes') as string) : undefined,
      preparationBufferMinutes: data.get('preparationBufferMinutes') ? parseInt(data.get('preparationBufferMinutes') as string) : undefined,
    };

    const config = await api.post<DeliveryPricingConfig>('admin/delivery-pricing', configData);
    
    revalidatePath('/admin/delivery-management/pricing');
    return { success: true, config };
  } catch (error) {
    console.error('Failed to create delivery pricing config:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create configuration'
    };
  }
}

export async function updateDeliveryPricingConfig(id: string, data: FormData): Promise<{ success: boolean; config?: DeliveryPricingConfig; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const configData: Partial<CreateDeliveryPricingConfigRequest> = {};
    
    // Only include fields that are present in the form data
    if (data.get('name')) configData.name = data.get('name') as string;
    if (data.get('description')) configData.description = data.get('description') as string;
    if (data.get('vehicleType')) configData.vehicleType = data.get('vehicleType') as 'motorcycle' | 'car' | 'bicycle' | 'van';
    if (data.get('basePrice')) configData.basePrice = parseFloat(data.get('basePrice') as string);
    if (data.get('pricePerKm')) configData.pricePerKm = parseFloat(data.get('pricePerKm') as string);
    if (data.has('isActive')) configData.isActive = data.get('isActive') === 'true';
    if (data.has('isDefault')) configData.isDefault = data.get('isDefault') === 'true';
    if (data.get('priority')) configData.priority = parseInt(data.get('priority') as string);
    if (data.get('minimumFee')) configData.minimumFee = parseFloat(data.get('minimumFee') as string);
    if (data.get('maximumFee')) configData.maximumFee = parseFloat(data.get('maximumFee') as string);
    if (data.get('maxDistanceKm')) configData.maxDistanceKm = parseFloat(data.get('maxDistanceKm') as string);
    if (data.get('minDistanceKm')) configData.minDistanceKm = parseFloat(data.get('minDistanceKm') as string);
    if (data.get('minimumOrderValue')) configData.minimumOrderValue = parseFloat(data.get('minimumOrderValue') as string);
    if (data.get('freeDeliveryThreshold')) configData.freeDeliveryThreshold = parseFloat(data.get('freeDeliveryThreshold') as string);
    if (data.get('baseTimeMinutes')) configData.baseTimeMinutes = parseInt(data.get('baseTimeMinutes') as string);
    if (data.get('timePerKmMinutes')) configData.timePerKmMinutes = parseInt(data.get('timePerKmMinutes') as string);
    if (data.get('preparationBufferMinutes')) configData.preparationBufferMinutes = parseInt(data.get('preparationBufferMinutes') as string);

    const config = await api.put<DeliveryPricingConfig>(`admin/delivery-pricing/${id}`, configData);
    
    revalidatePath('/admin/delivery-management/pricing');
    revalidatePath(`/admin/delivery-management/pricing/${id}`);
    return { success: true, config };
  } catch (error) {
    console.error('Failed to update delivery pricing config:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update configuration'
    };
  }
}

export async function deleteDeliveryPricingConfig(data: FormData): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const id = data.get('id') as string;
    await api.destroy(id, 'admin/delivery-pricing');

    revalidatePath('/admin/delivery-management/pricing');
    return { success: true };
  } catch (error) {
    console.error('Failed to delete delivery pricing config:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete configuration'
    };
  }
}

export async function toggleConfigStatus(id: string, isActive: boolean): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const endpoint = isActive ? `admin/delivery-pricing/${id}/activate` : `admin/delivery-pricing/${id}/deactivate`;
    await api.patch(endpoint);

    revalidatePath('/admin/delivery-management/pricing');
    return { success: true };
  } catch (error) {
    console.error('Failed to toggle config status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update status'
    };
  }
}

export async function bulkUpdateConfigs(data: BulkUpdateRequest): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    await api.patch('admin/delivery-pricing/bulk-update', data);

    revalidatePath('/admin/delivery-management/pricing');
    return { success: true };
  } catch (error) {
    console.error('Failed to bulk update configs:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update configurations'
    };
  }
}

// Tier management actions
export async function getConfigTiers(configId: string) {
  try {
    const session = await auth();
    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    const tiers = await api.get<DeliveryPricingTier[]>(`admin/delivery-pricing/${configId}/tiers`);
    return tiers || [];
  } catch (error) {
    console.error('Failed to fetch config tiers:', error);
    throw error;
  }
}

export async function createPricingTier(data: FormData): Promise<{ success: boolean; tier?: DeliveryPricingTier; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const configId = data.get('configId') as string;
    const tierData: CreateDeliveryPricingTierRequest = {
      tierName: data.get('tierName') as string,
      description: data.get('description') as string || undefined,
      distanceFromKm: parseFloat(data.get('distanceFromKm') as string),
      distanceToKm: data.get('distanceToKm') ? parseFloat(data.get('distanceToKm') as string) : undefined,
      basePrice: parseFloat(data.get('basePrice') as string),
      pricePerKm: parseFloat(data.get('pricePerKm') as string),
      flatRate: data.get('flatRate') ? parseFloat(data.get('flatRate') as string) : undefined,
      minimumFee: data.get('minimumFee') ? parseFloat(data.get('minimumFee') as string) : undefined,
      maximumFee: data.get('maximumFee') ? parseFloat(data.get('maximumFee') as string) : undefined,
      baseTimeMinutes: data.get('baseTimeMinutes') ? parseInt(data.get('baseTimeMinutes') as string) : undefined,
      timePerKmMinutes: data.get('timePerKmMinutes') ? parseInt(data.get('timePerKmMinutes') as string) : undefined,
      isActive: data.get('isActive') !== 'false',
      priority: data.get('priority') ? parseInt(data.get('priority') as string) : undefined,
    };

    const tier = await api.post<DeliveryPricingTier>(`admin/delivery-pricing/${configId}/tiers`, tierData);

    revalidatePath(`/admin/delivery-management/pricing/${configId}/tiers`);
    return { success: true, tier };
  } catch (error) {
    console.error('Failed to create pricing tier:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create tier'
    };
  }
}

export async function updatePricingTier(tierId: string, data: FormData): Promise<{ success: boolean; tier?: DeliveryPricingTier; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const tierData: Partial<CreateDeliveryPricingTierRequest> = {};

    if (data.get('tierName')) tierData.tierName = data.get('tierName') as string;
    if (data.get('description')) tierData.description = data.get('description') as string;
    if (data.get('distanceFromKm')) tierData.distanceFromKm = parseFloat(data.get('distanceFromKm') as string);
    if (data.get('distanceToKm')) tierData.distanceToKm = parseFloat(data.get('distanceToKm') as string);
    if (data.get('basePrice')) tierData.basePrice = parseFloat(data.get('basePrice') as string);
    if (data.get('pricePerKm')) tierData.pricePerKm = parseFloat(data.get('pricePerKm') as string);
    if (data.get('flatRate')) tierData.flatRate = parseFloat(data.get('flatRate') as string);
    if (data.get('minimumFee')) tierData.minimumFee = parseFloat(data.get('minimumFee') as string);
    if (data.get('maximumFee')) tierData.maximumFee = parseFloat(data.get('maximumFee') as string);
    if (data.get('baseTimeMinutes')) tierData.baseTimeMinutes = parseInt(data.get('baseTimeMinutes') as string);
    if (data.get('timePerKmMinutes')) tierData.timePerKmMinutes = parseInt(data.get('timePerKmMinutes') as string);
    if (data.has('isActive')) tierData.isActive = data.get('isActive') === 'true';
    if (data.get('priority')) tierData.priority = parseInt(data.get('priority') as string);

    const tier = await api.put<DeliveryPricingTier>(`admin/delivery-pricing/tiers/${tierId}`, tierData);

    revalidatePath('/admin/delivery-management/pricing');
    return { success: true, tier };
  } catch (error) {
    console.error('Failed to update pricing tier:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update tier'
    };
  }
}

export async function deletePricingTier(data: FormData): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const tierId = data.get('id') as string;
    await api.destroy(tierId, 'admin/delivery-pricing/tiers');

    revalidatePath('/admin/delivery-management/pricing');
    return { success: true };
  } catch (error) {
    console.error('Failed to delete pricing tier:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to delete tier'
    };
  }
}

export async function reorderTiers(configId: string, tiers: TierReorderRequest): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    await api.patch(`admin/delivery-pricing/${configId}/tiers/reorder`, tiers);

    revalidatePath(`/admin/delivery-management/pricing/${configId}/tiers`);
    return { success: true };
  } catch (error) {
    console.error('Failed to reorder tiers:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to reorder tiers'
    };
  }
}

export async function toggleTierStatus(tierId: string, isActive: boolean): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const endpoint = isActive ? `admin/delivery-pricing/tiers/${tierId}/activate` : `admin/delivery-pricing/tiers/${tierId}/deactivate`;
    await api.patch(endpoint);

    revalidatePath('/admin/delivery-management/pricing');
    return { success: true };
  } catch (error) {
    console.error('Failed to toggle tier status:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update tier status'
    };
  }
}

// Testing action
export async function testDeliveryPricing(data: { distance: number; orderValue?: number }): Promise<{ success: boolean; result?: DeliveryPricingTestResponse; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const result = await api.post<DeliveryPricingTestResponse>('admin/delivery-pricing/test', data);

    return { success: true, result };
  } catch (error) {
    console.error('Failed to test delivery pricing:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to test pricing'
    };
  }
}



// Get single pricing tier
export async function getPricingTier(tierId: string): Promise<DeliveryPricingTier | null> {
  try {
    const session = await auth();
    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    const response = await api.get<DeliveryPricingTier>(`admin/delivery-pricing/tiers/${tierId}`);
    return response;
  } catch (error) {
    console.error('Error fetching pricing tier:', error);
    return null;
  }
}
