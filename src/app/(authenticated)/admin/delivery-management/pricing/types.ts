export interface DeliveryPricingConfig {
  id: string;
  name: string;
  description?: string;
  vehicleType: 'motorcycle' | 'car' | 'bicycle' | 'van';
  isActive: boolean;
  isDefault: boolean;
  priority: number;
  basePrice: string;
  pricePerKm: string;
  minimumFee?: string;
  maximumFee?: string;
  maxDistanceKm?: string;
  minDistanceKm?: string;
  minimumOrderValue?: string;
  freeDeliveryThreshold?: string;
  baseTimeMinutes?: number;
  timePerKmMinutes?: string;
  preparationBufferMinutes?: number;
  workingHours?: any;
  blackoutDates?: any;
  availableOnHolidays?: boolean;
  surgeMultipliers?: any;
  weatherAdjustments?: any;
  createdByAdminId?: string;
  updatedByAdminId?: string;
  adminNotes?: string;
  createdAt: string;
  updatedAt: string;
  tiers?: DeliveryPricingTier[];
  displayName?: string;
  isAvailable?: boolean;
}

export interface DeliveryPricingTier {
  id: string;
  deliveryPricingConfigId: string;
  tierName: string;
  distanceFromKm: string;
  distanceToKm: string;
  basePrice: string;
  pricePerKm: string;
  flatRate?: string;
  baseTimeMinutes: number;
  timePerKmMinutes: string;
  minimumFee?: string;
  maximumFee?: string;
  isActive: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateDeliveryPricingConfigRequest {
  name: string;
  description?: string;
  vehicleType: 'motorcycle' | 'car' | 'bicycle' | 'van';
  basePrice: number;
  pricePerKm: number;
  isActive?: boolean;
  isDefault?: boolean;
  priority?: number;
  minimumFee?: number;
  maximumFee?: number;
  maxDistanceKm?: number;
  minDistanceKm?: number;
  minimumOrderValue?: number;
  freeDeliveryThreshold?: number;
  baseTimeMinutes?: number;
  timePerKmMinutes?: number;
  preparationBufferMinutes?: number;
}

export interface CreateDeliveryPricingTierRequest {
  tierName: string;
  description?: string;
  distanceFromKm: number;
  distanceToKm?: number;
  basePrice: number;
  pricePerKm: number;
  flatRate?: number;
  minimumFee?: number;
  maximumFee?: number;
  baseTimeMinutes?: number;
  timePerKmMinutes?: number;
  isActive?: boolean;
  priority?: number;
}

export interface DeliveryPricingTestRequest {
  distance: number;
  orderValue?: number;
}

export interface DeliveryPricingTestResponse {
  deliveryOptions: DeliveryOption[];
  distance: number;
  errors: string[];
  fallbackUsed: boolean;
}

export interface DeliveryOption {
  id: string;
  name: string;
  description: string;
  estimatedTime: number;
  fee: number;
  vehicleType: string;
  isAvailable: boolean;
  distance: number;
  priceBreakdown: {
    basePrice: number;
    distancePrice: number;
    totalDistance: number;
    tierUsed: string;
    appliedDiscounts: string[];
  };
  configId: string;
  priority: number;
}

export interface BulkUpdateRequest {
  updates: Array<{
    id: string;
    isActive?: boolean;
    priority?: number;
    basePrice?: number;
    pricePerKm?: number;
  }>;
}

export interface TierReorderRequest {
  tiers: Array<{
    id: string;
    priority: number;
  }>;
}
