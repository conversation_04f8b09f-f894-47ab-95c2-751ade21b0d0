import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import AdminDeliveryAnalytics from "@/components/admin/delivery/admin-delivery-analytics";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface DeliveryAnalytics {
  overview: {
    total_deliveries: number;
    success_rate: number;
    average_delivery_time: number;
    total_revenue: number;
    active_vendors: number;
    coverage_areas: number;
  };
  trends: {
    daily_deliveries: Array<{
      date: string;
      deliveries: number;
      success_rate: number;
    }>;
    vendor_performance: Array<{
      vendor_id: string;
      vendor_name: string;
      deliveries: number;
      success_rate: number;
      rating: number;
    }>;
    regional_performance: Array<{
      region: string;
      deliveries: number;
      success_rate: number;
      average_time: number;
    }>;
  };
  insights: {
    top_performing_areas: Array<{
      area_name: string;
      vendor_name: string;
      success_rate: number;
      order_count: number;
    }>;
    improvement_opportunities: Array<{
      area_name: string;
      vendor_name: string;
      issue: string;
      impact: 'high' | 'medium' | 'low';
    }>;
  };
}

const fetchDeliveryAnalytics = cache(
  (timeframe: string = '30d') =>
    api.get<DeliveryAnalytics>(`admin/delivery-management/analytics?timeframe=${timeframe}`),
);

export const generateMetadata = async () => {
  return {
    title: "Delivery Analytics & Reports",
    description: "Performance analytics and reporting for delivery management",
    keywords: ["admin", "delivery", "analytics", "reports", "performance"],
  };
};

export default async function AdminDeliveryAnalyticsPage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  const timeframe = searchParams.timeframe || '30d';
  
  // Fetch analytics data
  const analytics = await fetchDeliveryAnalytics(timeframe).catch(() => null);

  // Demo data fallback
  const demoAnalytics: DeliveryAnalytics = {
    overview: {
      total_deliveries: 15420,
      success_rate: 94.2,
      average_delivery_time: 28,
      total_revenue: 2850000,
      active_vendors: 247,
      coverage_areas: 1834,
    },
    trends: {
      daily_deliveries: [
        { date: '2024-01-01', deliveries: 450, success_rate: 95.2 },
        { date: '2024-01-02', deliveries: 520, success_rate: 93.8 },
        { date: '2024-01-03', deliveries: 480, success_rate: 96.1 },
        { date: '2024-01-04', deliveries: 610, success_rate: 92.5 },
        { date: '2024-01-05', deliveries: 580, success_rate: 94.7 },
        { date: '2024-01-06', deliveries: 650, success_rate: 93.2 },
        { date: '2024-01-07', deliveries: 590, success_rate: 95.8 },
      ],
      vendor_performance: [
        { vendor_id: 'v1', vendor_name: 'FastDelivery Co.', deliveries: 1250, success_rate: 96.5, rating: 4.8 },
        { vendor_id: 'v2', vendor_name: 'QuickCourier', deliveries: 890, success_rate: 92.1, rating: 4.5 },
        { vendor_id: 'v3', vendor_name: 'SpeedyService', deliveries: 234, success_rate: 78.5, rating: 3.9 },
        { vendor_id: 'v4', vendor_name: 'ReliableRiders', deliveries: 567, success_rate: 89.3, rating: 4.2 },
        { vendor_id: 'v5', vendor_name: 'ExpressDelivery', deliveries: 445, success_rate: 91.7, rating: 4.4 },
      ],
      regional_performance: [
        { region: 'Nairobi CBD', deliveries: 2340, success_rate: 95.8, average_time: 22 },
        { region: 'Westlands', deliveries: 1890, success_rate: 93.2, average_time: 26 },
        { region: 'Karen', deliveries: 1456, success_rate: 91.5, average_time: 32 },
        { region: 'Kiambu', deliveries: 1123, success_rate: 88.7, average_time: 35 },
        { region: 'Eastlands', deliveries: 890, success_rate: 85.3, average_time: 38 },
      ],
    },
    insights: {
      top_performing_areas: [
        { area_name: 'Downtown Circle', vendor_name: 'FastDelivery Co.', success_rate: 96.5, order_count: 1250 },
        { area_name: 'Westlands Zone', vendor_name: 'QuickCourier', success_rate: 93.2, order_count: 890 },
        { area_name: 'Karen Heights', vendor_name: 'ReliableRiders', success_rate: 91.5, order_count: 567 },
      ],
      improvement_opportunities: [
        { area_name: 'Eastlands Coverage', vendor_name: 'SpeedyService', issue: 'Low success rate (78.5%)', impact: 'high' },
        { area_name: 'Kiambu Extension', vendor_name: 'ExpressDelivery', issue: 'High delivery times (35min avg)', impact: 'medium' },
        { area_name: 'Industrial Area', vendor_name: 'QuickCourier', issue: 'Limited coverage hours', impact: 'low' },
      ],
    },
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Delivery Analytics & Reports</h1>
        <p className="text-gray-600 mt-1">
          Performance insights and analytics for the delivery management system
        </p>
      </div>

      <AdminDeliveryAnalytics
        analytics={analytics || demoAnalytics}
        timeframe={timeframe}
      />
    </div>
  );
}
