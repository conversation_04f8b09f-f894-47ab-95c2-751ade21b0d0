import { auth } from "@/auth";
import { api } from "@/lib/api";
import { cache } from "react";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import AdminVerificationQueue from "@/components/admin/delivery/admin-verification-queue";
import { PaginatedData } from "@/types";

export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface VerificationDocument {
  id: string;
  type: 'business_license' | 'insurance_certificate' | 'vehicle_registration' | 'driver_license';
  name: string;
  url: string;
  status: 'pending' | 'approved' | 'rejected';
  uploaded_at: string;
}

interface VerificationRequest {
  id: string;
  vendor: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  documents: VerificationDocument[];
  submitted_at: string;
  reviewed_at?: string;
  reviewer_notes?: string;
  priority: 'low' | 'medium' | 'high';
  estimated_review_time: number; // in hours
}

const fetchVerificationQueue = cache(
  (searchParams?: Record<string, string>) =>
    api.get<PaginatedData<VerificationRequest>>(
      'admin/delivery-management/verification-queue',
      searchParams,
    ),
);

export const generateMetadata = async () => {
  return {
    title: "Vendor Verification Queue",
    description: "Review and approve vendor verification requests",
    keywords: ["admin", "delivery", "verification", "vendors"],
  };
};

export default async function AdminVerificationQueuePage({
  searchParams,
}: {
  searchParams: Record<string, string>;
}) {
  const session = await auth();

  // Check if user is authenticated and has admin role
  if (!session || !session.user.roles.some(role => role.name === "admin")) {
    redirect("/");
  }

  // Fetch verification queue
  const verificationQueue = await fetchVerificationQueue(searchParams).catch(() => null);

  // Demo data fallback
  const demoQueue: PaginatedData<VerificationRequest> = {
    data: [
      {
        id: "vr1",
        vendor: {
          id: "v1",
          name: "FastDelivery Co.",
          email: "<EMAIL>",
          phone: "+254700123456",
        },
        status: "pending",
        documents: [
          {
            id: "d1",
            type: "business_license",
            name: "Business License.pdf",
            url: "/documents/business-license-1.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          },
          {
            id: "d2",
            type: "insurance_certificate",
            name: "Insurance Certificate.pdf",
            url: "/documents/insurance-cert-1.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          },
          {
            id: "d3",
            type: "vehicle_registration",
            name: "Vehicle Registration.pdf",
            url: "/documents/vehicle-reg-1.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          },
          {
            id: "d4",
            type: "driver_license",
            name: "Driver License.pdf",
            url: "/documents/driver-license-1.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
          },
        ],
        submitted_at: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(),
        priority: "high",
        estimated_review_time: 2,
      },
      {
        id: "vr2",
        vendor: {
          id: "v2",
          name: "QuickCourier",
          email: "<EMAIL>",
          phone: "+254700234567",
        },
        status: "under_review",
        documents: [
          {
            id: "d5",
            type: "business_license",
            name: "Business License.pdf",
            url: "/documents/business-license-2.pdf",
            status: "approved",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
          },
          {
            id: "d6",
            type: "insurance_certificate",
            name: "Insurance Certificate.pdf",
            url: "/documents/insurance-cert-2.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
          },
          {
            id: "d7",
            type: "vehicle_registration",
            name: "Vehicle Registration.pdf",
            url: "/documents/vehicle-reg-2.pdf",
            status: "rejected",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
          },
        ],
        submitted_at: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(),
        priority: "medium",
        estimated_review_time: 4,
      },
      {
        id: "vr3",
        vendor: {
          id: "v3",
          name: "SpeedyService",
          email: "<EMAIL>",
          phone: "+254700345678",
        },
        status: "pending",
        documents: [
          {
            id: "d8",
            type: "business_license",
            name: "Business License.pdf",
            url: "/documents/business-license-3.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
          },
          {
            id: "d9",
            type: "insurance_certificate",
            name: "Insurance Certificate.pdf",
            url: "/documents/insurance-cert-3.pdf",
            status: "pending",
            uploaded_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
          },
        ],
        submitted_at: new Date(Date.now() - 1000 * 60 * 60 * 24 * 5).toISOString(),
        priority: "low",
        estimated_review_time: 8,
      },
    ],
    meta: {
      total: 3,
      perPage: 10,
      currentPage: 1,
      lastPage: 1,
      firstPage: 1,
      firstPageUrl: "/admin/delivery-management/verification?page=1",
      lastPageUrl: "/admin/delivery-management/verification?page=1",
      nextPageUrl: null,
      previousPageUrl: null,
    },
    sum: {},
  };

  const reviewVerificationRequest = async (data: FormData) => {
    "use server";

    const requestId = data.get("requestId") as string;
    const action = data.get("action") as string;
    const notes = data.get("notes") as string;

    try {
      await api.post(`admin/delivery-management/verification-queue/${requestId}/review`, {
        action,
        notes,
      });
      revalidatePath("/admin/delivery-management/verification");
    } catch (error) {
      console.error("Failed to review verification request:", error);
      throw error;
    }
  };

  const updateDocumentStatus = async (data: FormData) => {
    "use server";

    const documentId = data.get("documentId") as string;
    const status = data.get("status") as string;

    try {
      await api.patch(`admin/delivery-management/documents/${documentId}`, {
        status,
      });
      revalidatePath("/admin/delivery-management/verification");
    } catch (error) {
      console.error("Failed to update document status:", error);
      throw error;
    }
  };

  return (
    <div className="page-content p-5">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Vendor Verification Queue</h1>
        <p className="text-gray-600 mt-1">
          Review and approve vendor verification requests and documents
        </p>
      </div>

      <AdminVerificationQueue
        data={verificationQueue?.data || demoQueue.data}
        meta={verificationQueue?.meta || demoQueue.meta}
        reviewVerificationRequest={reviewVerificationRequest}
        updateDocumentStatus={updateDocumentStatus}
      />
    </div>
  );
}
