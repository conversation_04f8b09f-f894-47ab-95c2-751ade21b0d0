export interface Branch {
  id: string;
  name: string;
  details: string;
  email: string;
  phone: string;
  image: AttachmentContract | null;
  cover: AttachmentContract | null;
  vendorId: string;
  location: Record<string, any> | null;
  geom: string | null;
  hours: { schedule: ScheduleHours[] } | null;
  createdAt: string;
  updatedAt: string;
  vendor: Vendor;
  sections: Section[];
  staff: User[];
  sectionCount: number;
  staffCount: number;
  orderCount: number;
}

export interface Vendor {
  id: string;
  name: string;
  slug: string;
  email: string;
  phone: string;
  reg: string;
  kra: string;
  active: boolean;
  featured: boolean;
  permit: string;
  details: string | null;
  location: Record<string, any> | null;
  geom: string | null;
  logo: AttachmentContract | null;
  cover: AttachmentContract | null;
  hours: { schedule: ScheduleHours[] } | null;
  createdAt: string;
  updatedAt: string;
  logoUrl: string;
  vendorCategoryId: string;
  category: VendorCategory;
  categories?: VendorCategory[];
  vendorType?: VendorType;
  vendorCategory?: VendorCategory;
  employees: (User & {
    role: string;
  })[];
  userId: string;
  products: Product[];
  tasks?: Task[];
  services?: Service[];
  specialities?: Speciality[];
  serviceId?: string;
}

export interface PaginatedData<M> {
  meta: PaginationMeta;
  data: M[];
  sum: Record<string, number>;
}

interface PaginationMeta {
  total: number;
  perPage: number;
  currentPage: number;
  lastPage: number;
  firstPage: number;
  firstPageUrl: string;
  lastPageUrl: string;
  nextPageUrl: string | null;
  previousPageUrl: string | null;
}

interface AttachmentContract {
  name: string;
  url: string;
  size: number;
  extname: string;
  mimeType: string;
  isLocal: boolean;
  isPersisted: boolean;
  isDeleted: boolean;
}

interface ScheduleHours {
  day: string;
  from: string;
  to: string;
}

interface Section {
  id: string;
  name: string;
  details: string;
  image: AttachmentContract | null;
  branchId: string;
  createdAt: string;
  updatedAt: string;
  branch: Branch;
}

export interface User {
  id: string;
  title: string;
  user_id?: string;
  firstName: string;
  lastName: string;
  gender: string | null;
  dob: string | null;
  email: string;
  phone: string;
  idpass: string;
  rememberMeToken: string | null;
  details: string | null;
  location: Record<string, any> | null;
  geom: string | null;
  avatar?: AttachmentContract | null;
  createdAt: string;
  updatedAt: string;
  name: string;
  status: string;
  avatarUrl: string;
  initials: string;
  devices: Device[];
  roles: Role[];
  permissions: Permission[];
  notifications: DatabaseNotification[];
  first_name?: string;
  last_name?: string;
  identifier: string;
  online: boolean;
  vendorId: string;
  meta?: { username: null; online: false };
}

interface Device {
  name: string;
  id: string;
  details: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string;
  $forceDelete: boolean;
  trashed: boolean;
  token: string;
  userId: string;
  meta: Record<string, any>;
}

export interface Role {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface Permission {
  id: string;
  name: string;
}

interface DatabaseNotification {
  id: string;
  data: Record<string, string | number | any>;
  created_at: string;
  updated_at: string;
  read_at: string;
}

export interface VendorCategory {
  id: string;
  name: string;
  details: string;
  image: AttachmentContract | null;
  vendorTypeId: string;
  createdAt: string;
  updatedAt: string;
  type: VendorType;
}

export interface VendorType {
  id: string;
  name: string;
  details: string;
  slug: string;
  serviceId: string;
  image: AttachmentContract | null;
  createdAt: string;
  updatedAt: string;
  service: Service;
  vendorCategories: VendorCategory[];
}

export interface Service {
  id: string;
  name: string;
  slug: string;
  details: string;
  active: boolean;
  featured: boolean;
  order: number;
  image: AttachmentContract | null;
  taskId: string;
  task: Task;
  createdAt: string;
  updatedAt: string;
}

export interface Task {
  id: string;
  name: string;
  slug: string;
  details: string;
  active: boolean;
  image: AttachmentContract | null;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
  services: Service[];
}

export interface PackagingOption {
  id: string;
  name: string;
  description?: string;
  price: string;
  vendorId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Product {
  id: string;
  name: string;
  ref: string;
  details: string;
  price: number;
  image: AttachmentContract | null;
  vendorId: string;
  vendor: Vendor;
  branchId: string;
  branch: Branch;
  serviceId: string;
  service: Service;
  status: "Draft" | "Published" | "Archived";
  featured: boolean;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  packagingOptions?: PackagingOption[];
  meta: {
    totalForms: number;
  } | null;
}

export interface Speciality {
  id: string;
  name: string;
  details?: string;
  image?: {
    url: string;
  };
}

export interface MessageTemplate {
  id: string;
  name: string;
  content: string;
  image: AttachmentContract | null;
  actions: Array<{
    id: string;
    type_id: number;
    config: Record<string, any>;
    status: 'pending' | 'active' | 'completed' | 'cancelled';
  }>;
  createdAt: string;
  updatedAt: string;
}

export interface ServiceArea {
  id: string;
  vendor_id: string;
  branch_id: string;
  name: string;
  description?: string;
  type: 'circle' | 'polygon' | 'administrative';
  radius?: number;
  geom?: string;
  administrative_areas?: Array<{
    country?: string;
    administrative_area_level_1?: string;
    administrative_area_level_2?: string;
    administrative_area_level_3?: string;
    administrative_area_level_4?: string;
    administrative_area_level_5?: string;
  }>;
  priority: number;
  active: boolean;
  meta?: {
    estimated_delivery_time?: number;
    delivery_fee?: number;
    special_instructions?: string;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
  branch?: Branch;
  vendor?: Vendor;
}