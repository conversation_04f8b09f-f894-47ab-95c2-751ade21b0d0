export interface TempOrderItem {
  id: string;
  ref: string | null;
  name: string;
  details: string | null;
  price: number;
  discounted: number | null;
  stock: number;
  active: boolean;
  featured: boolean;
  type: string;
  condition: string;
  status: string;
  availability: string;
  shipping: string;
  unit: string;
  mode: string;
  payment: string;
  visibility: string;
  productCategoryId: string;
  userId: string;
  vendorId: string;
  branchId: string;
  serviceId: string;
  image: any;
  meta: {
    sku: string | null;
  };
  extra: any;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
  quantity: number;
}

export interface TempOrder {
  id: string;
  vendorId: string;
  branchId: string;
  sectionId: string | null;
  lotId: string | null;
  userId: string;
  staffId: string | null;
  action: string;
  type: "Instant" | "Preorder";
  delivery: "Takeaway" | "Dinein" | "Delivery" | "Selfpick";
  status: "Pending" | "Placed" | "Processing" | "Ready" | "Delivering" | "Delivered" | "Completed" | "Cancelled" | "Rejected";
  meta: {
    charges: {
      AppInApp: number;
      Tip: number;
    };
    total: number;
    selectedSection?: {
      id: string;
      title: string;
    };
    selectedTable?: {
      id: string;
      title: string;
    };
  };
  ref: string | null;
  acceptedAt: string;
  createdAt: string;
  updatedAt: string;
  items: TempOrderItem[];
  customer: {
    id: string;
    idpass: string | null;
    title: string | null;
    firstName: string;
    lastName: string;
    gender: string | null;
    dob: string | null;
    otp: string;
    email: string;
    phone: string;
    rememberMeToken: string | null;
    details: string | null;
    location: string | null;
    geom: string | null;
    avatar: any;
    meta: {
      username: string | null;
      online: boolean;
    };
    status: string;
    emailVerifiedAt: string | null;
    phoneVerifiedAt: string | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    name: string;
    avatarUrl: string;
    initials: string;
  };
  vendor: {
    id: string;
    userId: string;
    serviceId: string | null;
    name: string;
    slug: string;
    details: string;
    email: string;
    phone: string;
    reg: string;
    permit: string;
    kra: string;
    logo: {
      url: string;
      name: string;
      extname: string;
      size: number;
      mimeType: string;
    };
    cover: {
      url: string;
      name: string;
      extname: string;
      size: number;
      mimeType: string;
    };
    active: boolean;
    featured: boolean;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  branch: {
    id: string;
    vendorId: string;
    name: string;
    details: string;
    email: string;
    phone: string;
    image: any;
    location: {
      name: string;
      address: string;
      regions: {
        country: string;
      };
      coordinates: {
        lat: number;
        lng: number;
      };
      place_id: string;
    };
    geom: string;
    hours: {
      schedule: Array<{
        day: string;
        from: string;
        to: string;
      }>;
    };
    createdAt: string;
    updatedAt: string;
  };
  section: any;
  total: number;
} 