export interface FormTemplate {
  id: string;
  name: string;
  details: string;
  sections: {
    [key: string]: {
      id: string;
      name: string;
      details: string;
      repeatable: boolean;
      repeats?: number;
      hasCost?: boolean;
      cost?: number;
      fields: Array<{
        id: string;
        type: string;
        name: string;
        label: string;
        defaultValue?: string;
        placeholder?: string;
        required: boolean;
        repeatable?: boolean;
        repeats?: number;
        min?: number;
        max?: number;
        options?: Array<{
          value: string;
          label: string;
        }>;
      }>;
    };
  };
  createdAt: string;
  updatedAt: string;
} 