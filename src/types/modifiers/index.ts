/**
 * Modifier types available in the system
 */
export type ModifierType = 'preparation' | 'condiment' | 'extra';

/**
 * Interface for a modifier option
 */
export interface Modifier {
  id: string;
  vendorId: string;
  name: string;
  type: ModifierType;
  description: string | null;
  defaultPriceAdjustment: string; // Decimal number as string
  active: boolean;
  createdAt: string; // ISO datetime
  updatedAt: string; // ISO datetime
}

/**
 * Interface for creating a new modifier
 */
export interface CreateModifierRequest {
  name: string;
  type: ModifierType;
  description?: string;
  default_price_adjustment?: number;
  active?: boolean;
}

/**
 * Interface for updating an existing modifier
 */
export interface UpdateModifierRequest {
  name?: string;
  type?: ModifierType;
  description?: string;
  default_price_adjustment?: number;
  active?: boolean;
}

/**
 * Interface for product-specific modifier settings
 */
export interface ProductModifierSettings {
  price_adjustment_override?: number;
  is_default?: boolean;
  sort_order?: number;
}

/**
 * Interface for product with modifiers
 */
export interface ProductWithModifiers {
  modifiers: {
    preparation: ProductModifier[];
    condiment: ProductModifier[];
    extra: ProductModifier[];
  };
}

/**
 * Interface for a modifier attached to a product
 */
export interface ProductModifier {
  id: string;
  name: string;
  type: ModifierType;
  description: string | null;
  price_adjustment: number; // Product-specific price override or default
  is_default: boolean;
  sort_order: number;
} 