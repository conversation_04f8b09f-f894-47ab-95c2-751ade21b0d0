'use server';

import { auth } from '@/auth';
import { api } from '@/lib/api';
import { revalidatePath } from 'next/cache';
import { Modifier } from '@/types/modifiers';

export interface ProductModifierRelationship {
  id: string;
  vendorId: string;
  name: string;
  type: string;
  description: string;
  defaultPriceAdjustment: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  maxQuantity: number | null;
  priceAdjustment: string;
  isDefault: boolean;
  sortOrder: number;
}

export interface ProductModifiersResponse {
  data: {
    condiment?: ProductModifierRelationship[];
    preparation?: ProductModifierRelationship[];
    extra?: ProductModifierRelationship[];
  };
}

// Get product modifiers
export async function getProductModifiers(productId: string): Promise<ProductModifierRelationship[]> {
  try {
    const session = await auth();
    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    const response = await api.get<ProductModifiersResponse>(`v1/products/${productId}/modifiers`);

    // Flatten the grouped response into a single array
    const allModifiers: ProductModifierRelationship[] = [];
    if (response?.data) {
      Object.values(response.data).forEach(modifierGroup => {
        if (Array.isArray(modifierGroup)) {
          allModifiers.push(...modifierGroup);
        }
      });
    }

    return allModifiers;
  } catch (error) {
    console.error('Error fetching product modifiers:', error);
    return [];
  }
}

// Attach modifier to product
export async function attachModifierToProduct(formData: FormData): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const productId = formData.get('productId') as string;
    const modifierId = formData.get('modifierId') as string;

    await api.post(`v1/products/${productId}/modifiers`, { modifierId });

    revalidatePath(`/products/${productId}/edit`);
    revalidatePath(`/products/${productId}`);

    return { success: true };
  } catch (error) {
    console.error('Error attaching modifier to product:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to attach modifier'
    };
  }
}

// Update product modifier relationship
export async function updateProductModifier(formData: FormData): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const productId = formData.get('productId') as string;
    const modifierId = formData.get('modifierId') as string;
    const priceAdjustmentOverride = formData.get('priceAdjustmentOverride')
      ? parseFloat(formData.get('priceAdjustmentOverride') as string)
      : undefined;
    const isDefault = formData.get('isDefault') === 'true';
    const sortOrder = formData.get('sortOrder')
      ? parseInt(formData.get('sortOrder') as string)
      : 0;

    const data = {
      priceAdjustmentOverride,
      isDefault,
      sortOrder
    };

    await api.put(`v1/products/${productId}/modifiers/${modifierId}`, data);

    revalidatePath(`/products/${productId}/edit`);
    revalidatePath(`/products/${productId}`);

    return { success: true };
  } catch (error) {
    console.error('Error updating product modifier:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to update modifier relationship'
    };
  }
}

// Detach modifier from product
export async function detachModifierFromProduct(formData: FormData): Promise<{ success: boolean; error?: string }> {
  try {
    const session = await auth();
    if (!session?.user) {
      return { success: false, error: 'Unauthorized' };
    }

    const productId = formData.get('productId') as string;
    const modifierId = formData.get('modifierId') as string;

    await api.delete(`v1/products/${productId}/modifiers/${modifierId}`);

    revalidatePath(`/products/${productId}/edit`);
    revalidatePath(`/products/${productId}`);

    return { success: true };
  } catch (error) {
    console.error('Error detaching modifier from product:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to detach modifier'
    };
  }
}

// Get available modifiers for a vendor
export async function getAvailableModifiers(vendorId: string): Promise<Modifier[]> {
  try {
    const session = await auth();
    if (!session?.user) {
      throw new Error('Unauthorized');
    }

    const response = await api.get<Modifier[]>(`vendors/${vendorId}/modifier-options`);
    return response || [];
  } catch (error) {
    console.error('Error fetching available modifiers:', error);
    return [];
  }
}
