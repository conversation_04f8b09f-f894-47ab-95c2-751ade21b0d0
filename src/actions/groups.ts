"use server";

import { api } from "@/lib/api";
import { revalidatePath } from "next/cache";

export async function deleteGroup(formData: FormData) {
  const id = formData.get("id") as string;
  await api.delete(`groups/${id}`);
  revalidatePath("/messages/groups");
}

export async function updateGroup(data: {
  id: string;
  name: string;
  details: string;
}) {
  await api.put(`groups/${data.id}`, data);
  revalidatePath("/messages/groups");
}

export async function createGroup(data: {
  name: string;
  details: string;
}) {
  await api.post("groups", data);
  revalidatePath("/messages/groups");
} 