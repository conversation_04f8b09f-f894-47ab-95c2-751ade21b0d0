'use server';

import { api } from '@/lib/api';
import { auth } from '@/auth';

export interface VendorUploadHistoryResponse {
  uploads: Array<{
    id: string;
    fileName: string;
    uploadedBy: {
      id: string;
      name: string;
    };
    status: 'queued' | 'processing' | 'completed' | 'failed';
    progress: number;
    totalRows: number;
    processedRows: number;
    failedRows: number;
    errors?: Array<{ row: number; message: string }>;
    createdAt: string;
    updatedAt: string;
  }>;
  total: number;
  page: number;
  perPage: number;
}

export async function getVendorUploadHistory(
  page: number = 1,
  perPage: number = 10
): Promise<{ history?: VendorUploadHistoryResponse; error?: string }> {
  const session = await auth();
  if (!session) {
    return { error: 'Unauthorized: User not authenticated.' };
  }

  try {
    const response = await api.get<VendorUploadHistoryResponse>(
      '/admin/vendors/bulk-upload/history',
      { page, perPage }
    );
    return { history: response };
  } catch (err: any) {
    console.error('Error fetching vendor upload history:', err);
    return { error: err.message || 'Failed to fetch upload history' };
  }
}

export async function downloadUploadResults(
  jobId: string
): Promise<{ url?: string; error?: string }> {
  const session = await auth();
  if (!session) {
    return { error: 'Unauthorized: User not authenticated.' };
  }

  try {
    const response = await api.get<{ downloadUrl: string }>(
      `/admin/vendors/bulk-upload/${jobId}/download`
    );
    return { url: response?.downloadUrl };
  } catch (err: any) {
    console.error('Error getting download URL:', err);
    return { error: err.message || 'Failed to get download URL' };
  }
}

export async function cancelUpload(
  jobId: string
): Promise<{ success?: boolean; error?: string }> {
  const session = await auth();
  if (!session) {
    return { error: 'Unauthorized: User not authenticated.' };
  }

  try {
    await api.post(`/admin/vendors/bulk-upload/${jobId}/cancel`);
    return { success: true };
  } catch (err: any) {
    console.error('Error cancelling upload:', err);
    return { error: err.message || 'Failed to cancel upload' };
  }
} 