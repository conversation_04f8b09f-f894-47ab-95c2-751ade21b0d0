'use server';

import { api, JobStatusResponse } from "@/lib/api";
import { auth } from "@/auth";

export async function getJobStatusAction(
  jobId: string
): Promise<{ status?: JobStatusResponse; error?: string }> {
  // Authentication check
  const session = await auth();
  if (!session) {
    // Depending on requirements, you might allow unauthenticated status checks
    // or return an error here.
    return { error: "Unauthorized: User not authenticated." };
  }

  // Prevent infinite loops if jobId is somehow invalid/empty
  if (!jobId) {
    return { error: "Invalid Job ID provided." };
  }

  try {
    const response = await api.get<JobStatusResponse>(`/admin/bulk-upload-jobs/${jobId}`);
    return { status: response };
  } catch (err: any) {
    console.error("Error fetching job status:", err);
    return { error: err.message || "Failed to fetch job status" };
  }
} 