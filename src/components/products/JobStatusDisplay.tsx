"use client";

import { JobStatusResponse } from "@/lib/api";
import { Progress, Card, CardBody, CardHeader, Divider, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Chip } from "@nextui-org/react";
import { Fi<PERSON>lock, FiLoader, FiCheckCircle, FiXCircle, FiAlertTriangle } from "react-icons/fi";
import { format } from 'date-fns';

interface JobStatusDisplayProps {
  jobStatus: JobStatusResponse | null;
  jobId: string;
}

const statusMap = {
  queued: { color: "default", icon: <FiClock />, label: "Queued" },
  processing: { color: "primary", icon: <FiLoader className="animate-spin" />, label: "Processing" },
  completed: { color: "success", icon: <FiCheckCircle />, label: "Completed" },
  failed: { color: "danger", icon: <FiXCircle />, label: "Failed" },
} as const;

export default function JobStatusDisplay({ jobStatus, jobId }: JobStatusDisplayProps) {
  if (!jobStatus) {
    return (
      <Card className="mt-6">
        <CardBody className="flex flex-row items-center gap-2 text-gray-500">
          <FiLoader className="animate-spin" />
          <span>Loading job status for ID: {jobId}...</span>
        </CardBody>
      </Card>
    );
  }

  const currentStatus = statusMap[jobStatus.status];
  const progressPercent = jobStatus.progress ?? (jobStatus.totalRows && jobStatus.processedRows ? Math.round((jobStatus.processedRows / jobStatus.totalRows) * 100) : 0);

  return (
    <Card className="mt-6">
      <CardHeader className="flex justify-between items-center">
        <h4 className="text-lg font-semibold">Job Status (ID: {jobId})</h4>
        <Chip
          color={currentStatus.color}
          variant="flat"
          startContent={currentStatus.icon}
          size="sm"
        >
          {currentStatus.label}
        </Chip>
      </CardHeader>
      <Divider />
      <CardBody className="space-y-4">
        {jobStatus.status === 'processing' && (
          <Progress
            aria-label="Processing..."
            value={progressPercent}
            color="primary"
            showValueLabel={true}
            className="w-full"
          />
        )}

        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-sm">
          <div className="text-center sm:text-left">
            <p className="text-gray-500">Total Rows</p>
            <p className="font-medium">{jobStatus.totalRows ?? 'N/A'}</p>
          </div>
          <div className="text-center sm:text-left">
            <p className="text-gray-500">Processed</p>
            <p className="font-medium">{jobStatus.processedRows ?? 'N/A'}</p>
          </div>
          <div className="text-center sm:text-left">
            <p className="text-gray-500">Failed</p>
            <p className={`font-medium ${jobStatus.failedRows && jobStatus.failedRows > 0 ? 'text-danger' : ''}`}>
              {jobStatus.failedRows ?? 'N/A'}
            </p>
          </div>
          <div className="text-center sm:text-left">
            <p className="text-gray-500">Last Updated</p>
            <p className="font-medium">{format(new Date(jobStatus.updatedAt), 'PPpp')}</p>
          </div>
        </div>

        {jobStatus.status === 'failed' && jobStatus.errors && jobStatus.errors.length > 0 && (
          <div className="mt-4">
            <h5 className="text-md font-semibold text-danger flex items-center gap-2 mb-2">
              <FiAlertTriangle />
              Error Details
            </h5>
            <Table aria-label="Job Errors" removeWrapper className="max-h-60 overflow-y-auto">
              <TableHeader>
                <TableColumn>Row</TableColumn>
                <TableColumn>Message</TableColumn>
              </TableHeader>
              <TableBody items={jobStatus.errors}>
                {(item) => (
                  <TableRow key={`${item.row}-${item.message.slice(0, 10)}`}>
                    <TableCell>{item.row}</TableCell>
                    <TableCell>{item.message}</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardBody>
    </Card>
  );
} 