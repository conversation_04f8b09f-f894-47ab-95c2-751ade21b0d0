"use client";

import { useState, useCallback, useEffect, useRef } from "react";
import { useDropzone } from "react-dropzone";
import { Button } from "@nextui-org/react";
import { FiUploadCloud, FiXCircle, FiCheckCircle } from "react-icons/fi";
import BranchSelect from "./BranchSelect";
import JobStatusDisplay from "./JobStatusDisplay";
import { JobStatusResponse } from "@/lib/api";
import toast from "react-hot-toast";
import { uploadBulkProductsAction } from "@/actions/products";
import { getJobStatusAction } from "@/actions/jobs";

interface BulkUploadFormProps {
  vendorId: string;
}

const POLLING_INTERVAL = 3000; // Poll job status every 3 seconds
const MAX_POLLING_ERRORS = 5; // Stop polling after 5 consecutive status fetch errors
const MAX_FILE_SIZE_MB = 10; // Maximum allowed file size in Megabytes
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

export default function BulkUploadForm({ vendorId }: BulkUploadFormProps) {
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [branchId, setBranchId] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [jobId, setJobId] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<JobStatusResponse | null>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const pollingErrorCount = useRef(0);

  const clearPolling = () => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
      pollingErrorCount.current = 0;
    }
  };

  useEffect(() => {
    if (!jobId) {
      setJobStatus(null);
      clearPolling();
      return;
    }
    const fetchStatus = async () => {
      if (!jobId) return;
      try {
        const result = await getJobStatusAction(jobId);

        if (result.status) {
          pollingErrorCount.current = 0;
          setJobStatus(result.status);
          if (result.status.status === 'completed' || result.status.status === 'failed') {
            clearPolling();
            if (result.status.status === 'completed') {
              toast.success("Bulk upload completed successfully!");
            } else if (result.status.status === 'failed') {
              toast.error(`Bulk upload failed. ${result.status.errors?.length || 0} rows had issues.`);
            }
          }
        } else if (result.error) {
          pollingErrorCount.current++;
          console.warn(`Failed to fetch status for job ${jobId}: ${result.error}`);
        } else {
          pollingErrorCount.current++;
          console.warn(`getJobStatusAction returned unexpected response for job ${jobId}`);
        }
      } catch (error: any) {
        pollingErrorCount.current++;
        console.error("Error calling getJobStatusAction:", error);
      }
      if (pollingErrorCount.current >= MAX_POLLING_ERRORS) {
        console.error(`Stopping polling for job ${jobId} after ${MAX_POLLING_ERRORS} consecutive errors.`);
        toast.error("Could not retrieve job status updates. Please check back later or contact support.", { duration: 5000 });
        clearPolling();
      }
    };
    fetchStatus();
    clearPolling();
    pollingRef.current = setInterval(fetchStatus, POLLING_INTERVAL);
    return () => clearPolling();
  }, [jobId]);

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setUploadError(null);
    setJobId(null);

    if (rejectedFiles.length > 0) {
        const rejection = rejectedFiles[0];
        if (rejection.errors.some((e: any) => e.code === 'file-too-large')) {
            setUploadError(`File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`);
        } else if (rejection.errors.some((e: any) => e.code === 'file-invalid-type')) {
            setUploadError("Invalid file type. Please upload a CSV file.");
        } else {
            setUploadError(rejection.errors[0]?.message || "File rejected.");
        }
        setCsvFile(null);
        return;
    }

    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      if (file.type === "text/csv" || file.name.endsWith(".csv")) {
        setCsvFile(file);
      } else {
        setUploadError("Invalid file type. Please upload a CSV file.");
        setCsvFile(null);
      }
    } else {
      setCsvFile(null);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { "text/csv": [".csv"] },
    maxSize: MAX_FILE_SIZE_BYTES,
    multiple: false,
  });

  const handleUpload = async () => {
    if (!csvFile) {
      toast.error("Please select a CSV file to upload.");
      return;
    }

    setIsUploading(true);
    setUploadError(null);
    setJobId(null);
    setJobStatus(null);
    pollingErrorCount.current = 0;

    const formData = new FormData();
    formData.append("product_csv", csvFile);
    if (branchId) {
      formData.append("branchId", branchId);
    }

    try {
      const result = await uploadBulkProductsAction(vendorId, formData);

      if (result.jobId) {
        setJobId(result.jobId);
        toast.success("File upload initiated successfully! Processing started.");
      } else if (result.error) {
        setUploadError(result.error);
        toast.error(`Upload failed: ${result.error}`);
      } else {
        throw new Error("Upload action did not return a job ID or an error.");
      }
    } catch (err: any) {
      console.error("Error calling upload server action:", err);
      const errorMessage = err.message || "Upload failed. Please try again.";
      setUploadError(errorMessage);
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveFile = () => {
    setCsvFile(null);
    setUploadError(null);
    setIsUploading(false);
    setJobId(null);
  };

  return (
    <div className="space-y-6 p-4 border rounded-lg shadow-sm bg-white">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${isDragActive ? "border-primary bg-primary-50" : "border-gray-300 hover:border-gray-400"} ${uploadError ? 'border-danger' : ''}`}
      >
        <input {...getInputProps()} />
        <FiUploadCloud className="mx-auto h-12 w-12 text-gray-400" />
        {isDragActive ? (
          <p className="mt-2 text-sm text-primary">Drop the CSV file here ...</p>
        ) : (
          <p className="mt-2 text-sm text-gray-600">
            Drag &apos;n&apos; drop a CSV file here, or click to select file
          </p>
        )}
        <p className="text-xs text-gray-500 mt-1">CSV files only, up to {MAX_FILE_SIZE_MB}MB</p>
      </div>

      {csvFile && (
        <div className="flex items-center justify-between p-3 border rounded-md bg-gray-50">
          <span className="text-sm font-medium text-gray-700 truncate">
            {csvFile.name}
          </span>
          <Button size="sm" isIconOnly variant="light" color="danger" onPress={handleRemoveFile} isDisabled={isUploading || (!!jobId && jobStatus?.status !== 'completed' && jobStatus?.status !== 'failed')}>
            <FiXCircle />
          </Button>
        </div>
      )}

      {uploadError && (
        <div className="flex items-center p-3 border border-danger-200 rounded-md bg-danger-50 text-danger">
          <FiXCircle className="mr-2" />
          <span className="text-sm">{uploadError}</span>
        </div>
      )}

      {jobId && !jobStatus && !isUploading && (
        <div className="flex items-center p-3 border border-success-200 rounded-md bg-success-50 text-success">
          <FiCheckCircle className="mr-2" />
          <span className="text-sm">Upload initiated successfully! Fetching job status...</span>
        </div>
      )}

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <BranchSelect vendorId={vendorId} value={branchId} onChange={setBranchId} />
        <Button
          color="primary"
          onClick={handleUpload}
          isDisabled={!csvFile || isUploading || (!!jobId && jobStatus?.status !== 'completed' && jobStatus?.status !== 'failed')}
          isLoading={isUploading}
        >
          {isUploading ? "Initiating Upload..." : (jobId && (jobStatus?.status === 'processing' || jobStatus?.status === 'queued') ? "Processing..." : "Upload Products")}
        </Button>
      </div>

      {jobId &&
        <JobStatusDisplay jobId={jobId} jobStatus={jobStatus} />
      }
    </div>
  );
} 