"use client";

import { useEffect, useState } from "react";
import { Select, SelectItem } from "@nextui-org/react";
import { api } from "@/lib/api";
import { Branch, PaginatedData } from "@/types";

interface BranchSelectProps {
  vendorId: string;
  value: string;
  onChange: (value: string) => void;
}

export default function BranchSelect({ vendorId, value, onChange }: BranchSelectProps) {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBranches = async () => {
      if (!vendorId) {
        console.log('BranchSelect: No vendorId provided');
        setError("No vendor ID provided");
        setIsLoading(false);
        return;
      }

      console.log('BranchSelect: Starting fetch for vendorId:', vendorId);
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('BranchSelect: Making API call to vendors/${vendorId}/branches');
        const response = await fetch(`/api/vendors/${vendorId}/branches`);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('BranchSelect: API Response:', data);
        
        if (data.data) {
          console.log('BranchSelect: Setting branches data:', data.data);
          setBranches(data.data);
        } else {
          console.log('BranchSelect: No data in response');
          setBranches([]);
        }
      } catch (err: any) {
        console.error('BranchSelect: Error fetching branches:', err);
        console.error('BranchSelect: Error details:', {
          message: err.message,
          status: err.status
        });
        setError(err.message || "Failed to fetch branches");
      } finally {
        setIsLoading(false);
      }
    };

    fetchBranches();
  }, [vendorId]);

  const items = [
    { key: "", label: "None" },
    ...branches.map((branch) => ({
      key: branch.id,
      label: branch.name,
    })),
  ];

  console.log('BranchSelect: Current state:', {
    isLoading,
    error,
    branchesCount: branches.length,
    items,
    value
  });

  return (
    <Select
      label="Branch (Optional)"
      placeholder="Select a branch"
      className="max-w-xs"
      selectedKeys={value ? new Set([value]) : new Set()}
      onSelectionChange={(keys) => onChange(Array.from(keys)[0]?.toString() || "")}
      isLoading={isLoading}
      isDisabled={isLoading || !!error}
      items={items}
      errorMessage={error}
    >
      {(item) => (
        <SelectItem key={item.key}>
          {item.label}
        </SelectItem>
      )}
    </Select>
  );
} 