"use client";

import { useState, useEffect } from "react";

interface DialogProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  type?: "error" | "warning" | "info" | "success" | "confirm";
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export default function Dialog({
  isOpen,
  onClose,
  title,
  children,
  type = "info",
  confirmText = "Got it",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
}: DialogProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      onClose();
    }, 150); // Allow animation to complete
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    }
    handleClose();
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
    handleClose();
  };

  if (!isOpen) return null;

  // Get styling based on type
  const getTypeStyles = () => {
    switch (type) {
      case "error":
        return {
          iconColor: "text-red-400",
          buttonColor: "bg-red-600 hover:bg-red-700 focus:ring-red-500",
          icon: (
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
            </svg>
          ),
        };
      case "warning":
        return {
          iconColor: "text-yellow-400",
          buttonColor: "bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",
          icon: (
            <svg className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          ),
        };
      case "success":
        return {
          iconColor: "text-green-400",
          buttonColor: "bg-green-600 hover:bg-green-700 focus:ring-green-500",
          icon: (
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
        };
      case "confirm":
        return {
          iconColor: "text-blue-400",
          buttonColor: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",
          icon: (
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9 5.25h.008v.008H12v-.008z" />
            </svg>
          ),
        };
      default:
        return {
          iconColor: "text-blue-400",
          buttonColor: "bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",
          icon: (
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z" />
            </svg>
          ),
        };
    }
  };

  const styles = getTypeStyles();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div 
        className={`bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-150 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
      >
        <div className="p-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <div className={styles.iconColor}>
                {styles.icon}
              </div>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-lg font-medium text-gray-900">
                {title}
              </h3>
              <div className="mt-2 text-sm text-gray-600">
                {children}
              </div>
            </div>
          </div>
          
          <div className="mt-6 flex justify-end space-x-3">
            {onCancel && (
              <button
                type="button"
                onClick={handleCancel}
                className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                {cancelText}
              </button>
            )}
            <button
              type="button"
              onClick={handleConfirm}
              className={`text-white px-4 py-2 rounded-lg focus:outline-none focus:ring-2 ${styles.buttonColor}`}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper components for specific use cases
export function ErrorDialog({
  isOpen,
  onClose,
  title = "Error",
  message,
  suggestions = [],
}: {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  message: string;
  suggestions?: string[];
}) {
  return (
    <Dialog isOpen={isOpen} onClose={onClose} title={title} type="error">
      <p className="mb-3">{message}</p>
      {suggestions.length > 0 && (
        <div>
          <p className="font-medium mb-2">How to fix this:</p>
          <ul className="list-disc list-inside space-y-1 ml-2">
            {suggestions.map((suggestion, index) => (
              <li key={index}>{suggestion}</li>
            ))}
          </ul>
        </div>
      )}
    </Dialog>
  );
}

export function ConfirmDialog({
  isOpen,
  onClose,
  onConfirm,
  onCancel,
  title = "Confirm Action",
  message,
  confirmText = "Confirm",
  cancelText = "Cancel",
  type = "confirm",
}: {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onCancel?: () => void;
  title?: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: "error" | "warning" | "info" | "success" | "confirm";
}) {
  return (
    <Dialog
      isOpen={isOpen}
      onClose={onClose}
      onConfirm={onConfirm}
      onCancel={onCancel}
      title={title}
      type={type}
      confirmText={confirmText}
      cancelText={cancelText}
    >
      <p>{message}</p>
    </Dialog>
  );
}
