"use client";

import { Card, CardBody } from "@nextui-org/react";
import { Icon } from "@/components/icon";

interface LoadingProps {
  message?: string;
  size?: "sm" | "md" | "lg";
  variant?: "minimal" | "card" | "fullscreen";
  className?: string;
}

export default function Loading({
  message = "Loading...",
  size = "md",
  variant = "card",
  className = ""
}: LoadingProps) {
  const sizeClasses = {
    sm: "h-6 w-6",
    md: "h-8 w-8",
    lg: "h-12 w-12"
  };

  const LoadingSpinner = () => (
    <div className="flex flex-col items-center justify-center space-y-4">
      <div className="relative">
        <div className={`animate-spin rounded-full border-2 border-primary/20 border-t-primary ${sizeClasses[size]}`}></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <Icon
            name="icon-[lucide--loader-2]"
            classNames={`animate-spin text-primary ${size === 'sm' ? 'w-3 h-3' : size === 'md' ? 'w-4 h-4' : 'w-6 h-6'}`}
          />
        </div>
      </div>
      <p className={`text-default-600 ${size === 'sm' ? 'text-sm' : size === 'lg' ? 'text-lg' : 'text-base'}`}>
        {message}
      </p>
    </div>
  );

  if (variant === "minimal") {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <LoadingSpinner />
      </div>
    );
  }

  if (variant === "fullscreen") {
    return (
      <div className={`fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 ${className}`}>
        <Card className="bg-background/95 shadow-lg">
          <CardBody className="p-8">
            <LoadingSpinner />
          </CardBody>
        </Card>
      </div>
    );
  }

  // Default card variant
  return (
    <div className={`p-6 ${className}`}>
      <Card className="bg-background shadow-sm">
        <CardBody className="p-8">
          <LoadingSpinner />
        </CardBody>
      </Card>
    </div>
  );
}