"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@nextui-org/react";

interface FormModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  size?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl" | "full";
  scrollBehavior?: "inside" | "outside";
  children: React.ReactNode;
  footer?: React.ReactNode;
  isSubmitting?: boolean;
  submitLabel?: string;
  cancelLabel?: string;
  onSubmit?: () => void;
  hideDefaultFooter?: boolean;
  className?: string;
}

export default function FormModal({
  isOpen,
  onClose,
  title,
  size = "2xl",
  scrollBehavior = "inside",
  children,
  footer,
  isSubmitting = false,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  onSubmit,
  hideDefaultFooter = false,
  classN<PERSON>,
}: <PERSON>ModalProps) {
  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose} 
      size={size} 
      scrollBehavior={scrollBehavior}
      classNames={{
        base: "max-h-[90vh]",
        body: "py-6",
        header: "border-b border-default-200",
        footer: "border-t border-default-200",
        ...className && { wrapper: className }
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h2 className="text-xl font-semibold text-default-900 dark:text-white">
            {title}
          </h2>
        </ModalHeader>
        
        <ModalBody className="space-y-6">
          {children}
        </ModalBody>
        
        {!hideDefaultFooter && (
          <ModalFooter>
            {footer || (
              <>
                <Button 
                  variant="light" 
                  onPress={onClose} 
                  isDisabled={isSubmitting}
                >
                  {cancelLabel}
                </Button>
                <Button
                  color="primary"
                  onPress={onSubmit}
                  isLoading={isSubmitting}
                >
                  {isSubmitting ? "Saving..." : submitLabel}
                </Button>
              </>
            )}
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  );
}

// Export a form wrapper version that handles form submission
interface FormModalWithFormProps extends Omit<FormModalProps, 'children' | 'onSubmit'> {
  children: React.ReactNode;
  onSubmit: (e: React.FormEvent) => void;
}

export function FormModalWithForm({
  children,
  onSubmit,
  ...modalProps
}: FormModalWithFormProps) {
  return (
    <FormModal {...modalProps} hideDefaultFooter>
      <form onSubmit={onSubmit}>
        {children}
        
        <ModalFooter>
          <Button 
            variant="light" 
            onPress={modalProps.onClose} 
            isDisabled={modalProps.isSubmitting}
          >
            {modalProps.cancelLabel || "Cancel"}
          </Button>
          <Button
            color="primary"
            type="submit"
            isLoading={modalProps.isSubmitting}
          >
            {modalProps.isSubmitting ? "Saving..." : (modalProps.submitLabel || "Save")}
          </Button>
        </ModalFooter>
      </form>
    </FormModal>
  );
}
