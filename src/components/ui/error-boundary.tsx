"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { useState } from "react";

interface ErrorBoundaryProps {
  error: Error & { digest?: string };
  reset?: () => void;
  title?: string;
  description?: string;
  variant?: "minimal" | "card" | "fullscreen";
  showDetails?: boolean;
  className?: string;
}

export default function ErrorBoundary({
  error,
  reset,
  title = "Something went wrong",
  description,
  variant = "card",
  showDetails = false,
  className = ""
}: ErrorBoundaryProps) {
  const [showErrorDetails, setShowErrorDetails] = useState(false);

  const getErrorType = (error: Error) => {
    if (error.message.includes('fetch')) return 'Network Error';
    if (error.message.includes('permission') || error.message.includes('unauthorized')) return 'Permission Error';
    if (error.message.includes('timeout')) return 'Timeout Error';
    return 'Application Error';
  };

  const getErrorColor = (errorType: string) => {
    switch (errorType) {
      case 'Network Error': return 'warning';
      case 'Permission Error': return 'danger';
      case 'Timeout Error': return 'secondary';
      default: return 'danger';
    }
  };

  const errorType = getErrorType(error);
  const errorColor = getErrorColor(errorType);

  const ErrorContent = () => (
    <div className="text-center space-y-6">
      {/* Error Icon */}
      <div className="flex justify-center">
        <div className="relative">
          <div className="w-20 h-20 rounded-full bg-danger-50 flex items-center justify-center">
            <Icon
              name="icon-[lucide--alert-triangle]"
              classNames="w-10 h-10 text-danger-500"
            />
          </div>
          <div className="absolute -top-1 -right-1">
            <div className="w-6 h-6 rounded-full bg-danger-500 flex items-center justify-center">
              <Icon
                name="icon-[lucide--x]"
                classNames="w-3 h-3 text-white"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Error Type Badge */}
      <div className="flex justify-center">
        <Chip
          color={errorColor as any}
          variant="flat"
          size="sm"
          startContent={
            <Icon
              name="icon-[lucide--info]"
              classNames="w-3 h-3"
            />
          }
        >
          {errorType}
        </Chip>
      </div>

      {/* Title and Description */}
      <div className="space-y-2">
        <h2 className="text-2xl font-semibold text-foreground">
          {title}
        </h2>
        <p className="text-default-600 max-w-md mx-auto">
          {description || error.message || "An unexpected error occurred. Please try again."}
        </p>
      </div>

      {/* Error ID */}
      {error.digest && (
        <div className="text-xs text-default-400 font-mono bg-default-100 rounded-lg px-3 py-2 inline-block">
          Error ID: {error.digest}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
        {reset && (
          <Button
            color="primary"
            variant="solid"
            onPress={() => reset()}
            startContent={
              <Icon
                name="icon-[lucide--refresh-cw]"
                classNames="w-4 h-4"
              />
            }
          >
            Try Again
          </Button>
        )}
        
        <Button
          color="default"
          variant="flat"
          onPress={() => window.location.reload()}
          startContent={
            <Icon
              name="icon-[lucide--rotate-ccw]"
              classNames="w-4 h-4"
            />
          }
        >
          Reload Page
        </Button>

        {showDetails && (
          <Button
            color="default"
            variant="light"
            size="sm"
            onPress={() => setShowErrorDetails(!showErrorDetails)}
            startContent={
              <Icon
                name={showErrorDetails ? "icon-[lucide--chevron-up]" : "icon-[lucide--chevron-down]"}
                classNames="w-4 h-4"
              />
            }
          >
            {showErrorDetails ? 'Hide' : 'Show'} Details
          </Button>
        )}
      </div>

      {/* Error Details */}
      {showDetails && showErrorDetails && (
        <div className="mt-6 p-4 bg-default-100 rounded-lg text-left">
          <h4 className="text-sm font-semibold text-default-700 mb-2">Error Details:</h4>
          <pre className="text-xs text-default-600 whitespace-pre-wrap overflow-auto max-h-40">
            {error.stack || error.message}
          </pre>
        </div>
      )}
    </div>
  );

  if (variant === "minimal") {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <div className="text-center space-y-3">
          <Icon
            name="icon-[lucide--alert-circle]"
            classNames="w-8 h-8 text-danger-500 mx-auto"
          />
          <p className="text-sm text-default-600">{error.message}</p>
          {reset && (
            <Button size="sm" color="primary" variant="flat" onPress={reset}>
              Retry
            </Button>
          )}
        </div>
      </div>
    );
  }

  if (variant === "fullscreen") {
    return (
      <div className={`fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50 p-4 ${className}`}>
        <Card className="bg-background/95 shadow-xl max-w-lg w-full">
          <CardBody className="p-8">
            <ErrorContent />
          </CardBody>
        </Card>
      </div>
    );
  }

  // Default card variant
  return (
    <div className={`p-6 ${className}`}>
      <Card className="bg-background shadow-sm max-w-2xl mx-auto">
        <CardBody className="p-8">
          <ErrorContent />
        </CardBody>
      </Card>
    </div>
  );
}
