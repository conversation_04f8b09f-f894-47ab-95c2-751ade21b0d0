import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const actionButtonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        primary: "text-white hover:focus-visible:ring-slate-950" + " " + "bg-[#65a695] hover:bg-[#5a9488]",
        teal: "text-white hover:focus-visible:ring-teal-600" + " " + "bg-[#65a695] hover:bg-[#5a9488]",
        outline: "border border-slate-200 bg-white shadow-sm hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-slate-950",
        destructive: "bg-red-500 text-white hover:bg-red-600 focus-visible:ring-red-500",
        ghost: "hover:bg-slate-100 hover:text-slate-900 focus-visible:ring-slate-950",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
)

export interface ActionButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof actionButtonVariants> {
  asChild?: boolean
}

const ActionButton = React.forwardRef<HTMLButtonElement, ActionButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(actionButtonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
ActionButton.displayName = "ActionButton"

export { ActionButton, actionButtonVariants }
