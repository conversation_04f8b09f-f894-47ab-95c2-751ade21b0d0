"use client";

import { useState } from "react";
import {
  Card,
  Card<PERSON>ody,
  CardHeader,
  Input,
  Switch,
  Button,
  Tabs,
  Tab,
  Textarea,
  Select,
  SelectItem,
  Divider,
} from "@nextui-org/react";
import { Setting<PERSON>, Shield, Bell, Database } from "lucide-react";
import toast from "react-hot-toast";

export default function AdminDeliverySettings() {
  const [activeTab, setActiveTab] = useState("policies");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Demo settings state
  const [settings, setSettings] = useState({
    policies: {
      max_delivery_distance: 50,
      min_delivery_distance: 0.5,
      max_service_areas_per_vendor: 20,
      allow_overlapping_areas: true,
      require_admin_approval: true,
      auto_resolve_conflicts: false,
    },
    verification: {
      required_documents: ['business_license', 'insurance_certificate', 'vehicle_registration'],
      auto_verification_enabled: false,
      verification_timeout_days: 7,
      require_background_check: true,
      minimum_insurance_amount: 1000000,
    },
    notifications: {
      email_notifications: true,
      sms_notifications: false,
      vendor_verification_alerts: true,
      conflict_detection_alerts: true,
      performance_alerts: true,
      daily_reports: true,
    },
    system: {
      cache_duration_minutes: 30,
      api_rate_limit: 1000,
      data_retention_days: 365,
      backup_frequency: 'daily',
      maintenance_mode: false,
    },
  });

  const handleSaveSettings = async (category: string) => {
    setIsSubmitting(true);
    
    try {
      // In a real implementation, this would call the API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(`${category} settings saved successfully!`);
    } catch (error) {
      toast.error(`Failed to save ${category} settings`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateSetting = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  return (
    <div className="max-w-4xl">
      <Tabs
        selectedKey={activeTab}
        onSelectionChange={(key) => setActiveTab(key as string)}
        className="w-full"
        variant="underlined"
      >
        <Tab key="policies" title="System Policies">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Settings className="size-5" />
                <h3 className="text-lg font-semibold">Delivery Policies</h3>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  type="number"
                  label="Maximum Delivery Distance (km)"
                  value={settings.policies.max_delivery_distance.toString()}
                  onChange={(e) => updateSetting('policies', 'max_delivery_distance', parseInt(e.target.value))}
                  min={1}
                  max={200}
                />
                
                <Input
                  type="number"
                  label="Minimum Delivery Distance (km)"
                  value={settings.policies.min_delivery_distance.toString()}
                  onChange={(e) => updateSetting('policies', 'min_delivery_distance', parseFloat(e.target.value))}
                  min={0.1}
                  max={10}
                  step={0.1}
                />
              </div>

              <Input
                type="number"
                label="Max Service Areas per Vendor"
                value={settings.policies.max_service_areas_per_vendor.toString()}
                onChange={(e) => updateSetting('policies', 'max_service_areas_per_vendor', parseInt(e.target.value))}
                min={1}
                max={100}
                className="max-w-xs"
              />

              <div className="space-y-4">
                <Switch
                  isSelected={settings.policies.allow_overlapping_areas}
                  onValueChange={(checked) => updateSetting('policies', 'allow_overlapping_areas', checked)}
                >
                  Allow overlapping service areas
                </Switch>
                
                <Switch
                  isSelected={settings.policies.require_admin_approval}
                  onValueChange={(checked) => updateSetting('policies', 'require_admin_approval', checked)}
                >
                  Require admin approval for new service areas
                </Switch>
                
                <Switch
                  isSelected={settings.policies.auto_resolve_conflicts}
                  onValueChange={(checked) => updateSetting('policies', 'auto_resolve_conflicts', checked)}
                >
                  Auto-resolve service area conflicts
                </Switch>
              </div>

              <Divider />

              <div className="flex justify-end">
                <Button
                  color="primary"
                  onPress={() => handleSaveSettings('policies')}
                  isLoading={isSubmitting}
                >
                  Save Policy Settings
                </Button>
              </div>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="verification" title="Verification">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="size-5" />
                <h3 className="text-lg font-semibold">Verification Requirements</h3>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  type="number"
                  label="Verification Timeout (days)"
                  value={settings.verification.verification_timeout_days.toString()}
                  onChange={(e) => updateSetting('verification', 'verification_timeout_days', parseInt(e.target.value))}
                  min={1}
                  max={30}
                />
                
                <Input
                  type="number"
                  label="Minimum Insurance Amount (KES)"
                  value={settings.verification.minimum_insurance_amount.toString()}
                  onChange={(e) => updateSetting('verification', 'minimum_insurance_amount', parseInt(e.target.value))}
                  min={100000}
                  step={100000}
                />
              </div>

              <div className="space-y-4">
                <Switch
                  isSelected={settings.verification.auto_verification_enabled}
                  onValueChange={(checked) => updateSetting('verification', 'auto_verification_enabled', checked)}
                >
                  Enable automatic verification (when all documents are valid)
                </Switch>
                
                <Switch
                  isSelected={settings.verification.require_background_check}
                  onValueChange={(checked) => updateSetting('verification', 'require_background_check', checked)}
                >
                  Require background check for drivers
                </Switch>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Required Documents</label>
                <div className="space-y-2">
                  {[
                    { key: 'business_license', label: 'Business License' },
                    { key: 'insurance_certificate', label: 'Insurance Certificate' },
                    { key: 'vehicle_registration', label: 'Vehicle Registration' },
                    { key: 'driver_license', label: 'Driver License' },
                  ].map((doc) => (
                    <Switch
                      key={doc.key}
                      isSelected={settings.verification.required_documents.includes(doc.key)}
                      onValueChange={(checked) => {
                        const current = settings.verification.required_documents;
                        const updated = checked
                          ? [...current, doc.key]
                          : current.filter(d => d !== doc.key);
                        updateSetting('verification', 'required_documents', updated);
                      }}
                      size="sm"
                    >
                      {doc.label}
                    </Switch>
                  ))}
                </div>
              </div>

              <Divider />

              <div className="flex justify-end">
                <Button
                  color="primary"
                  onPress={() => handleSaveSettings('verification')}
                  isLoading={isSubmitting}
                >
                  Save Verification Settings
                </Button>
              </div>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="notifications" title="Notifications">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Bell className="size-5" />
                <h3 className="text-lg font-semibold">Notification Settings</h3>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <div className="space-y-4">
                <h4 className="font-medium">General Notifications</h4>
                
                <Switch
                  isSelected={settings.notifications.email_notifications}
                  onValueChange={(checked) => updateSetting('notifications', 'email_notifications', checked)}
                >
                  Email notifications
                </Switch>
                
                <Switch
                  isSelected={settings.notifications.sms_notifications}
                  onValueChange={(checked) => updateSetting('notifications', 'sms_notifications', checked)}
                >
                  SMS notifications
                </Switch>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium">Alert Types</h4>
                
                <Switch
                  isSelected={settings.notifications.vendor_verification_alerts}
                  onValueChange={(checked) => updateSetting('notifications', 'vendor_verification_alerts', checked)}
                >
                  Vendor verification alerts
                </Switch>
                
                <Switch
                  isSelected={settings.notifications.conflict_detection_alerts}
                  onValueChange={(checked) => updateSetting('notifications', 'conflict_detection_alerts', checked)}
                >
                  Service area conflict alerts
                </Switch>
                
                <Switch
                  isSelected={settings.notifications.performance_alerts}
                  onValueChange={(checked) => updateSetting('notifications', 'performance_alerts', checked)}
                >
                  Performance threshold alerts
                </Switch>
                
                <Switch
                  isSelected={settings.notifications.daily_reports}
                  onValueChange={(checked) => updateSetting('notifications', 'daily_reports', checked)}
                >
                  Daily summary reports
                </Switch>
              </div>

              <Divider />

              <div className="flex justify-end">
                <Button
                  color="primary"
                  onPress={() => handleSaveSettings('notifications')}
                  isLoading={isSubmitting}
                >
                  Save Notification Settings
                </Button>
              </div>
            </CardBody>
          </Card>
        </Tab>

        <Tab key="system" title="System">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Database className="size-5" />
                <h3 className="text-lg font-semibold">System Configuration</h3>
              </div>
            </CardHeader>
            <CardBody className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  type="number"
                  label="Cache Duration (minutes)"
                  value={settings.system.cache_duration_minutes.toString()}
                  onChange={(e) => updateSetting('system', 'cache_duration_minutes', parseInt(e.target.value))}
                  min={5}
                  max={1440}
                />
                
                <Input
                  type="number"
                  label="API Rate Limit (requests/hour)"
                  value={settings.system.api_rate_limit.toString()}
                  onChange={(e) => updateSetting('system', 'api_rate_limit', parseInt(e.target.value))}
                  min={100}
                  max={10000}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  type="number"
                  label="Data Retention (days)"
                  value={settings.system.data_retention_days.toString()}
                  onChange={(e) => updateSetting('system', 'data_retention_days', parseInt(e.target.value))}
                  min={30}
                  max={2555}
                />
                
                <Select
                  label="Backup Frequency"
                  selectedKeys={[settings.system.backup_frequency]}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    updateSetting('system', 'backup_frequency', selected);
                  }}
                >
                  <SelectItem key="hourly" value="hourly">Hourly</SelectItem>
                  <SelectItem key="daily" value="daily">Daily</SelectItem>
                  <SelectItem key="weekly" value="weekly">Weekly</SelectItem>
                </Select>
              </div>

              <div className="space-y-4">
                <Switch
                  isSelected={settings.system.maintenance_mode}
                  onValueChange={(checked) => updateSetting('system', 'maintenance_mode', checked)}
                  color="warning"
                >
                  Maintenance mode (disables new registrations)
                </Switch>
              </div>

              <div className="p-4 bg-warning-50 border border-warning-200 rounded-lg">
                <h4 className="font-medium text-warning-800 mb-2">System Maintenance</h4>
                <p className="text-sm text-warning-700 mb-3">
                  These settings affect system performance and should be changed carefully.
                </p>
                <div className="flex space-x-2">
                  <Button size="sm" variant="flat" color="warning">
                    Clear Cache
                  </Button>
                  <Button size="sm" variant="flat" color="warning">
                    Run Backup
                  </Button>
                  <Button size="sm" variant="flat" color="danger">
                    System Health Check
                  </Button>
                </div>
              </div>

              <Divider />

              <div className="flex justify-end">
                <Button
                  color="primary"
                  onPress={() => handleSaveSettings('system')}
                  isLoading={isSubmitting}
                >
                  Save System Settings
                </Button>
              </div>
            </CardBody>
          </Card>
        </Tab>
      </Tabs>
    </div>
  );
}
