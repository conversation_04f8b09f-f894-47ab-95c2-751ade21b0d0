"use client";

import React, { useState } from "react";
import { CustomTable } from "../../custom-table";
import {
  <PERSON>,
  CardBody,
  Card<PERSON>eader,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  useDisclosure,
  Textarea,
  Progress,
} from "@nextui-org/react";
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock, 
  FileText, 
  Download,
  AlertTriangle,
} from "lucide-react";
import toast from "react-hot-toast";
// Using global PaginationMeta type from next-models.d.ts

interface VerificationDocument {
  id: string;
  type: 'business_license' | 'insurance_certificate' | 'vehicle_registration' | 'driver_license';
  name: string;
  url: string;
  status: 'pending' | 'approved' | 'rejected';
  uploaded_at: string;
}

interface VerificationRequest {
  id: string;
  vendor: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  documents: VerificationDocument[];
  submitted_at: string;
  reviewed_at?: string;
  reviewer_notes?: string;
  priority: 'low' | 'medium' | 'high';
  estimated_review_time: number;
}

interface AdminVerificationQueueProps {
  data: VerificationRequest[];
  meta: PaginationMeta;
  reviewVerificationRequest: (data: FormData) => Promise<void>;
  updateDocumentStatus: (data: FormData) => Promise<void>;
}

export default function AdminVerificationQueue({
  data,
  meta,
  reviewVerificationRequest,
  updateDocumentStatus,
}: AdminVerificationQueueProps) {
  const [selectedRequest, setSelectedRequest] = useState<VerificationRequest | null>(null);
  const [reviewNotes, setReviewNotes] = useState("");
  const [reviewAction, setReviewAction] = useState<'approve' | 'reject' | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleReview = async () => {
    if (!selectedRequest || !reviewAction) return;

    const formData = new FormData();
    formData.append("requestId", selectedRequest.id);
    formData.append("action", reviewAction);
    formData.append("notes", reviewNotes);

    toast.promise(reviewVerificationRequest(formData), {
      loading: `${reviewAction === 'approve' ? 'Approving' : 'Rejecting'} verification...`,
      success: `Verification ${reviewAction === 'approve' ? 'approved' : 'rejected'} successfully!`,
      error: "Failed to process verification",
    });

    onClose();
    setSelectedRequest(null);
    setReviewNotes("");
    setReviewAction(null);
  };

  const openReviewModal = (request: VerificationRequest, action: 'approve' | 'reject') => {
    setSelectedRequest(request);
    setReviewAction(action);
    setReviewNotes("");
    onOpen();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'under_review':
        return 'primary';
      case 'rejected':
        return 'danger';
      default:
        return 'default';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'danger';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case 'business_license':
        return 'Business License';
      case 'insurance_certificate':
        return 'Insurance Certificate';
      case 'vehicle_registration':
        return 'Vehicle Registration';
      case 'driver_license':
        return 'Driver License';
      default:
        return type;
    }
  };

  const calculateCompletionPercentage = (documents: VerificationDocument[]) => {
    const approvedDocs = documents.filter(doc => doc.status === 'approved').length;
    return (approvedDocs / documents.length) * 100;
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  return (
    <>
      <CustomTable
        title="Verification Queue"
        columns={[
          {
            name: "Vendor",
            uid: "vendor",
            sortable: true,
            renderCell: (request: VerificationRequest) => (
              <div className="flex flex-col">
                <p className="font-semibold">{request.vendor.name}</p>
                <p className="text-sm text-default-500">{request.vendor.email}</p>
                <p className="text-xs text-default-400">{request.vendor.phone}</p>
              </div>
            ),
          },
          {
            name: "Status",
            uid: "status",
            sortable: true,
            renderCell: (request: VerificationRequest) => (
              <Chip
                size="sm"
                variant="flat"
                color={getStatusColor(request.status) as any}
                className="capitalize"
              >
                {request.status.replace('_', ' ')}
              </Chip>
            ),
          },
          {
            name: "Priority",
            uid: "priority",
            sortable: true,
            renderCell: (request: VerificationRequest) => (
              <Chip
                size="sm"
                variant="flat"
                color={getPriorityColor(request.priority) as any}
                className="capitalize"
              >
                {request.priority}
              </Chip>
            ),
          },
          {
            name: "Documents",
            uid: "documents",
            renderCell: (request: VerificationRequest) => {
              const completionPercentage = calculateCompletionPercentage(request.documents);
              const approvedCount = request.documents.filter(doc => doc.status === 'approved').length;
              
              return (
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium">
                      {approvedCount}/{request.documents.length}
                    </span>
                    <Progress
                      size="sm"
                      value={completionPercentage}
                      color={completionPercentage === 100 ? "success" : "warning"}
                      className="flex-1"
                    />
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {request.documents.map((doc) => (
                      <Chip
                        key={doc.id}
                        size="sm"
                        variant="bordered"
                        color={getStatusColor(doc.status) as any}
                        className="text-xs"
                      >
                        {getDocumentTypeLabel(doc.type)}
                      </Chip>
                    ))}
                  </div>
                </div>
              );
            },
          },
          {
            name: "Submitted",
            uid: "submitted_at",
            sortable: true,
            renderCell: (request: VerificationRequest) => (
              <div className="flex flex-col">
                <span className="text-sm">{formatTime(request.submitted_at)}</span>
                <span className="text-xs text-default-500">
                  Est. {request.estimated_review_time}h review
                </span>
              </div>
            ),
          },
          {
            name: "Actions",
            uid: "actions",
            renderCell: (request: VerificationRequest) => (
              <div className="flex items-center gap-2">
                <Button
                  isIconOnly
                  size="sm"
                  variant="light"
                  onPress={() => {
                    // Open detailed view modal
                    setSelectedRequest(request);
                    // You could implement a detailed view modal here
                  }}
                >
                  <Eye className="size-4" />
                </Button>
                
                {request.status === 'pending' && (
                  <>
                    <Button
                      isIconOnly
                      size="sm"
                      color="success"
                      variant="flat"
                      onPress={() => openReviewModal(request, 'approve')}
                    >
                      <CheckCircle className="size-4" />
                    </Button>
                    <Button
                      isIconOnly
                      size="sm"
                      color="danger"
                      variant="flat"
                      onPress={() => openReviewModal(request, 'reject')}
                    >
                      <XCircle className="size-4" />
                    </Button>
                  </>
                )}
              </div>
            ),
          },
        ]}
        data={data}
        meta={meta}
        filter={{
          column: "status",
          displayName: "Status",
          values: [
            { name: "Pending", value: "pending" },
            { name: "Under Review", value: "under_review" },
            { name: "Approved", value: "approved" },
            { name: "Rejected", value: "rejected" },
          ],
        }}
      />

      {/* Review Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalContent>
          <ModalHeader>
            {reviewAction === 'approve' ? 'Approve' : 'Reject'} Verification Request
          </ModalHeader>
          <ModalBody>
            {selectedRequest && (
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <h4 className="font-semibold">Vendor Information</h4>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-2">
                      <p><strong>Name:</strong> {selectedRequest.vendor.name}</p>
                      <p><strong>Email:</strong> {selectedRequest.vendor.email}</p>
                      <p><strong>Phone:</strong> {selectedRequest.vendor.phone}</p>
                      <p><strong>Submitted:</strong> {formatTime(selectedRequest.submitted_at)}</p>
                    </div>
                  </CardBody>
                </Card>

                <Card>
                  <CardHeader>
                    <h4 className="font-semibold">Documents</h4>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-2">
                      {selectedRequest.documents.map((doc) => (
                        <div key={doc.id} className="flex items-center justify-between p-2 border rounded">
                          <div className="flex items-center space-x-2">
                            <FileText className="size-4" />
                            <span className="text-sm">{getDocumentTypeLabel(doc.type)}</span>
                            <Chip size="sm" color={getStatusColor(doc.status) as any} variant="flat">
                              {doc.status}
                            </Chip>
                          </div>
                          <Button
                            size="sm"
                            variant="light"
                            startContent={<Download className="size-4" />}
                            onPress={() => {
                              // In a real implementation, this would download the document
                              toast.success("Document download started");
                            }}
                          >
                            Download
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                </Card>

                <Textarea
                  label="Review Notes"
                  placeholder={`Add notes for ${reviewAction === 'approve' ? 'approval' : 'rejection'}...`}
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  minRows={3}
                />

                {reviewAction === 'reject' && (
                  <div className="p-3 bg-danger-50 border border-danger-200 rounded-lg">
                    <div className="flex items-start space-x-2">
                      <AlertTriangle className="size-5 text-danger-600 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-danger-800">
                          Rejection Notice
                        </p>
                        <p className="text-sm text-danger-700">
                          The vendor will be notified of the rejection and can resubmit documents.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color={reviewAction === 'approve' ? 'success' : 'danger'}
              onPress={handleReview}
              isDisabled={!reviewNotes.trim()}
            >
              {reviewAction === 'approve' ? 'Approve' : 'Reject'} Verification
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}
