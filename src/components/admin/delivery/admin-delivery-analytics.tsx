"use client";

import { useState } from "react";
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Select, 
  SelectItem,
  Chip,
  Progress,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@nextui-org/react";
import { 
  TrendingUp, 
  TrendingDown, 
  Download, 
  Calendar,
  Users,
  MapPin,
  Clock,
  DollarSign,
  Star,
  AlertTriangle,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";

interface DeliveryAnalytics {
  overview: {
    total_deliveries: number;
    success_rate: number;
    average_delivery_time: number;
    total_revenue: number;
    active_vendors: number;
    coverage_areas: number;
  };
  trends: {
    daily_deliveries: Array<{
      date: string;
      deliveries: number;
      success_rate: number;
    }>;
    vendor_performance: Array<{
      vendor_id: string;
      vendor_name: string;
      deliveries: number;
      success_rate: number;
      rating: number;
    }>;
    regional_performance: Array<{
      region: string;
      deliveries: number;
      success_rate: number;
      average_time: number;
    }>;
  };
  insights: {
    top_performing_areas: Array<{
      area_name: string;
      vendor_name: string;
      success_rate: number;
      order_count: number;
    }>;
    improvement_opportunities: Array<{
      area_name: string;
      vendor_name: string;
      issue: string;
      impact: 'high' | 'medium' | 'low';
    }>;
  };
}

interface AdminDeliveryAnalyticsProps {
  analytics: DeliveryAnalytics;
  timeframe: string;
}

export default function AdminDeliveryAnalytics({
  analytics,
  timeframe,
}: AdminDeliveryAnalyticsProps) {
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getPerformanceColor = (rate: number) => {
    if (rate >= 95) return 'success';
    if (rate >= 85) return 'warning';
    return 'danger';
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high':
        return 'danger';
      case 'medium':
        return 'warning';
      case 'low':
        return 'success';
      default:
        return 'default';
    }
  };

  const handleTimeframeChange = (newTimeframe: string) => {
    setSelectedTimeframe(newTimeframe);
    // In a real implementation, this would trigger a data refresh
    window.location.href = `/admin/delivery-management/analytics?timeframe=${newTimeframe}`;
  };

  const exportReport = () => {
    // In a real implementation, this would generate and download a report
    alert("Report export functionality would be implemented here");
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Select
            label="Timeframe"
            selectedKeys={[selectedTimeframe]}
            onSelectionChange={(keys) => {
              const selected = Array.from(keys)[0] as string;
              handleTimeframeChange(selected);
            }}
            className="w-48"
          >
            <SelectItem key="7d" value="7d">Last 7 days</SelectItem>
            <SelectItem key="30d" value="30d">Last 30 days</SelectItem>
            <SelectItem key="90d" value="90d">Last 90 days</SelectItem>
            <SelectItem key="1y" value="1y">Last year</SelectItem>
          </Select>
        </div>
        
        <Button
          color="primary"
          variant="flat"
          startContent={<Download className="size-4" />}
          onPress={exportReport}
        >
          Export Report
        </Button>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Deliveries</p>
                <p className="text-2xl font-bold">{analytics.overview.total_deliveries.toLocaleString()}</p>
              </div>
              <div className="p-2 bg-blue-100 rounded-lg">
                <MapPin className="size-6 text-blue-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+15% from last period</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold">{analytics.overview.success_rate}%</p>
              </div>
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="size-6 text-green-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+2.3% improvement</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Delivery Time</p>
                <p className="text-2xl font-bold">{analytics.overview.average_delivery_time}min</p>
              </div>
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="size-6 text-yellow-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingDown className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">-3min faster</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold">{formatCurrency(analytics.overview.total_revenue)}</p>
              </div>
              <div className="p-2 bg-purple-100 rounded-lg">
                <DollarSign className="size-6 text-purple-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+22% growth</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Vendors</p>
                <p className="text-2xl font-bold">{analytics.overview.active_vendors}</p>
              </div>
              <div className="p-2 bg-indigo-100 rounded-lg">
                <Users className="size-6 text-indigo-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+8 new vendors</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Coverage Areas</p>
                <p className="text-2xl font-bold">{analytics.overview.coverage_areas}</p>
              </div>
              <div className="p-2 bg-teal-100 rounded-lg">
                <MapPin className="size-6 text-teal-600" />
              </div>
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="size-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+12% expansion</span>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Performance Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Vendor Performance */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Top Vendor Performance</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="Vendor performance table">
              <TableHeader>
                <TableColumn>VENDOR</TableColumn>
                <TableColumn>DELIVERIES</TableColumn>
                <TableColumn>SUCCESS RATE</TableColumn>
                <TableColumn>RATING</TableColumn>
              </TableHeader>
              <TableBody>
                {analytics.trends.vendor_performance.slice(0, 5).map((vendor) => (
                  <TableRow key={vendor.vendor_id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{vendor.vendor_name}</span>
                      </div>
                    </TableCell>
                    <TableCell>{vendor.deliveries.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        color={getPerformanceColor(vendor.success_rate) as any}
                        variant="flat"
                      >
                        {vendor.success_rate}%
                      </Chip>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Star className="size-4 text-yellow-500 fill-current" />
                        <span>{vendor.rating}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>

        {/* Regional Performance */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Regional Performance</h3>
          </CardHeader>
          <CardBody>
            <Table aria-label="Regional performance table">
              <TableHeader>
                <TableColumn>REGION</TableColumn>
                <TableColumn>DELIVERIES</TableColumn>
                <TableColumn>SUCCESS RATE</TableColumn>
                <TableColumn>AVG TIME</TableColumn>
              </TableHeader>
              <TableBody>
                {analytics.trends.regional_performance.map((region) => (
                  <TableRow key={region.region}>
                    <TableCell>
                      <span className="font-medium">{region.region}</span>
                    </TableCell>
                    <TableCell>{region.deliveries.toLocaleString()}</TableCell>
                    <TableCell>
                      <Chip
                        size="sm"
                        color={getPerformanceColor(region.success_rate) as any}
                        variant="flat"
                      >
                        {region.success_rate}%
                      </Chip>
                    </TableCell>
                    <TableCell>{region.average_time}min</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardBody>
        </Card>
      </div>

      {/* Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Areas */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center space-x-2">
              <CheckCircle className="size-5 text-green-500" />
              <span>Top Performing Areas</span>
            </h3>
          </CardHeader>
          <CardBody className="space-y-3">
            {analytics.insights.top_performing_areas.map((area, index) => (
              <div key={index} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-green-900">{area.area_name}</p>
                    <p className="text-sm text-green-700">{area.vendor_name}</p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-900">{area.success_rate}%</p>
                    <p className="text-sm text-green-700">{area.order_count} orders</p>
                  </div>
                </div>
              </div>
            ))}
          </CardBody>
        </Card>

        {/* Improvement Opportunities */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold flex items-center space-x-2">
              <AlertTriangle className="size-5 text-yellow-500" />
              <span>Improvement Opportunities</span>
            </h3>
          </CardHeader>
          <CardBody className="space-y-3">
            {analytics.insights.improvement_opportunities.map((opportunity, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="font-medium">{opportunity.area_name}</p>
                    <p className="text-sm text-gray-600">{opportunity.vendor_name}</p>
                    <p className="text-sm text-gray-700 mt-1">{opportunity.issue}</p>
                  </div>
                  <Chip
                    size="sm"
                    color={getImpactColor(opportunity.impact) as any}
                    variant="flat"
                    className="ml-2"
                  >
                    {opportunity.impact} impact
                  </Chip>
                </div>
              </div>
            ))}
          </CardBody>
        </Card>
      </div>

      {/* Chart Placeholder */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Delivery Trends</h3>
        </CardHeader>
        <CardBody>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <Calendar className="size-12 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600 font-medium">Interactive Charts Coming Soon</p>
              <p className="text-sm text-gray-500">
                Daily delivery trends, success rates, and performance metrics
              </p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}
