"use client";

import { ReactNode } from "react";
import { <PERSON><PERSON> } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import Link from "next/link";

interface AdminPageLayoutProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
  }>;
  actions?: ReactNode;
  children: ReactNode;
}

interface AdminPageHeaderProps {
  title: string;
  subtitle?: string;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
  }>;
  actions?: ReactNode;
}

interface BreadcrumbsProps {
  items: Array<{
    label: string;
    href?: string;
  }>;
}

// Breadcrumbs component
export function Breadcrumbs({ items }: BreadcrumbsProps) {
  return (
    <nav className="flex mb-4" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {items.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <Icon
                name="icon-[heroicons--chevron-right-20-solid]"
                classNames="w-4 h-4 text-gray-400 mx-1"
              />
            )}
            {item.href ? (
              <Link
                href={item.href}
                className="text-sm font-medium text-gray-500 hover:text-gray-700"
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-sm font-medium text-gray-900">
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}

// Page header component
export function AdminPageHeader({ 
  title, 
  subtitle, 
  breadcrumbs, 
  actions 
}: AdminPageHeaderProps) {
  return (
    <div className="mb-6">
      {breadcrumbs && <Breadcrumbs items={breadcrumbs} />}
      
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        
        {actions && (
          <div className="flex items-center gap-3">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
}

// Main layout component
export function AdminPageLayout({ 
  title, 
  subtitle, 
  breadcrumbs, 
  actions, 
  children 
}: AdminPageLayoutProps) {
  return (
    <div className="page-content p-5">
      <AdminPageHeader
        title={title}
        subtitle={subtitle}
        breadcrumbs={breadcrumbs}
        actions={actions}
      />
      {children}
    </div>
  );
}

// Common action buttons
export const AdminActionButtons = {
  Back: ({ href, label = "Back" }: { href: string; label?: string }) => (
    <Button
      as={Link}
      href={href}
      variant="light"
      startContent={<Icon name="icon-[heroicons--arrow-left-20-solid]" />}
    >
      {label}
    </Button>
  ),
  
  Edit: ({ href }: { href: string }) => (
    <Button
      as={Link}
      href={href}
      color="primary"
      startContent={<Icon name="icon-[heroicons--pencil-20-solid]" />}
    >
      Edit
    </Button>
  ),
  
  Create: ({ href, label = "Create" }: { href: string; label?: string }) => (
    <Button
      as={Link}
      href={href}
      color="primary"
      startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
    >
      {label}
    </Button>
  ),
  
  Delete: ({ onPress, isLoading = false }: { onPress: () => void; isLoading?: boolean }) => (
    <Button
      onPress={onPress}
      color="danger"
      variant="light"
      isLoading={isLoading}
      startContent={!isLoading ? <Icon name="icon-[heroicons--trash-20-solid]" /> : undefined}
    >
      Delete
    </Button>
  ),
  
  Settings: ({ href }: { href: string }) => (
    <Button
      as={Link}
      href={href}
      variant="light"
      startContent={<Icon name="icon-[heroicons--cog-6-tooth-20-solid]" />}
    >
      Settings
    </Button>
  ),
};
