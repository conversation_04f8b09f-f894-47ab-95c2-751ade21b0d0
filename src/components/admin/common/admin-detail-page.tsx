"use client";

import { ReactNode } from "react";
import { <PERSON>, CardB<PERSON>, Button, Chip } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import Link from "next/link";

interface AdminDetailPageProps {
  title: string;
  subtitle?: string;
  isLoading?: boolean;
  error?: string | null;
  actions?: ReactNode;
  children: ReactNode;
}

interface DetailSectionProps {
  title: string;
  children: ReactNode;
  actions?: ReactNode;
}

interface DetailFieldProps {
  label: string;
  value: string | ReactNode;
  type?: 'text' | 'chip' | 'code' | 'date';
  chipColor?: 'success' | 'warning' | 'danger' | 'default' | 'primary' | 'secondary';
}

interface MetadataCardProps {
  title: string;
  fields: Array<{
    label: string;
    value: string;
    type?: 'text' | 'code' | 'date';
  }>;
}

interface QuickActionsCardProps {
  title: string;
  actions: Array<{
    label: string;
    href?: string;
    onClick?: () => void;
    icon: string;
    color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    variant?: 'solid' | 'bordered' | 'light' | 'flat' | 'faded' | 'shadow' | 'ghost';
  }>;
}

// Main detail page component
export function AdminDetailPage({ 
  title, 
  subtitle, 
  isLoading, 
  error, 
  actions, 
  children 
}: AdminDetailPageProps) {
  if (isLoading) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page-content p-5">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">{error}</h2>
            <p className="text-gray-600 mb-4">
              The item you&apos;re looking for doesn&apos;t exist or has been removed.
            </p>
            <Button
              as={Link}
              href="/admin"
              color="primary"
            >
              Back to Admin
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-content p-5">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
          {subtitle && (
            <p className="text-gray-600 mt-1">{subtitle}</p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-3">
            {actions}
          </div>
        )}
      </div>

      {/* Content */}
      {children}
    </div>
  );
}

// Detail section component
export function DetailSection({ title, children, actions }: DetailSectionProps) {
  return (
    <Card>
      <CardBody>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">{title}</h3>
          {actions}
        </div>
        {children}
      </CardBody>
    </Card>
  );
}

// Detail field component
export function DetailField({ label, value, type = 'text', chipColor = 'default' }: DetailFieldProps) {
  const renderValue = () => {
    switch (type) {
      case 'chip':
        return (
          <Chip color={chipColor} variant="flat" size="sm">
            {value}
          </Chip>
        );
      case 'code':
        return (
          <code className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
            {value}
          </code>
        );
      case 'date':
        return (
          <span className="text-gray-900">
            {typeof value === 'string' ? new Date(value).toLocaleDateString() : value}
          </span>
        );
      default:
        return <span className="text-gray-900">{value}</span>;
    }
  };

  return (
    <div>
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="mt-1">{renderValue()}</div>
    </div>
  );
}

// Metadata card component
export function MetadataCard({ title, fields }: MetadataCardProps) {
  return (
    <Card>
      <CardBody>
        <h3 className="text-lg font-semibold mb-4">{title}</h3>
        <div className="space-y-3 text-sm">
          {fields.map((field, index) => (
            <DetailField
              key={index}
              label={field.label}
              value={field.value}
              type={field.type}
            />
          ))}
        </div>
      </CardBody>
    </Card>
  );
}

// Quick actions card component
export function QuickActionsCard({ title, actions }: QuickActionsCardProps) {
  return (
    <Card>
      <CardBody>
        <h3 className="text-lg font-semibold mb-4">{title}</h3>
        <div className="space-y-2">
          {actions.map((action, index) => (
            <Button
              key={index}
              as={action.href ? Link : 'button'}
              href={action.href}
              onClick={action.onClick}
              variant={action.variant || 'light'}
              color={action.color || 'default'}
              className="w-full justify-start"
              startContent={<Icon name={action.icon} />}
            >
              {action.label}
            </Button>
          ))}
        </div>
      </CardBody>
    </Card>
  );
}
