"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import {
  Input,
  Select,
  SelectItem,
  Textarea,
  Switch,
} from "@nextui-org/react";
import toast from "react-hot-toast";
import { AdminFormModalWithForm } from "@/components/admin/common/admin-form-modal";

// Duration interface for the dropdown
interface Duration {
  id: string;
  name: string;
  minutes: number;
  category: string;
  active: boolean;
}

// Form data interface for creating new options
interface OptionFormData {
  name: string;
  type: string;
  description: string;
  priceAdjustment: number;
  durationId: string;
  isDefault: boolean;
  sortOrder: number;
  constraints: {
    requiresSpecialEquipment?: boolean;
    weatherDependent?: boolean;
    skillLevel?: string;
    maxQuantity?: number;
    minAdvanceHours?: number;
  };
  active: boolean;
}

interface ConfigurationOptionFormProps {
  isOpen: boolean;
  onClose: () => void;
  configurationId: string;
  onSuccess: (newOption: any) => void;
  existingOptionsCount: number;
}

// Option types from the API documentation
const OPTION_TYPES = [
  { key: "duration", label: "Duration" },
  { key: "location", label: "Location" },
  { key: "personnel", label: "Personnel" },
  { key: "equipment", label: "Equipment" },
  { key: "delivery_method", label: "Delivery Method" },
  { key: "expertise_level", label: "Expertise Level" },
  { key: "add_on", label: "Add-on Service" },
  { key: "scheduling", label: "Scheduling" },
  { key: "custom", label: "Custom" },
];

export default function ConfigurationOptionForm({
  isOpen,
  onClose,
  configurationId,
  onSuccess,
  existingOptionsCount,
}: ConfigurationOptionFormProps) {
  const [durations, setDurations] = useState<Duration[]>([]);
  const [submitting, setSubmitting] = useState(false);

  // Form handling
  const {
    register,
    handleSubmit,
    control,
    reset,
    watch,
    formState: { errors },
  } = useForm<OptionFormData>({
    defaultValues: {
      name: "",
      type: "duration",
      description: "",
      priceAdjustment: 0,
      durationId: "",
      isDefault: false,
      sortOrder: existingOptionsCount + 1,
      constraints: {},
      active: true,
    },
  });

  // Fetch durations when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchDurations();
    }
  }, [isOpen]);

  const fetchDurations = async () => {
    try {
      const response = await fetch('/api/durations?active=true&limit=100');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data?.data) {
          setDurations(result.data.data);
        }
      }
    } catch (error) {
      console.error('Error fetching durations:', error);
    }
  };

  // Form submission handler
  const onSubmit = async (data: OptionFormData) => {
    try {
      setSubmitting(true);
      
      // Prepare the request body
      const requestBody = {
        ...data,
        // Only include durationId if type is duration and a duration is selected
        durationId: data.type === 'duration' && data.durationId ? data.durationId : undefined,
      };

      const response = await fetch(`/api/service-configurations/${configurationId}/options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          toast.success('Option created successfully');
          onSuccess(result.data); // Notify parent component
          reset(); // Reset form
          onClose(); // Close modal
        } else {
          toast.error(result.error || 'Failed to create option');
        }
      } else {
        const errorResult = await response.json();
        toast.error(errorResult.error || 'Failed to create option');
      }
    } catch (error) {
      console.error('Error creating option:', error);
      toast.error('Failed to create option');
    } finally {
      setSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <AdminFormModalWithForm
      isOpen={isOpen}
      onClose={handleClose}
      title="Add Configuration Option"
      size="3xl"
      isSubmitting={submitting}
      submitLabel="Add Option"
      onSubmit={handleSubmit(onSubmit)}
    >
      {/* Basic Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Basic Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            {...register("name", { 
              required: "Option name is required",
              minLength: { value: 2, message: "Name must be at least 2 characters" }
            })}
            label="Option Name"
            placeholder="e.g., Premium Detail (2 hrs)"
            isInvalid={!!errors.name}
            errorMessage={errors.name?.message}
          />

          <Controller
            name="type"
            control={control}
            rules={{ required: "Option type is required" }}
            render={({ field }) => (
              <Select
                selectedKeys={field.value ? new Set([field.value]) : new Set()}
                onSelectionChange={(keys) => {
                  const selectedValue = Array.from(keys)[0] as string;
                  field.onChange(selectedValue);
                }}
                label="Option Type"
                placeholder="Select option type"
                isInvalid={!!errors.type}
                errorMessage={errors.type?.message}
              >
                {OPTION_TYPES.map((type) => (
                  <SelectItem key={type.key} value={type.key}>
                    {type.label}
                  </SelectItem>
                ))}
              </Select>
            )}
          />
        </div>

        <Textarea
          {...register("description")}
          label="Description"
          placeholder="Detailed description of this option..."
          minRows={2}
          maxRows={4}
        />
      </div>

      {/* Pricing and Duration */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Pricing & Duration</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            {...register("priceAdjustment", { 
              valueAsNumber: true,
              min: { value: -1000, message: "Price adjustment cannot be less than -1000" }
            })}
            type="number"
            label="Price Adjustment"
            placeholder="0"
            startContent="$"
            description="Amount to add/subtract from base price"
            isInvalid={!!errors.priceAdjustment}
            errorMessage={errors.priceAdjustment?.message}
          />

          {watch("type") === "duration" && (
            <Controller
              name="durationId"
              control={control}
              rules={{ 
                required: watch("type") === "duration" ? "Duration is required for duration type options" : false 
              }}
              render={({ field }) => (
                <Select
                  selectedKeys={field.value ? new Set([field.value]) : new Set()}
                  onSelectionChange={(keys) => {
                    const selectedValue = Array.from(keys)[0] as string;
                    field.onChange(selectedValue);
                  }}
                  label="Duration"
                  placeholder="Select duration"
                  isInvalid={!!errors.durationId}
                  errorMessage={errors.durationId?.message}
                >
                  {durations.map((duration) => (
                    <SelectItem key={duration.id} value={duration.id}>
                      {duration.name} ({duration.minutes} min)
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          )}
        </div>
      </div>

      {/* Settings */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Settings</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Input
            {...register("sortOrder", {
              valueAsNumber: true,
              min: { value: 1, message: "Sort order must be at least 1" }
            })}
            type="number"
            label="Sort Order"
            placeholder="1"
            description="Display order (lower numbers first)"
            isInvalid={!!errors.sortOrder}
            errorMessage={errors.sortOrder?.message}
          />

          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Default Option</label>
            <Controller
              name="isDefault"
              control={control}
              render={({ field }) => (
                <Switch
                  isSelected={field.value}
                  onValueChange={field.onChange}
                  size="sm"
                >
                  Set as default
                </Switch>
              )}
            />
            <p className="text-xs text-gray-500">This option will be pre-selected</p>
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-sm font-medium">Status</label>
            <Controller
              name="active"
              control={control}
              render={({ field }) => (
                <Switch
                  isSelected={field.value}
                  onValueChange={field.onChange}
                  size="sm"
                  color="success"
                >
                  Active
                </Switch>
              )}
            />
            <p className="text-xs text-gray-500">Option is available for selection</p>
          </div>
        </div>
      </div>

      {/* Constraints (Optional) */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Constraints (Optional)</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Controller
            name="constraints.requiresSpecialEquipment"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-2">
                <Switch
                  isSelected={field.value || false}
                  onValueChange={field.onChange}
                  size="sm"
                >
                  Requires Special Equipment
                </Switch>
              </div>
            )}
          />

          <Controller
            name="constraints.weatherDependent"
            control={control}
            render={({ field }) => (
              <div className="flex flex-col gap-2">
                <Switch
                  isSelected={field.value || false}
                  onValueChange={field.onChange}
                  size="sm"
                >
                  Weather Dependent
                </Switch>
              </div>
            )}
          />

          <Input
            {...register("constraints.maxQuantity", { valueAsNumber: true })}
            type="number"
            label="Max Quantity"
            placeholder="1"
            description="Maximum number that can be selected"
            min="1"
          />

          <Input
            {...register("constraints.minAdvanceHours", { valueAsNumber: true })}
            type="number"
            label="Min Advance Hours"
            placeholder="2"
            description="Minimum hours in advance to book"
            min="0"
          />

          <Controller
            name="constraints.skillLevel"
            control={control}
            render={({ field }) => (
              <Select
                selectedKeys={field.value ? new Set([field.value]) : new Set()}
                onSelectionChange={(keys) => {
                  const selectedValue = Array.from(keys)[0] as string;
                  field.onChange(selectedValue);
                }}
                label="Skill Level Required"
                placeholder="Select skill level"
              >
                <SelectItem key="basic" value="basic">Basic</SelectItem>
                <SelectItem key="intermediate" value="intermediate">Intermediate</SelectItem>
                <SelectItem key="advanced" value="advanced">Advanced</SelectItem>
                <SelectItem key="expert" value="expert">Expert</SelectItem>
              </Select>
            )}
          />
        </div>
      </div>
    </AdminFormModalWithForm>
  );
}
