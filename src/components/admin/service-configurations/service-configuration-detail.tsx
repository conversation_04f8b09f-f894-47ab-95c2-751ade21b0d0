"use client";

import { useState, useEffect } from "react";
import { 
  AdminDetailPage, 
  DetailSection, 
  DetailField, 
  MetadataCard, 
  QuickActionsCard 
} from "../common/admin-detail-page";
import { AdminActionButtons } from "../common/admin-page-layout";
import toast from "react-hot-toast";

interface ServiceConfiguration {
  id: string;
  name: string;
  description?: string;
  serviceId: string;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

interface ServiceConfigurationDetailProps {
  configurationId: string;
}

export default function ServiceConfigurationDetail({ 
  configurationId 
}: ServiceConfigurationDetailProps) {
  const [configuration, setConfiguration] = useState<ServiceConfiguration | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch service configuration
  useEffect(() => {
    const fetchConfiguration = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/service-configurations/${configurationId}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Configuration not found');
          } else {
            setError('Failed to load configuration');
          }
          return;
        }
        
        const result = await response.json();
        if (result.success) {
          setConfiguration(result.data);
        } else {
          setError('Configuration not found');
        }
      } catch (error: any) {
        console.error('Error fetching service configuration:', error);

        // Handle specific error cases
        if (error.message?.includes('priceAdjustment')) {
          setError('Configuration has data integrity issues. Please contact support.');
        } else if (error.response?.status === 404) {
          setError('Configuration not found');
        } else {
          setError('Failed to load configuration');
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchConfiguration();
  }, [configurationId]);

  const handleDelete = async () => {
    if (!configuration) return;
    
    if (!confirm(`Are you sure you want to delete "${configuration.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/service-configurations/${configuration.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Configuration deleted successfully');
        // Redirect to list page
        window.location.href = '/admin/service-configurations';
      } else {
        toast.error('Failed to delete configuration');
      }
    } catch (error) {
      console.error('Error deleting configuration:', error);
      toast.error('Failed to delete configuration');
    }
  };

  return (
    <AdminDetailPage
      title={configuration?.name || 'Service Configuration'}
      subtitle="Service configuration details and management"
      isLoading={isLoading}
      error={error}
      actions={
        configuration && (
          <>
            <AdminActionButtons.Edit 
              href={`/admin/service-configurations/${configuration.id}/edit`} 
            />
            <AdminActionButtons.Back 
              href="/admin/service-configurations" 
              label="All Configurations"
            />
          </>
        )
      }
    >
      {configuration && (
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Main Details */}
          <div className="lg:col-span-2 space-y-6">
            <DetailSection title="Configuration Details">
              <div className="space-y-4">
                <DetailField
                  label="Name"
                  value={configuration.name}
                />
                
                {configuration.description && (
                  <DetailField
                    label="Description"
                    value={configuration.description}
                  />
                )}
                
                <DetailField
                  label="Service ID"
                  value={configuration.serviceId}
                  type="code"
                />
                
                <DetailField
                  label="Status"
                  value={configuration.active ? "Active" : "Inactive"}
                  type="chip"
                  chipColor={configuration.active ? "success" : "default"}
                />
              </div>
            </DetailSection>

            <DetailSection 
              title="Configuration Options"
              actions={
                <AdminActionButtons.Settings 
                  href={`/admin/service-configurations/${configuration.id}/options`}
                />
              }
            >
              <p className="text-gray-600">
                Configure duration options, add-on services, and custom settings for this service template.
              </p>
            </DetailSection>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <MetadataCard
              title="Metadata"
              fields={[
                {
                  label: "Created",
                  value: configuration.createdAt,
                  type: "date"
                },
                {
                  label: "Last Updated", 
                  value: configuration.updatedAt,
                  type: "date"
                },
                {
                  label: "Configuration ID",
                  value: configuration.id,
                  type: "code"
                }
              ]}
            />

            <QuickActionsCard
              title="Quick Actions"
              actions={[
                {
                  label: "Manage Options",
                  href: `/admin/service-configurations/${configuration.id}/options`,
                  icon: "icon-[heroicons--cog-6-tooth-20-solid]"
                },
                {
                  label: "Edit Configuration",
                  href: `/admin/service-configurations/${configuration.id}/edit`,
                  icon: "icon-[heroicons--pencil-20-solid]"
                },
                {
                  label: "Delete Configuration",
                  onClick: handleDelete,
                  icon: "icon-[heroicons--trash-20-solid]",
                  color: "danger"
                }
              ]}
            />
          </div>
        </div>
      )}
    </AdminDetailPage>
  );
}
