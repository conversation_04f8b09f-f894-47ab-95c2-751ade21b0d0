"use client";

import { Modifier } from "@/types/modifiers";
import { CustomTable } from "./custom-table";
import { Chip } from "@nextui-org/react";
import Link from "next/link";

interface AvailableModifiersTableProps {
  modifiers: Modifier[];
  productId: string;
}

export function AvailableModifiersTable({ modifiers, productId }: AvailableModifiersTableProps) {
  const columns = [
    {
      name: "Name",
      uid: "name",
      sortable: true,
      renderCell: (modifier: Modifier) => (
        <div className="flex flex-col">
          <span className="font-medium text-default-800">{modifier.name}</span>
          {modifier.description && (
            <span className="text-sm text-default-500">{modifier.description}</span>
          )}
        </div>
      ),
    },
    {
      name: "Type",
      uid: "type",
      sortable: true,
      renderCell: (modifier: Modifier) => (
        <Chip
          size="sm"
          variant="flat"
          color="primary"
          className="capitalize"
        >
          {modifier.type}
        </Chip>
      ),
    },
    {
      name: "Price Adjustment",
      uid: "defaultPriceAdjustment",
      sortable: true,
      renderCell: (modifier: Modifier) => (
        <span className="font-medium text-default-700">
          KES {parseFloat(modifier.defaultPriceAdjustment).toFixed(2)}
        </span>
      ),
    },
    {
      name: "Status",
      uid: "active",
      sortable: true,
      renderCell: (modifier: Modifier) => (
        <Chip
          size="sm"
          variant="flat"
          color={modifier.active ? "success" : "danger"}
        >
          {modifier.active ? "Active" : "Inactive"}
        </Chip>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-default-800">Available Modifiers</h1>
        <Link
          href={`/products/${productId}/edit?tab=extra`}
          className="rounded-lg bg-primary px-4 py-2 text-sm text-white"
        >
          Edit Modifiers
        </Link>
      </div>
      
      <CustomTable
        title="Available Modifiers"
        data={modifiers}
        columns={columns}
        isDashboard={true}
      />
    </div>
  );
}
