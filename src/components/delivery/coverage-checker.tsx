"use client";

import { useState } from "react";
import { <PERSON>, <PERSON>B<PERSON>, CardHeader, Input, But<PERSON>, Chip } from "@nextui-org/react";
import { MapPin, Search } from "lucide-react";
import toast from "react-hot-toast";
import { api } from "@/lib/api";

interface CoverageResult {
  in_service_area: boolean;
  service_areas: Array<{
    id: string;
    name: string;
    type: string;
    priority: number;
    branch: {
      id: string;
      name: string;
    };
  }>;
}

interface CoverageCheckerProps {
  vendorId: string;
}

export default function CoverageChecker({ vendorId }: CoverageCheckerProps) {
  const [address, setAddress] = useState("");
  const [isChecking, setIsChecking] = useState(false);
  const [result, setResult] = useState<CoverageResult | null>(null);
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);

  const checkCoverage = async () => {
    if (!address.trim()) {
      toast.error("Please enter an address to check");
      return;
    }

    setIsChecking(true);
    setResult(null);

    try {
      // For demo purposes, we'll use a simple geocoding simulation
      // In a real implementation, you'd use Google Maps Geocoding API or similar
      const mockCoordinates = {
        lat: -1.2921 + (Math.random() - 0.5) * 0.1, // Random coordinates around Nairobi
        lng: 36.8219 + (Math.random() - 0.5) * 0.1,
      };

      setCoordinates(mockCoordinates);

      // Check coverage using the API
      const coverageResult = await api.get<CoverageResult>(
        `vendors/${vendorId}/service-areas/check`,
        {
          lat: mockCoordinates.lat,
          lng: mockCoordinates.lng,
        }
      );

      setResult(coverageResult || {
        in_service_area: false,
        service_areas: [],
      });

      if (coverageResult?.in_service_area) {
        toast.success("Address is within service area!");
      } else {
        toast.error("Address is not covered by any service area");
      }
    } catch (error) {
      console.error("Coverage check failed:", error);
      
      // Fallback demo result
      const demoResult: CoverageResult = {
        in_service_area: Math.random() > 0.3, // 70% chance of coverage for demo
        service_areas: Math.random() > 0.3 ? [
          {
            id: "demo-1",
            name: "Downtown Coverage",
            type: "circle",
            priority: 1,
            branch: {
              id: "branch-1",
              name: "Main Branch",
            },
          },
        ] : [],
      };
      
      setResult(demoResult);
      
      if (demoResult.in_service_area) {
        toast.success("Address is within service area! (Demo)");
      } else {
        toast.error("Address is not covered by any service area (Demo)");
      }
    } finally {
      setIsChecking(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      checkCoverage();
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <MapPin className="size-5 text-primary" />
          <h3 className="text-lg font-semibold">Coverage Checker</h3>
        </div>
      </CardHeader>
      <CardBody className="space-y-4">
        <p className="text-sm text-gray-600">
          Enter an address to check if it&apos;s within your delivery service areas.
        </p>

        <div className="flex space-x-2">
          <Input
            placeholder="Enter address (e.g., Westlands, Nairobi)"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            onKeyPress={handleKeyPress}
            startContent={<Search className="size-4 text-gray-400" />}
            className="flex-1"
          />
          <Button
            color="primary"
            onPress={checkCoverage}
            isLoading={isChecking}
            isDisabled={isChecking || !address.trim()}
          >
            Check
          </Button>
        </div>

        {coordinates && (
          <div className="text-xs text-gray-500">
            Coordinates: {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
          </div>
        )}

        {result && (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">Coverage Status:</span>
              <Chip
                color={result.in_service_area ? "success" : "danger"}
                variant="flat"
                size="sm"
              >
                {result.in_service_area ? "Covered" : "Not Covered"}
              </Chip>
            </div>

            {result.in_service_area && result.service_areas.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Matching Service Areas:</h4>
                <div className="space-y-2">
                  {result.service_areas.map((area) => (
                    <div
                      key={area.id}
                      className="p-3 bg-green-50 border border-green-200 rounded-lg"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-green-900">{area.name}</p>
                          <p className="text-sm text-green-700">
                            {area.branch.name} • Priority {area.priority}
                          </p>
                        </div>
                        <Chip
                          size="sm"
                          variant="bordered"
                          className="capitalize"
                        >
                          {area.type}
                        </Chip>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {!result.in_service_area && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">
                  This address is not within any of your current service areas. 
                  Consider expanding your coverage or creating a new service area.
                </p>
              </div>
            )}
          </div>
        )}

        <div className="text-xs text-gray-500 border-t pt-3">
          <p>
            <strong>Note:</strong> This is a demo implementation. In production, 
            this would use real geocoding services and precise geographic calculations.
          </p>
        </div>
      </CardBody>
    </Card>
  );
}
