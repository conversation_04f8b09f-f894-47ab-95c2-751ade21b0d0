"use client";

import React, { useState, useEffect } from "react";
import { Controller } from "react-hook-form";
import Link from "next/link";
import {
  Select,
  SelectItem,
  Button,
  Card,
  CardBody,
  CardHeader,
  Input,
  Switch,
  Chip,
  Tooltip,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab
} from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { Modifier, ModifierType, ProductModifierSettings } from "@/types/modifiers";

interface ProductModifiersProps {
  control: any;
  setValue: any;
  watch: any;
  vendorId: string;
  productId?: string;
  name?: string;
  label?: string;
  description?: string;
  loadModifiers?: () => Promise<Modifier[]>;
}

export default function ProductModifiers({
  control,
  setValue,
  watch,
  vendorId,
  productId,
  name = "available_modifiers",
  label = "Modifier Options",
  description = "Add modifier options that customers can select when ordering this product",
  loadModifiers
}: ProductModifiersProps) {
  const [modifiers, setModifiers] = useState<Modifier[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Watch the available_modifiers field
  const availableModifiers = watch(name) || {};



  // Load modifiers when component mounts
  useEffect(() => {
    const loadModifierOptions = async () => {
      setIsLoading(true);
      setError(null);

      try {
        let modifierData: Modifier[] = [];

        if (loadModifiers) {
          // Use the provided server action
          modifierData = await loadModifiers();
        } else {
          // Fallback to client-side API call
          const response = await fetch('/api/modifiers');
          if (response.ok) {
            modifierData = await response.json();
          } else {
            throw new Error(`Failed to fetch modifiers: ${response.status}`);
          }
        }

        setModifiers(modifierData);
      } catch (err) {
        console.error("Error loading modifiers:", err);
        setError("Failed to load modifier options");
        setModifiers([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadModifierOptions();
  }, [vendorId, loadModifiers]);
  
  // Group modifiers by type
  const modifiersByType = {
    preparation: modifiers.filter(m => m.type === "preparation"),
    condiment: modifiers.filter(m => m.type === "condiment"),
    extra: modifiers.filter(m => m.type === "extra")
  };

  // Get available modifiers (not yet selected)
  const getAvailableModifiers = () => {
    return modifiers.filter(modifier => !availableModifiers[modifier.id]);
  };

  // Get selected modifiers
  const getSelectedModifiers = () => {
    return modifiers.filter(modifier => availableModifiers[modifier.id]);
  };

  const formatPrice = (price: string | number) => {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return `KES ${numPrice.toFixed(2)}`;
  };
  
  // Function to add a modifier to the product
  const addModifier = (modifierId: string) => {
    const modifier = modifiers.find(m => m.id === modifierId);
    if (!modifier) return;

    // Create a new modifier settings object
    const newModifierSettings: ProductModifierSettings = {
      price_adjustment_override: parseFloat(modifier.defaultPriceAdjustment),
      is_default: false,
      sort_order: Object.keys(availableModifiers).length
    };

    // Add the modifier to the available_modifiers field
    setValue(name, {
      ...availableModifiers,
      [modifierId]: newModifierSettings
    }, { shouldValidate: true });
  };

  // Function to remove a modifier from the product
  const removeModifier = (modifierId: string) => {
    const updatedModifiers = { ...availableModifiers };
    delete updatedModifiers[modifierId];
    setValue(name, updatedModifiers, { shouldValidate: true });
  };
  
  // Function to update modifier settings
  const updateModifierSettings = (
    modifierId: string,
    field: keyof ProductModifierSettings,
    value: any
  ) => {
    setValue(name, {
      ...availableModifiers,
      [modifierId]: {
        ...availableModifiers[modifierId],
        [field]: value
      }
    }, { shouldValidate: true });
  };

  // Handle multiple modifier selection
  const handleSelectionChange = (keys: any) => {
    const selectedArray = Array.from(keys) as string[];
    const currentSelected = Object.keys(availableModifiers);

    // Add newly selected modifiers
    selectedArray.forEach((modifierId) => {
      if (!currentSelected.includes(modifierId)) {
        addModifier(modifierId);
      }
    });

    // Remove deselected modifiers
    currentSelected.forEach(modifierId => {
      if (!selectedArray.includes(modifierId)) {
        removeModifier(modifierId);
      }
    });
  };
  
  return (
    <div className="space-y-4">
      {/* Error Alert */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-700 text-sm font-medium">Error loading modifier options</p>
              <p className="text-red-600 text-xs mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}



      <div>
        <div className="flex items-center justify-between mb-2">
          <div>
            <label className="text-sm font-medium text-default-700">
              {label}
            </label>
            {description && (
              <p className="text-xs text-default-500 mt-1">{description}</p>
            )}
          </div>

          <Button
            as={Link}
            href="/products/modifiers/create"
            size="sm"
            variant="light"
            color="primary"
            startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-3 w-3" />}
          >
            Add New
          </Button>
        </div>

        {/* Summary of selected modifiers */}
        {getSelectedModifiers().length > 0 && (
          <div className="mb-3 p-3 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-primary">
                  {getSelectedModifiers().length} modifier option{getSelectedModifiers().length !== 1 ? 's' : ''} selected
                </span>
                {getAvailableModifiers().length === 0 && (
                  <Chip size="sm" variant="flat" color="success" className="text-xs">
                    All selected
                  </Chip>
                )}
              </div>
              <div className="flex items-center gap-2">
                {Object.entries(modifiersByType).map(([type, typeModifiers]) => {
                  const count = typeModifiers.filter(m => availableModifiers[m.id]).length;
                  return count > 0 ? (
                    <Chip key={type} size="sm" variant="flat" color="primary" className="text-xs">
                      {count} {type}
                    </Chip>
                  ) : null;
                })}
              </div>
            </div>
          </div>
        )}

        <Tabs aria-label="Modifier selection options" className="w-full">
          <Tab key="select" title="Quick Select">
            <Controller
              name="modifier_selection"
              control={control}
              render={({ field }) => (
                <Select
                  label={`Select modifier options (${getAvailableModifiers().length} available)`}
                  placeholder={
                    error
                      ? "Error loading options - you can still manage existing selections"
                      : isLoading
                        ? "Loading..."
                        : getAvailableModifiers().length === 0
                          ? "No modifier options available"
                          : `Choose from ${getAvailableModifiers().length} modifier options`
                  }
                  selectionMode="multiple"
                  isLoading={isLoading}
                  isDisabled={isLoading || getAvailableModifiers().length === 0}
                  selectedKeys={new Set(Object.keys(availableModifiers))}
                  onSelectionChange={handleSelectionChange}
                  classNames={{
                    trigger: "min-h-12",
                    value: "text-default-900",
                  }}
                  renderValue={(items) => (
                    <div className="flex flex-wrap gap-2">
                      {items.map((item) => {
                        const modifier = modifiers.find(m => m.id === item.key);
                        return modifier ? (
                          <Chip
                            key={item.key}
                            onClose={() => removeModifier(modifier.id)}
                            variant="flat"
                            color="primary"
                            size="sm"
                          >
                            {modifier.name} ({modifier.type})
                          </Chip>
                        ) : null;
                      })}
                    </div>
                  )}
                >
                  {getAvailableModifiers().map((modifier) => (
                    <SelectItem
                      key={modifier.id}
                      value={modifier.id}
                      textValue={`${modifier.name} - ${modifier.type}`}
                    >
                      <div className="flex items-center justify-between w-full">
                        <div>
                          <p className="font-medium">{modifier.name}</p>
                          <div className="flex items-center gap-2">
                            <Chip size="sm" variant="flat" color="secondary">
                              {modifier.type}
                            </Chip>
                            {modifier.description && (
                              <p className="text-xs text-default-500 line-clamp-1">
                                {modifier.description}
                              </p>
                            )}
                          </div>
                        </div>
                        <span className="text-sm font-medium text-primary">
                          {formatPrice(modifier.defaultPriceAdjustment)}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </Select>
              )}
            />
          </Tab>

          <Tab key="table" title="Browse All">
            <div className="space-y-4">
              <p className="text-sm text-default-600">
                Browse all available modifier options and add them to your product.
              </p>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : modifiers.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-default-500">No modifier options available</p>
                </div>
              ) : (
                <Table aria-label="Available modifier options">
                  <TableHeader>
                    <TableColumn>NAME</TableColumn>
                    <TableColumn>TYPE</TableColumn>
                    <TableColumn>PRICE ADJUSTMENT</TableColumn>
                    <TableColumn>STATUS</TableColumn>
                    <TableColumn>ACTION</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {modifiers.map((modifier) => (
                      <TableRow key={modifier.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium text-default-900">{modifier.name}</p>
                            {modifier.description && (
                              <p className="text-xs text-default-500">{modifier.description}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Chip size="sm" variant="flat" color="secondary" className="capitalize">
                            {modifier.type}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-default-700">
                            {formatPrice(modifier.defaultPriceAdjustment)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            variant="flat"
                            color={modifier.active ? "success" : "danger"}
                          >
                            {modifier.active ? "Active" : "Inactive"}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          {availableModifiers[modifier.id] ? (
                            <Button
                              size="sm"
                              variant="light"
                              color="danger"
                              onPress={() => removeModifier(modifier.id)}
                              startContent={<Icon name="icon-[heroicons--minus-20-solid]" classNames="h-3 w-3" />}
                            >
                              Remove
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="light"
                              color="primary"
                              onPress={() => addModifier(modifier.id)}
                              startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-3 w-3" />}
                            >
                              Add
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </Tab>
        </Tabs>
      </div>

      {/* Selected Modifiers Preview */}
      {getSelectedModifiers().length > 0 && (
        <Card className="bg-default-50">
          <CardHeader>
            <h4 className="text-sm font-medium text-default-700">
              Selected Modifier Options ({getSelectedModifiers().length})
            </h4>
          </CardHeader>
          <CardBody className="space-y-4">
            {Object.entries(modifiersByType).map(([type, typeModifiers]) => {
              const attachedModifiers = typeModifiers.filter(
                modifier => availableModifiers[modifier.id]
              );

              if (attachedModifiers.length === 0) return null;

              return (
                <div key={type} className="space-y-3">
                  <h5 className="text-sm font-medium text-default-800 capitalize">
                    {type} Options ({attachedModifiers.length})
                  </h5>

                  {attachedModifiers.map(modifier => (
                    <Card key={modifier.id} className="bg-white">
                      <CardBody>
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h6 className="font-medium text-default-900">{modifier.name}</h6>
                              <Chip size="sm" variant="flat" color="secondary">
                                {modifier.type}
                              </Chip>
                            </div>
                            {modifier.description && (
                              <p className="text-xs text-default-500 mb-2">
                                {modifier.description}
                              </p>
                            )}
                          </div>

                          <Tooltip content="Remove modifier" color="danger">
                            <Button
                              isIconOnly
                              size="sm"
                              variant="light"
                              color="danger"
                              onPress={() => removeModifier(modifier.id)}
                            >
                              <Icon name="icon-[heroicons--x-mark-20-solid]" classNames="h-4 w-4" />
                            </Button>
                          </Tooltip>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div>
                            <label className="text-xs font-medium text-default-600 mb-1 block">
                              Price Adjustment (cents)
                            </label>
                            <Input
                              type="number"
                              step="1"
                              size="sm"
                              value={availableModifiers[modifier.id]?.price_adjustment_override?.toString() || "0"}
                              onChange={(e) =>
                                updateModifierSettings(
                                  modifier.id,
                                  "price_adjustment_override",
                                  parseInt(e.target.value) || 0
                                )
                              }
                              startContent={
                                <div className="pointer-events-none flex items-center">
                                  <span className="text-default-400 text-small">KES</span>
                                </div>
                              }
                            />
                          </div>

                          <div>
                            <label className="text-xs font-medium text-default-600 mb-1 block">
                              Sort Order
                            </label>
                            <Input
                              type="number"
                              size="sm"
                              value={availableModifiers[modifier.id]?.sort_order?.toString() || "0"}
                              onChange={(e) =>
                                updateModifierSettings(
                                  modifier.id,
                                  "sort_order",
                                  parseInt(e.target.value) || 0
                                )
                              }
                            />
                          </div>

                          <div className="flex items-center">
                            <div className="flex items-center justify-between w-full">
                              <div>
                                <p className="text-xs font-medium text-default-600">Default Selection</p>
                                <p className="text-xs text-default-500">
                                  {availableModifiers[modifier.id]?.is_default
                                    ? "Selected by default"
                                    : "Optional selection"
                                  }
                                </p>
                              </div>
                              <Switch
                                size="sm"
                                isSelected={availableModifiers[modifier.id]?.is_default || false}
                                onValueChange={(checked) =>
                                  updateModifierSettings(
                                    modifier.id,
                                    "is_default",
                                    checked
                                  )
                                }
                                color="primary"
                              />
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              );
            })}

            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-xs text-blue-800">
                <Icon name="icon-[heroicons--information-circle-20-solid]" classNames="h-4 w-4 inline mr-1" />
                These modifier options will be available for customers to select when ordering this product.
                Price adjustments are added to the base product price.
              </p>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Empty State */}
      {!isLoading && modifiers.length === 0 && !error && (
        <Card className="bg-default-50">
          <CardBody className="text-center py-8">
            <Icon name="icon-[heroicons--adjustments-horizontal-20-solid]" classNames="h-12 w-12 text-default-400 mx-auto mb-3" />
            <p className="text-default-600 font-medium mb-2">No modifier options available</p>
            <p className="text-default-500 text-sm mb-4">
              Create modifier options to make them available for your products.
            </p>
            <Button
              as={Link}
              href="/products/modifiers/create"
              color="primary"
              size="sm"
              startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
            >
              Create Modifier Option
            </Button>
          </CardBody>
        </Card>
      )}

      {/* Error State with Action */}
      {!isLoading && modifiers.length === 0 && error && (
        <Card className="bg-orange-50 border-orange-200">
          <CardBody className="text-center py-8">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-12 w-12 text-orange-500 mx-auto mb-3" />
            <p className="text-orange-800 font-medium mb-2">Unable to load modifier options</p>
            <p className="text-orange-700 text-sm mb-4">
              You can still create new modifier options or try refreshing the page.
            </p>
            <div className="flex items-center justify-center gap-3">
              <Button
                color="warning"
                variant="light"
                size="sm"
                onPress={() => window.location.reload()}
                startContent={<Icon name="icon-[heroicons--arrow-path-20-solid]" classNames="h-4 w-4" />}
              >
                Refresh Page
              </Button>
              <Button
                as={Link}
                href="/products/modifiers/create"
                color="primary"
                size="sm"
                startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
              >
                Create New Option
              </Button>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
}