"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { Input, Button, Select, SelectItem, Textarea, Switch } from "@nextui-org/react";
import { api } from "@/lib/api";

interface ServiceOptionFormProps {
  // TODO: Add props for storing/updating service options and handling API calls
}

// Duration interface based on actual API response
interface Duration {
  id: string;
  name: string;
  description?: string;
  minutes: number;
  bufferMinutes: number;
  category: "short" | "medium" | "long" | "full-day";
  maxConcurrent: number;
  allowsBackToBack: boolean;
  requiredBreakAfter: number;
  schedulingRules: {
    minAdvanceHours: number;
    maxPerDay: number;
    timeSlots: string[];
    blackoutDays: string[];
  };
  branchConstraints: {
    respectBranchHours: boolean;
    staffRequired: number;
    equipmentRequired: string[];
  };
  active: boolean;
  createdAt: string;
  updatedAt: string;
  calendarBlockMinutes: number;
  totalHours: number;
  isShortDuration: boolean;
  isMediumDuration: boolean;
  isLongDuration: boolean;
  isFullDay: boolean;
}

// TODO: Replace with real types
interface ServiceOptionPayload {
  name: string;
  type: string;
  description: string;
  defaultPriceAdjustment: number;
  durationId?: string;
  isDefault: boolean;
  sortOrder: number;
  active: boolean;
}

const ServiceOptionForm: React.FC<ServiceOptionFormProps> = ({}) => {
  // State for durations
  const [durations, setDurations] = useState<Duration[]>([]);
  const [isLoadingDurations, setIsLoadingDurations] = useState(false);
  const [durationsError, setDurationsError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    watch,
    reset,
    formState: { errors },
  } = useForm<ServiceOptionPayload>({
    defaultValues: {
      active: true,
      isDefault: false,
      defaultPriceAdjustment: 0,
      sortOrder: 1,
    },
  });

  // Watch the type field to show/hide duration selector
  const selectedType = watch("type");

  // Load durations when component mounts or when type changes to duration
  useEffect(() => {
    if (selectedType === "duration") {
      loadDurations();
    }
  }, [selectedType]);

  const loadDurations = async () => {
    setIsLoadingDurations(true);
    setDurationsError(null);

    try {
      // Use fetch instead of api.get to avoid server-side auth issues
      const response = await fetch('/api/durations', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data?.success && data.data?.data) {
        // Filter only active durations
        const activeDurations = data.data.data.filter((duration: Duration) => duration.active);
        setDurations(activeDurations);
      } else {
        throw new Error("Failed to fetch durations");
      }
    } catch (error) {
      console.error("Error loading durations:", error);
      setDurationsError(`Failed to load duration options: ${error instanceof Error ? error.message : String(error)}`);
      setDurations([]);
    } finally {
      setIsLoadingDurations(false);
    }
  };

  const onSubmit = async (data: ServiceOptionPayload) => {
    setIsSubmitting(true);

    try {
      console.log("Submitting service option:", data);

      // Make POST request to create service option
      const response = await fetch('/api/service-options', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || `HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log("Service option created successfully:", result);

      // Show success message and reset form
      alert("Service option created successfully!");
      reset(); // Reset the form to initial state
      setDurations([]); // Clear durations

    } catch (error) {
      console.error("Error creating service option:", error);
      alert(`Failed to create service option: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <Input
        {...register("name", { required: "Name is required" })}
        label="Service Option Name"
        placeholder="e.g., Deep Clean"
        isInvalid={!!errors.name}
        errorMessage={errors.name?.message}
      />
      
      {/* TODO: Populate with real option types from API */}
      <Controller
        name="type"
        control={control}
        render={({ field }) => (
          <Select
            selectedKeys={field.value ? new Set([field.value]) : new Set()}
            onSelectionChange={(keys) => {
              const selectedValue = Array.from(keys)[0] as string;
              field.onChange(selectedValue);
            }}
            label="Option Type"
            placeholder="Select an option type"
            selectionMode="single"
          >
            <SelectItem key="duration" value="duration">Duration</SelectItem>
            <SelectItem key="add_on" value="add_on">Add-on</SelectItem>
            <SelectItem key="personnel" value="personnel">Personnel</SelectItem>
          </Select>
        )}
      />

      <Textarea
        {...register("description")}
        label="Description"
        placeholder="Describe the service option"
      />

      <Input
        {...register("defaultPriceAdjustment", { valueAsNumber: true })}
        type="number"
        label="Default Price Adjustment"
        placeholder="0.00"
        step="0.01"
      />

      <Input
        {...register("sortOrder", { valueAsNumber: true })}
        type="number"
        label="Sort Order"
        placeholder="1"
        min="0"
      />

      {/* Duration selector - only show when type is 'duration' */}
      {selectedType === "duration" && (
        <Controller
          name="durationId"
          control={control}
          rules={{
            required: selectedType === "duration" ? "Duration is required when type is duration" : false
          }}
          render={({ field }) => {
            const selectedDuration = durations.find(d => d.id === field.value);

            return (
              <Select
                {...field}
                selectedKeys={field.value ? new Set([field.value]) : new Set()}
                onSelectionChange={(keys) => {
                  const selectedValue = Array.from(keys)[0] as string;
                  field.onChange(selectedValue);
                }}
                label="Duration Template"
                placeholder={
                  isLoadingDurations
                    ? "Loading durations..."
                    : "Select a duration template"
                }
                isLoading={isLoadingDurations}
                isInvalid={!!errors.durationId}
                errorMessage={errors.durationId?.message || durationsError}
                isDisabled={isLoadingDurations || durations.length === 0}
                selectionMode="single"
                // Force display of selected value
                renderValue={() => {
                  if (selectedDuration) {
                    return `${selectedDuration.name} (${selectedDuration.minutes} min)`;
                  }
                  return "";
                }}
              >
                {durations.map((duration) => (
                  <SelectItem key={duration.id} value={duration.id}>
                    {duration.name} ({duration.minutes} min)
                    {duration.description && ` - ${duration.description}`}
                  </SelectItem>
                ))}
              </Select>
            );
          }}
        />
      )}

      <div className="flex items-center justify-between">
        <label>Is Default</label>
        <Controller
          name="isDefault"
          control={control}
          render={({ field }) => (
            <Switch 
              isSelected={field.value} 
              onValueChange={field.onChange}
            />
          )}
        />
      </div>

      <div className="flex items-center justify-between">
        <label>Active</label>
        <Controller
          name="active"
          control={control}
          render={({ field }) => (
            <Switch 
              isSelected={field.value} 
              onValueChange={field.onChange}
            />
          )}
        />
      </div>
      
      <Button
        type="submit"
        color="primary"
        isLoading={isSubmitting}
        isDisabled={isSubmitting}
      >
        {isSubmitting ? "Creating..." : "Create Service Option"}
      </Button>
    </form>
  );
};

export default ServiceOptionForm; 