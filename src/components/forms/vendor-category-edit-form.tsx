"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>ooter,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Icon } from "@/components/icon";
import { useState } from "react";

interface VendorCategoryEditFormProps {
  category: VendorCategory;
  updateVendorCategory: (data: FormData) => Promise<void>;
}

export default function VendorCategoryEditForm({
  category,
  updateVendorCategory,
}: VendorCategoryEditFormProps) {
  const [name, setName] = useState(category.name);
  const [details, setDetails] = useState(category.details);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData();
    formData.append("id", category.id);
    formData.append("name", name);
    formData.append("details", details);
    updateVendorCategory(formData);
  };

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="outline" size={"icon"}>
          <Icon
            name="icon-[mage--edit-pen-fill]"
            classNames="text-primary"
          />
        </Button>
      </SheetTrigger>
      <SheetContent>
        <SheetHeader>
          <SheetTitle className="text-primary">
            Edit vendor category
          </SheetTitle>
          <SheetDescription className="text-primary">
            Enter details below.
          </SheetDescription>
        </SheetHeader>
        <form onSubmit={handleSubmit} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="details" className="text-right">
              Description
            </Label>
            <Input
              id="details"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              className="col-span-3"
            />
          </div>
          <SheetFooter>
            <SheetClose asChild>
              <Button type="submit" className="bg-primary">
                Save changes
              </Button>
            </SheetClose>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
} 