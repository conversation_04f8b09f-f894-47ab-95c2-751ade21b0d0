"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { Button, Input, Textarea, Switch, Card, CardBody, CardHeader } from "@nextui-org/react";
import toast from "react-hot-toast";
import { 
  PackagingOption, 
  CreatePackagingOptionRequest, 
  UpdatePackagingOptionRequest,
  createPackagingOption,
  updatePackagingOption 
} from "@/actions/packaging-options";

interface PackagingOptionFormProps {
  defaultValues?: PackagingOption;
  mode: "create" | "edit";
}

type FormData = CreatePackagingOptionRequest & { id?: string };

export default function PackagingOptionForm({ defaultValues, mode }: PackagingOptionFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid },
  } = useForm<FormData>({
    defaultValues: {
      name: defaultValues?.name || "",
      description: defaultValues?.description || "",
      price: defaultValues ? parseFloat(defaultValues.price) : 0,
      active: defaultValues?.active ?? true,
      id: defaultValues?.id,
    },
    mode: "onChange",
  });

  const watchedActive = watch("active");

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    
    try {
      const payload = {
        name: data.name.trim(),
        description: data.description?.trim() || undefined,
        price: data.price,
        active: data.active,
      };

      if (mode === "create") {
        await createPackagingOption(payload);
        toast.success("Packaging option created successfully!");
      } else if (mode === "edit" && defaultValues?.id) {
        await updatePackagingOption(defaultValues.id, payload);
        toast.success("Packaging option updated successfully!");
      }

      router.push("/products/packaging-options");
      router.refresh();
    } catch (error: any) {
      console.error("Error submitting form:", error);
      
      // Handle specific API errors
      if (error.response?.data?.errors) {
        const apiErrors = error.response.data.errors;
        Object.keys(apiErrors).forEach(field => {
          toast.error(`${field}: ${apiErrors[field].join(", ")}`);
        });
      } else if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error(`Failed to ${mode} packaging option. Please try again.`);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/products/packaging-options");
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <Card>
        <CardHeader className="pb-4">
          <div>
            <h1 className="text-2xl font-bold text-default-900">
              {mode === "create" ? "Create Packaging Option" : "Edit Packaging Option"}
            </h1>
            <p className="text-default-600 mt-1">
              {mode === "create" 
                ? "Add a new packaging option that can be associated with your products."
                : "Update the packaging option details."
              }
            </p>
          </div>
        </CardHeader>
        
        <CardBody>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Field */}
            <Input
              label="Name"
              placeholder="Enter packaging option name (e.g., Gift Wrapping, Standard Box)"
              isRequired
              isInvalid={!!errors.name}
              errorMessage={errors.name?.message}
              {...register("name", {
                required: "Name is required",
                minLength: {
                  value: 2,
                  message: "Name must be at least 2 characters long"
                },
                maxLength: {
                  value: 100,
                  message: "Name must not exceed 100 characters"
                },
                pattern: {
                  value: /^[a-zA-Z0-9\s\-_&()]+$/,
                  message: "Name contains invalid characters"
                }
              })}
              classNames={{
                input: "text-default-900",
                label: "text-default-700"
              }}
            />

            {/* Description Field */}
            <Textarea
              label="Description"
              placeholder="Enter a detailed description of the packaging option (optional)"
              maxRows={4}
              isInvalid={!!errors.description}
              errorMessage={errors.description?.message}
              {...register("description", {
                maxLength: {
                  value: 500,
                  message: "Description must not exceed 500 characters"
                }
              })}
              classNames={{
                input: "text-default-900",
                label: "text-default-700"
              }}
            />

            {/* Price Field */}
            <Input
              label="Price"
              placeholder="0.00"
              type="number"
              step="0.01"
              min="0"
              max="9999.99"
              isRequired
              isInvalid={!!errors.price}
              errorMessage={errors.price?.message}
              startContent={
                <div className="pointer-events-none flex items-center">
                  <span className="text-default-400 text-small">KES</span>
                </div>
              }
              {...register("price", {
                required: "Price is required",
                min: {
                  value: 0,
                  message: "Price must be 0 or greater"
                },
                max: {
                  value: 9999.99,
                  message: "Price must not exceed KES 9,999.99"
                },
                validate: (value) => {
                  if (isNaN(value)) return "Price must be a valid number";
                  if (value < 0) return "Price cannot be negative";
                  return true;
                }
              })}
              classNames={{
                input: "text-default-900",
                label: "text-default-700"
              }}
            />

            {/* Active Status Switch */}
            <div className="flex items-center justify-between p-4 bg-default-50 rounded-lg">
              <div>
                <p className="font-medium text-default-900">Active Status</p>
                <p className="text-sm text-default-600">
                  {watchedActive 
                    ? "This packaging option is active and can be selected for products"
                    : "This packaging option is inactive and won't be available for selection"
                  }
                </p>
              </div>
              <Switch
                isSelected={watchedActive}
                onValueChange={(value) => setValue("active", value)}
                color="primary"
                size="lg"
              />
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-default-200">
              <Button
                variant="light"
                onPress={handleCancel}
                isDisabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                color="primary"
                isLoading={isSubmitting}
                isDisabled={!isValid}
              >
                {isSubmitting 
                  ? (mode === "create" ? "Creating..." : "Updating...")
                  : (mode === "create" ? "Create Packaging Option" : "Update Packaging Option")
                }
              </Button>
            </div>
          </form>
        </CardBody>
      </Card>
    </div>
  );
}
