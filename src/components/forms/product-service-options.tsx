import React, { useState } from "react";
import { Control, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { Card, CardBody, Button, Chip } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import Link from "next/link";

interface ServiceOption {
  id: string;
  name: string;
  description?: string;
  priceAdjustment?: number;
}

interface ProductServiceOptionsProps {
  control: Control<any>;
  setValue: UseFormSetValue<any>;
  watch: UseFormWatch<any>;
  vendorId: string;
  name?: string;
  label?: string;
  description?: string;
}

const mockServiceOptions: ServiceOption[] = [
  { id: "opt-1", name: "Deep Clean", description: "Comprehensive cleaning", priceAdjustment: 75 },
  { id: "opt-2", name: "Express", description: "Quick service", priceAdjustment: 30 },
  { id: "opt-3", name: "Pet Treatment", description: "Pet odor removal", priceAdjustment: 35 },
];

const ProductServiceOptions: React.FC<ProductServiceOptionsProps> = ({
  control,
  setValue,
  watch,
  vendorId,
  name = "serviceOptionIds",
  label = "Service Options",
  description = "Assign service options that customers can select for this service product",
}) => {
  // TODO: Replace with real API fetch
  const [serviceOptions] = useState<ServiceOption[]>(mockServiceOptions);
  const [isLoading] = useState(false);
  const [error] = useState<string | null>(null);

  // Watch selected service option IDs
  const selectedIds = Array.isArray(watch(name)) ? watch(name) : [];

  const getSelectedOptions = () => {
    return serviceOptions.filter(option => selectedIds.includes(option.id));
  };

  const handleSelectionChange = (keys: any) => {
    const selectedArray = Array.from(keys);
    setValue(name, selectedArray, { shouldValidate: true });
  };

  const removeOption = (optionId: string) => {
    const newSelection = selectedIds.filter((id: string) => id !== optionId);
    setValue(name, newSelection, { shouldValidate: true });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between mb-2">
        <div>
          <label className="text-sm font-medium text-default-700">
            {label}
          </label>
          {description && (
            <p className="text-xs text-default-500 mt-1">{description}</p>
          )}
        </div>
        <Button
          as={Link}
          href="/products/service-options/create"
          size="sm"
          variant="light"
          color="primary"
        >
          Add New
        </Button>
      </div>

      {/* TODO: Replace with real Select component for service options */}
      <div>
        <div className="flex flex-wrap gap-2 mb-2">
          {serviceOptions.map((option) => (
            <Chip
              key={option.id}
              color={selectedIds.includes(option.id) ? "primary" : "default"}
              variant={selectedIds.includes(option.id) ? "solid" : "flat"}
              onClick={() => {
                if (selectedIds.includes(option.id)) {
                  removeOption(option.id);
                } else {
                  setValue(name, [...selectedIds, option.id], { shouldValidate: true });
                }
              }}
              className="cursor-pointer"
            >
              {option.name}
            </Chip>
          ))}
        </div>
      </div>

      {/* Selected Options Preview */}
      {selectedIds.length > 0 && (
        <Card className="bg-default-50">
          <CardBody>
            <h4 className="text-sm font-medium text-default-700 mb-3">
              Selected Service Options ({selectedIds.length})
            </h4>
            <div className="space-y-2">
              {getSelectedOptions().map((option) => (
                <div
                  key={option.id}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border border-default-200"
                >
                  <div className="flex-1">
                    <p className="font-medium text-default-900">{option.name}</p>
                    {option.description && (
                      <p className="text-xs text-default-500 mt-1 line-clamp-2">
                        {option.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-3">
                    {option.priceAdjustment !== undefined && (
                      <span className="text-sm font-medium text-primary">
                        KES {option.priceAdjustment.toFixed(2)}
                      </span>
                    )}
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      color="danger"
                      onPress={() => removeOption(option.id)}
                    >
                      <Icon name="icon-[heroicons--x-mark-20-solid]" classNames="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-xs text-blue-800">
                <Icon name="icon-[heroicons--information-circle-20-solid]" classNames="h-4 w-4 inline mr-1" />
                These service options will be available for customers to select when booking this service product.
              </p>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Empty State */}
      {!isLoading && serviceOptions.length === 0 && !error && (
        <Card className="bg-default-50">
          <CardBody className="text-center py-8">
            <Icon name="icon-[heroicons--cube-20-solid]" classNames="h-12 w-12 text-default-400 mx-auto mb-3" />
            <p className="text-default-600 font-medium mb-2">No service options available</p>
            <p className="text-default-500 text-sm mb-4">
              Create service options to make them available for your products.
            </p>
            <Button
              color="primary"
              size="sm"
              startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
              // TODO: Implement add new service option action
            >
              Create Service Option
            </Button>
          </CardBody>
        </Card>
      )}

      {/* Error State with Action */}
      {!isLoading && serviceOptions.length === 0 && error && (
        <Card className="bg-orange-50 border-orange-200">
          <CardBody className="text-center py-8">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-12 w-12 text-orange-500 mx-auto mb-3" />
            <p className="text-orange-800 font-medium mb-2">Unable to load service options</p>
            <p className="text-orange-700 text-sm mb-4">
              You can still create new service options or try refreshing the page.
            </p>
            <div className="flex items-center justify-center gap-3">
              <Button
                color="warning"
                variant="light"
                size="sm"
                onPress={() => window.location.reload()}
                startContent={<Icon name="icon-[heroicons--arrow-path-20-solid]" classNames="h-4 w-4" />}
              >
                Refresh Page
              </Button>
              <Button
                color="primary"
                size="sm"
                startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
                // TODO: Implement add new service option action
              >
                Create New Option
              </Button>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default ProductServiceOptions; 