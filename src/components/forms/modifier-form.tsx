"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button, Input, Select, SelectItem, Switch, Textarea } from "@nextui-org/react";
import { Modifier, ModifierType } from "@/types/modifiers";

interface ModifierFormProps {
  modifier?: Modifier;
  onSubmit: (data: Partial<Modifier>) => Promise<void>;
  isSubmitting?: boolean;
}

export default function ModifierForm({ modifier, onSubmit, isSubmitting = false }: ModifierFormProps) {
  const router = useRouter();
  const [formData, setFormData] = useState<Partial<Modifier>>({
    name: modifier?.name || "",
    type: modifier?.type || "preparation",
    description: modifier?.description || "",
    defaultPriceAdjustment: modifier?.defaultPriceAdjustment || "0",
    active: modifier?.active ?? true,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (checked: boolean) => {
    setFormData((prev) => ({ ...prev, active: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert the form data to match the API's expected format
    const apiData = {
      name: formData.name,
      type: formData.type,
      description: formData.description,
      default_price_adjustment: formData.defaultPriceAdjustment,
      active: formData.active
    };
    
    await onSubmit(apiData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <Input
          label="Name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          isRequired
          placeholder="Enter modifier name"
          variant="bordered"
          classNames={{
            label: "text-black/50 dark:text-white/90",
            input: [
              "bg-transparent",
              "text-black/90 dark:text-white/90",
              "placeholder:text-default-700/50 dark:placeholder:text-white/60",
            ],
            innerWrapper: "bg-transparent",
          }}
        />

        <Select
          label="Type"
          name="type"
          selectedKeys={[formData.type as ModifierType]}
          onChange={handleSelectChange}
          isRequired
          variant="bordered"
          classNames={{
            label: "text-black/50 dark:text-white/90",
            value: "text-black/90 dark:text-white/90",
            trigger: "bg-transparent",
          }}
        >
          <SelectItem key="preparation" value="preparation">
            Preparation
          </SelectItem>
          <SelectItem key="condiment" value="condiment">
            Condiment
          </SelectItem>
          <SelectItem key="extra" value="extra">
            Extra
          </SelectItem>
        </Select>

        <Textarea
          label="Description"
          name="description"
          value={formData.description || ""}
          onChange={handleChange}
          placeholder="Enter modifier description"
          variant="bordered"
          classNames={{
            label: "text-black/50 dark:text-white/90",
            input: [
              "bg-transparent",
              "text-black/90 dark:text-white/90",
              "placeholder:text-default-700/50 dark:placeholder:text-white/60",
            ],
            innerWrapper: "bg-transparent",
          }}
        />

        <Input
          label="Price Adjustment"
          name="defaultPriceAdjustment"
          value={formData.defaultPriceAdjustment}
          onChange={handleChange}
          type="number"
          step="0.01"
          placeholder="Enter price adjustment"
          variant="bordered"
          classNames={{
            label: "text-black/50 dark:text-white/90",
            input: [
              "bg-transparent",
              "text-black/90 dark:text-white/90",
              "placeholder:text-default-700/50 dark:placeholder:text-white/60",
            ],
            innerWrapper: "bg-transparent",
          }}
        />

        <div className="flex items-center space-x-2">
          <Switch
            isSelected={formData.active}
            onValueChange={handleSwitchChange}
            color="success"
          />
          <span>Active</span>
        </div>
      </div>

      <div className="flex justify-end space-x-4">
        <Button
          color="danger"
          variant="flat"
          onPress={() => router.push("/products/modifiers")}
          isDisabled={isSubmitting}
        >
          Cancel
        </Button>
        <Button
          color="primary"
          type="submit"
          isLoading={isSubmitting}
          isDisabled={!formData.name || !formData.type}
        >
          {modifier ? "Update Modifier" : "Create Modifier"}
        </Button>
      </div>
    </form>
  );
} 