"use client";

import React, { useState, useEffect } from "react";
import { Controller } from "react-hook-form";
import {
  Select,
  SelectItem,
  Chip,
  Card,
  CardBody,
  Button,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Tabs,
  Tab
} from "@nextui-org/react";
import Link from "next/link";
import { Icon } from "@/components/icon";
import { PackagingOption, getPackagingOptions } from "@/actions/packaging-options";

interface PackagingOptionSelectorProps {
  control: any;
  setValue: any;
  watch: any;
  vendorId?: string;
  name?: string;
  label?: string;
  description?: string;
  isRequired?: boolean;
  loadPackagingOptions?: () => Promise<PackagingOption[]>;
}

export default function PackagingOptionSelector({
  control,
  setValue,
  watch,
  vendorId,
  name = "packagingOptionIds",
  label = "Packaging Options",
  description = "Select packaging options that will be available for this product",
  isRequired = false,
  loadPackagingOptions,
}: PackagingOptionSelectorProps) {
  const [packagingOptions, setPackagingOptions] = useState<PackagingOption[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const selectedIds = watch(name) || [];

  // Load packaging options
  useEffect(() => {
    const loadOptions = async () => {
      setIsLoading(true);
      setError(null);

      try {
        let options: PackagingOption[] = [];

        if (loadPackagingOptions) {
          // Use the provided server action
          options = await loadPackagingOptions();
        } else {
          // Fallback to direct API call
          const response = await getPackagingOptions({
            active: "true",
            per: "100" // Get all active options
          });
          options = response?.data || [];
        }

        setPackagingOptions(options);
      } catch (err) {
        console.error("Error loading packaging options:", err);
        setError("Failed to load packaging options");
        setPackagingOptions([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadOptions();
  }, [vendorId, loadPackagingOptions]);

  const formatPrice = (price: string | number) => {
    return `KES ${parseFloat(price.toString()).toFixed(2)}`;
  };

  const getSelectedOptions = () => {
    return packagingOptions.filter(option => selectedIds.includes(option.id));
  };

  const handleSelectionChange = (keys: any) => {
    const selectedArray = Array.from(keys);
    setValue(name, selectedArray, { shouldValidate: true });
  };

  const removeOption = (optionId: string) => {
    const newSelection = selectedIds.filter((id: string) => id !== optionId);
    setValue(name, newSelection, { shouldValidate: true });
  };

  // Don't block the entire component on error - show error inline instead

  return (
    <div className="space-y-4">
      {/* Error Alert */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-2">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-red-700 text-sm font-medium">Error loading packaging options</p>
              <p className="text-red-600 text-xs mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div>
        <div className="flex items-center justify-between mb-2">
          <div>
            <label className="text-sm font-medium text-default-700">
              {label}
              {isRequired && <span className="text-danger-500 ml-1">*</span>}
            </label>
            {description && (
              <p className="text-xs text-default-500 mt-1">{description}</p>
            )}
          </div>
          
          <Button
            as={Link}
            href="/products/packaging-options/create"
            size="sm"
            variant="light"
            color="primary"
            startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-3 w-3" />}
          >
            Add New
          </Button>
        </div>

        <Tabs aria-label="Packaging option selection" className="w-full">
          <Tab key="select" title="Quick Select">
            <Controller
              name={name}
              control={control}
              rules={isRequired ? { required: "Please select at least one packaging option" } : {}}
              render={({ field, fieldState }) => (
                <div>
                  <Select
                    {...field}
                    label="Select packaging options"
                    placeholder={
                      error
                        ? "Error loading options - you can still manage existing selections"
                        : isLoading
                          ? "Loading..."
                          : "Choose packaging options"
                    }
                    selectionMode="multiple"
                    isLoading={isLoading}
                    isDisabled={isLoading}
                    selectedKeys={new Set(selectedIds)}
                    onSelectionChange={handleSelectionChange}
                    isInvalid={!!fieldState.error}
                    errorMessage={fieldState.error?.message}
                    classNames={{
                      trigger: "min-h-12",
                      value: "text-default-900",
                    }}
                    renderValue={(items) => (
                      <div className="flex flex-wrap gap-2">
                        {items.map((item) => {
                          const option = packagingOptions.find(opt => opt.id === item.key);
                          return option ? (
                            <Chip
                              key={item.key}
                              onClose={() => removeOption(option.id)}
                              variant="flat"
                              color="primary"
                              size="sm"
                            >
                              {option.name} ({formatPrice(option.price)})
                            </Chip>
                          ) : null;
                        })}
                      </div>
                    )}
                  >
                    {packagingOptions.map((option) => (
                      <SelectItem
                        key={option.id}
                        value={option.id}
                        textValue={`${option.name} - ${formatPrice(option.price)}`}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div>
                            <p className="font-medium">{option.name}</p>
                            {option.description && (
                              <p className="text-xs text-default-500 line-clamp-1">
                                {option.description}
                              </p>
                            )}
                          </div>
                          <span className="text-sm font-medium text-primary">
                            {formatPrice(option.price)}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </Select>
                </div>
              )}
            />
          </Tab>

          <Tab key="table" title="Browse All">
            <div className="space-y-4">
              <p className="text-sm text-default-600">
                Browse all available packaging options and add them to your product.
              </p>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : packagingOptions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-default-500">No packaging options available</p>
                </div>
              ) : (
                <Table aria-label="Available packaging options">
                  <TableHeader>
                    <TableColumn>NAME</TableColumn>
                    <TableColumn>ADDITIONAL COST</TableColumn>
                    <TableColumn>STATUS</TableColumn>
                    <TableColumn>ACTION</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {packagingOptions.map((option) => (
                      <TableRow key={option.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium text-default-900">{option.name}</p>
                            {option.description && (
                              <p className="text-xs text-default-500">{option.description}</p>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-default-700">
                            {formatPrice(option.price)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Chip
                            size="sm"
                            variant="flat"
                            color={option.active ? "success" : "danger"}
                          >
                            {option.active ? "Active" : "Inactive"}
                          </Chip>
                        </TableCell>
                        <TableCell>
                          {selectedIds.includes(option.id) ? (
                            <Button
                              size="sm"
                              variant="light"
                              color="danger"
                              onPress={() => removeOption(option.id)}
                              startContent={<Icon name="icon-[heroicons--minus-20-solid]" classNames="h-3 w-3" />}
                            >
                              Remove
                            </Button>
                          ) : (
                            <Button
                              size="sm"
                              variant="light"
                              color="primary"
                              onPress={() => {
                                const newSelection = [...selectedIds, option.id];
                                setValue(name, newSelection, { shouldValidate: true });
                              }}
                              startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-3 w-3" />}
                            >
                              Add
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </div>
          </Tab>
        </Tabs>
      </div>

      {/* Selected Options Preview */}
      {selectedIds.length > 0 && (
        <Card className="bg-default-50">
          <CardBody>
            <h4 className="text-sm font-medium text-default-700 mb-3">
              Selected Packaging Options ({selectedIds.length})
            </h4>
            <div className="space-y-2">
              {getSelectedOptions().map((option) => (
                <div
                  key={option.id}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border border-default-200"
                >
                  <div className="flex-1">
                    <p className="font-medium text-default-900">{option.name}</p>
                    {option.description && (
                      <p className="text-xs text-default-500 mt-1 line-clamp-2">
                        {option.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-sm font-medium text-primary">
                      {formatPrice(option.price)}
                    </span>
                    <Button
                      isIconOnly
                      size="sm"
                      variant="light"
                      color="danger"
                      onPress={() => removeOption(option.id)}
                    >
                      <Icon name="icon-[heroicons--x-mark-20-solid]" classNames="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-xs text-blue-800">
                <Icon name="icon-[heroicons--information-circle-20-solid]" classNames="h-4 w-4 inline mr-1" />
                These packaging options will be available when customers order this product for 
                Takeaway, Delivery, or Self-pickup (not for Dine-in orders).
              </p>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Empty State */}
      {!isLoading && packagingOptions.length === 0 && !error && (
        <Card className="bg-default-50">
          <CardBody className="text-center py-8">
            <Icon name="icon-[heroicons--cube-20-solid]" classNames="h-12 w-12 text-default-400 mx-auto mb-3" />
            <p className="text-default-600 font-medium mb-2">No packaging options available</p>
            <p className="text-default-500 text-sm mb-4">
              Create packaging options to make them available for your products.
            </p>
            <Button
              as={Link}
              href="/products/packaging-options/create"
              color="primary"
              size="sm"
              startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
            >
              Create Packaging Option
            </Button>
          </CardBody>
        </Card>
      )}

      {/* Error State with Action */}
      {!isLoading && packagingOptions.length === 0 && error && (
        <Card className="bg-orange-50 border-orange-200">
          <CardBody className="text-center py-8">
            <Icon name="icon-[heroicons--exclamation-triangle-20-solid]" classNames="h-12 w-12 text-orange-500 mx-auto mb-3" />
            <p className="text-orange-800 font-medium mb-2">Unable to load packaging options</p>
            <p className="text-orange-700 text-sm mb-4">
              You can still create new packaging options or try refreshing the page.
            </p>
            <div className="flex items-center justify-center gap-3">
              <Button
                color="warning"
                variant="light"
                size="sm"
                onPress={() => window.location.reload()}
                startContent={<Icon name="icon-[heroicons--arrow-path-20-solid]" classNames="h-4 w-4" />}
              >
                Refresh Page
              </Button>
              <Button
                as={Link}
                href="/products/packaging-options/create"
                color="primary"
                size="sm"
                startContent={<Icon name="icon-[heroicons--plus-20-solid]" classNames="h-4 w-4" />}
              >
                Create New Option
              </Button>
            </div>
          </CardBody>
        </Card>
      )}
    </div>
  );
}
