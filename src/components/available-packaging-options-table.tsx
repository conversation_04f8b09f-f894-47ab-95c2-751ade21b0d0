"use client";

import { PackagingOption } from "@/actions/packaging-options";
import { CustomTable } from "./custom-table";
import { Chip } from "@nextui-org/react";
import Link from "next/link";

interface AvailablePackagingOptionsTableProps {
  packagingOptions: PackagingOption[];
  productId: string;
}

export function AvailablePackagingOptionsTable({ packagingOptions, productId }: AvailablePackagingOptionsTableProps) {
  const columns = [
    {
      name: "Name",
      uid: "name",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <div className="flex flex-col">
          <span className="font-medium text-default-800">{option.name}</span>
          {option.description && (
            <span className="text-sm text-default-500">{option.description}</span>
          )}
        </div>
      ),
    },
    {
      name: "Additional Cost",
      uid: "price",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <span className="font-medium text-default-700">
          KES {parseFloat(option.price).toFixed(2)}
        </span>
      ),
    },
    {
      name: "Status",
      uid: "active",
      sortable: true,
      renderCell: (option: PackagingOption) => (
        <Chip
          size="sm"
          variant="flat"
          color={option.active ? "success" : "danger"}
        >
          {option.active ? "Active" : "Inactive"}
        </Chip>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-semibold text-default-800">Available Packaging Options</h1>
        <Link
          href={`/products/${productId}/edit?tab=extra`}
          className="rounded-lg bg-primary px-4 py-2 text-sm text-white"
        >
          Edit Packaging
        </Link>
      </div>
      
      <CustomTable
        title="Available Packaging Options"
        data={packagingOptions}
        columns={columns}
        isDashboard={true}
      />
    </div>
  );
}
