"use client";

import { format } from "date-fns";
import { Icon } from "../icon";
import { CustomTable } from "../custom-table";
import { Button } from "@nextui-org/react";
import { deleteMessage } from "@/app/(authenticated)/messages/actions";
import toast from "react-hot-toast";

interface Message {
  id: string;
  template?: {
    content: string;
    name: string;
  };
  author?: {
    name: string;
  };
  createdAt: string;
}

interface MessagesTableProps {
  data: Message[];
  meta: PaginationMeta;
}

export default function MessagesTable({ data, meta }: MessagesTableProps) {
  const onDeleteSubmit = async (id: string) => {
    const formData = new FormData();
    formData.append("id", id);

    toast.promise(deleteMessage(formData), {
      loading: "Deleting message...",
      success: "Message deleted!",
      error: "Failed to delete message",
    });
  };

  return (
    <CustomTable
      title="Messages"
      columns={[
        {
          name: "Content",
          uid: "details",
          renderCell: (message: Message) => <>{message.template?.content}</>,
        },
        {
          name: "Author",
          uid: "author",
          renderCell: (message: Message) => <>{message.author?.name}</>,
        },
        {
          name: "Template",
          uid: "template.name",
          renderCell: (message: Message) => <>{message.template?.name}</>,
        },
        {
          name: "Sent",
          uid: "createdAt",
          sortable: true,
          renderCell: (message: Message) => (
            <span className="text-default-700">
              {format(new Date(message.createdAt), "EEE, MMM dd, yyyy")}
            </span>
          ),
        },
        {
          name: "Actions",
          uid: "actions",
          renderCell: (message: Message) => (
            <div className="flex w-fit items-center gap-5">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                color="danger"
                onPress={() => onDeleteSubmit(message.id)}
              >
                <Icon name="icon-[heroicons--trash-20-solid]" />
              </Button>
            </div>
          ),
        },
      ]}
      data={data}
      meta={meta}
    />
  );
} 