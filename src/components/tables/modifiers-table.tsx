"use client";

import Link from "next/link";
import { Chip } from "@nextui-org/react";
import { Session } from "next-auth";
import toast from "react-hot-toast";

import { CustomTable } from "../custom-table";
import { Icon } from "../icon";
import { Modifier } from "@/types/modifiers";
import { deleteModifier, updateModifier } from "@/actions/modifiers";

export default function ModifiersTable({
  data,
  meta,
  session,
}: {
  data: Modifier[];
  meta: PaginationMeta;
  session: Session | null;
}) {
  const onDeleteSubmit = async (id: string) => {
    toast.promise(deleteModifier(id), {
      loading: "Deleting modifier...",
      success: "Modifier deleted!",
      error: "Failed to delete modifier",
    });
  };

  const setActive = (modifier: Modifier, active: boolean) => {
    const updateData = {
      name: modifier.name,
      type: modifier.type,
      description: modifier.description || undefined,
      default_price_adjustment: Number(modifier.defaultPriceAdjustment),
      active: active
    };
    
    toast.promise(
      updateModifier(modifier.id, updateData),
      {
        loading: "Updating modifier...",
        success: "Modifier updated!",
        error: "Failed to update modifier",
      },
    );
  };

  const columns = [
    {
      uid: "name",
      name: "Name",
      renderCell: (modifier: Modifier) => (
        <Link href={`/products/modifiers/${modifier.id}`} className="flex items-center">
          <div className="ms-2">
            <p className="tb-lead">{modifier.name}</p>
            <span className="text-sm text-default-500">{modifier.description || "No description"}</span>
          </div>
        </Link>
      ),
    },

    {
      uid: "type",
      name: "Type",
      renderCell: (modifier: Modifier) => (
        <Chip
          color={
            modifier.type === "preparation"
              ? "primary"
              : modifier.type === "condiment"
                ? "secondary"
                : "success"
          }
          variant="flat"
          className="text-xs"
        >
          {modifier.type}
        </Chip>
      ),
      sortable: true,
    },

    {
      uid: "defaultPriceAdjustment",
      name: "Price Adjustment",
      renderCell: (modifier: Modifier) => (
        <span className={parseFloat(modifier.defaultPriceAdjustment) >= 0 ? "text-success" : "text-danger"}>
          {parseFloat(modifier.defaultPriceAdjustment) >= 0 ? "+" : ""}KES {modifier.defaultPriceAdjustment}
        </span>
      ),
      sortable: true,
    },

    {
      uid: "active",
      name: "Status",
      renderCell: (modifier: Modifier) => (
        <Chip
          color={modifier.active ? "success" : "danger"}
          variant="flat"
          className="text-xs"
        >
          {modifier.active ? "Active" : "Inactive"}
        </Chip>
      ),
    },

    {
      name: "Actions",
      uid: "actions",
      renderCell: (modifier: Modifier) => (
        <div className="flex w-fit items-center gap-5">
          <Link href={`/products/modifiers/${modifier.id}/edit`}>
            <Icon name="icon-[mage--edit-pen-fill]" classNames="text-primary" />
          </Link>

          <form
            onSubmit={(e) => {
              e.preventDefault();
              onDeleteSubmit(modifier.id);
            }}
          >
            <button
              name="id"
              value={modifier.id}
              type="submit"
              className="text-red-500"
            >
              <Icon name="icon-[mingcute--delete-2-line]" />
            </button>
          </form>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      title="Modifier Options"
      columns={columns}
      data={data}
      meta={meta}
      filter={
        session?.vendor?.id
          ? []
          : [
              {
                column: "type",
                displayName: "Type",
                values: [
                  { name: "Preparation", value: "preparation" },
                  { name: "Condiment", value: "condiment" },
                  { name: "Extra", value: "extra" },
                ],
              },
              {
                column: "active",
                displayName: "Status",
                values: [
                  { name: "Active", value: "true" },
                  { name: "Inactive", value: "false" },
                ],
              },
            ]
      }
      action={
        <Link
          className="flex items-center space-x-3 rounded-lg bg-primary px-6 py-2 text-white"
          href="/products/modifiers/create"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth={1.5}
            stroke="currentColor"
            className="h-4 w-4"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M12 4.5v15m7.5-7.5h-15"
            />
          </svg>
          <span className="s-only">Add Modifier</span>
        </Link>
      }
    />
  );
} 