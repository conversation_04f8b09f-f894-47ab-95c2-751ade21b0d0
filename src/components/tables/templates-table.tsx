"use client";

import { But<PERSON> } from "@nextui-org/react";
import { Icon } from "@/components/icon";
import { useRouter } from "next/navigation";
import { CustomTable } from "@/components/custom-table";
import toast from "react-hot-toast";

interface MessageTemplate {
  id: string;
  name: string;
  content: string;
  image?: string;
  actions: any[];
  createdAt: string;
}

interface PaginationMeta {
  total: number;
  perPage: number;
  currentPage: number;
  lastPage: number;
  firstPage: number;
  firstPageUrl: string;
  lastPageUrl: string;
  nextPageUrl: string | null;
  previousPageUrl: string | null;
}

interface TemplatesTableProps {
  data: MessageTemplate[];
  meta?: PaginationMeta;
  deleteTemplate: (data: FormData) => Promise<void>;
}

export default function TemplatesTable({ data, meta, deleteTemplate }: TemplatesTableProps) {
  const router = useRouter();

  const handleDeleteTemplate = async (id: string) => {
    if (confirm("Are you sure you want to delete this template?")) {
      const formData = new FormData();
      formData.append("id", id);

      toast.promise(deleteTemplate(formData), {
        loading: "Deleting template...",
        success: "Template deleted successfully!",
        error: "Failed to delete template",
      });
    }
  };

  const columns = [
    {
      name: "Name",
      uid: "name",
      renderCell: (template: MessageTemplate) => (
        <div className="flex flex-col">
          <p className="text-bold text-small capitalize">{template.name}</p>
          <p className="text-bold text-tiny capitalize text-default-400">{template.content.substring(0, 50)}...</p>
        </div>
      ),
    },
    {
      name: "Created",
      uid: "createdAt",
      sortable: true,
      renderCell: (template: MessageTemplate) => (
        <p>{new Date(template.createdAt).toLocaleDateString()}</p>
      ),
    },
    {
      name: "Actions",
      uid: "actions",
      align: "center",
      renderCell: (template: MessageTemplate) => (
        <div className="flex justify-center items-center gap-2">
          <Button
            isIconOnly
            size="sm"
            variant="light"
            color="primary"
            onPress={() => router.push(`/messages/templates/${template.id}/edit`)}
          >
            <Icon name="icon-[heroicons--pencil-square-20-solid]" classNames="text-primary" />
          </Button>
          {/* TODO: Implement template preview feature
          <Button
            isIconOnly
            size="sm"
            variant="light"
            color="primary"
            onPress={() => router.push(`/messages/templates/${template.id}/preview`)}
          >
            <Icon name="icon-[heroicons--eye-20-solid]" classNames="text-primary" />
          </Button>
          */}
          <Button
            isIconOnly
            size="sm"
            variant="light"
            color="danger"
            onPress={() => handleDeleteTemplate(template.id)}
          >
            <Icon name="icon-[heroicons--trash-20-solid]" classNames="text-danger" />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      title="Templates"
      columns={columns}
      data={data}
      meta={meta}
      action={
        <Button
          color="primary"
          startContent={<Icon name="icon-[heroicons--plus-20-solid]" />}
          onPress={() => router.push("/messages/templates/create")}
        >
          Create Template
        </Button>
      }
    />
  );
}
