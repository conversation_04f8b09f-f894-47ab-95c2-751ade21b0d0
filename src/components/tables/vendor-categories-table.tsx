"use client";

import Link from "next/link";
import { CustomTable } from "../custom-table";
import { Button } from "@/components/ui/button";
import { Icon } from "@/components/icon";
import VendorCategoryEditForm from "../forms/vendor-category-edit-form";

function VendorCategoriesTable({
  data,
  meta,
  typeDetails,
  storeVendorCategory,
  deleteVendorCategory,
  updateVendorCategory,
  typeId,
  CreateForm,
}: {
  data: VendorCategory[];
  meta: PaginationMeta;
  typeDetails: string;
  storeVendorCategory: (data: FormData) => Promise<void>;
  deleteVendorCategory: (data: FormData) => Promise<void>;
  updateVendorCategory: (data: FormData) => Promise<void>;
  typeId: string;
  CreateForm: React.ComponentType<{
    storeVendorCategory: (data: FormData) => Promise<void>;
    typeId: string;
  }>;
}) {
  return (
    <CustomTable
      title=""
      columns={[
        {
          uid: "name",
          name: "Category",
          renderCell: (category) => (
            <Link href={`/vendors/categories/${category.id}`}>
              {category.name}
            </Link>
          ),
        },
        { uid: "details", name: "Details" },
        {
          uid: "actions",
          name: "Actions",
          renderCell: (category) => {
            return (
              <div className="flex space-x-2">
                <VendorCategoryEditForm
                  category={category}
                  updateVendorCategory={updateVendorCategory}
                />

                <form action={deleteVendorCategory} className="btn btn-danger">
                  <input type="hidden" name="id" value={category.id} />
                  <Button variant="outline" size={"icon"} type="submit">
                    <Icon
                      name="icon-[mingcute--delete-2-line]"
                      classNames="text-red-500"
                    />
                  </Button>
                </form>
              </div>
            );
          },
        },
      ]}
      data={data}
      meta={meta}
      action={
        <div className="mb-4 flex justify-between px-4">
          <p className="text-lg font-bold">{typeDetails}</p>
          <CreateForm
            storeVendorCategory={storeVendorCategory}
            typeId={typeId}
          />
        </div>
      }
    />
  );
}

export default VendorCategoriesTable; 