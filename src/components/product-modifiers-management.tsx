"use client";

import { useState, useEffect } from "react";
import { CustomTable, useDeleteConfirmation } from "@/components/custom-table";
import { ActionButton } from "@/components/ui/action-button";
import { Icon } from "@/components/icon";
import {
  getProductModifiers,
  attachModifierToProduct,
  detachModifierFromProduct,
  getAvailableModifiers,
  ProductModifierRelationship
} from "@/actions/product-modifiers";
import { Modifier } from "@/types/modifiers";
import toast from "react-hot-toast";
import { Card, CardBody, CardHeader, Select, SelectItem, Chip, Input } from "@nextui-org/react";
import Link from "next/link";

interface ProductModifiersManagementProps {
  productId: string;
  vendorId: string;
}

export function ProductModifiersManagement({ productId, vendorId }: ProductModifiersManagementProps) {
  const [attachedModifiers, setAttachedModifiers] = useState<ProductModifierRelationship[]>([]);
  const [allVendorModifiers, setAllVendorModifiers] = useState<Modifier[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedModifier, setSelectedModifier] = useState<string>("");
  const [isAttaching, setIsAttaching] = useState(false);

  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  // Load data
  useEffect(() => {
    loadData();
  }, [productId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadData = async () => {
    try {
      setLoading(true);
      const [attached, allModifiers] = await Promise.all([
        getProductModifiers(productId),
        getAvailableModifiers(vendorId)
      ]);
      setAttachedModifiers(attached);
      setAllVendorModifiers(allModifiers);
    } catch (error) {
      console.error("Error loading modifiers:", error);
      toast.error("Failed to load modifiers");
    } finally {
      setLoading(false);
    }
  };

  const handleAttach = async () => {
    if (!selectedModifier) {
      toast.error("Please select a modifier to attach");
      return;
    }

    setIsAttaching(true);
    try {
      const formData = new FormData();
      formData.append('productId', productId);
      formData.append('modifierId', selectedModifier);

      const result = await attachModifierToProduct(formData);

      if (result.success) {
        toast.success("Modifier attached successfully");
        setSelectedModifier("");
        await loadData();
      } else {
        toast.error(result.error || "Failed to attach modifier");
      }
    } catch (error) {
      console.error("Error attaching modifier:", error);
      toast.error("Failed to attach modifier");
    } finally {
      setIsAttaching(false);
    }
  };

  const handleDetach = async (modifierId: string) => {
    try {
      const formData = new FormData();
      formData.append('productId', productId);
      formData.append('modifierId', modifierId);

      const result = await detachModifierFromProduct(formData);

      if (result.success) {
        toast.success("Modifier detached successfully");
        await loadData();
      } else {
        toast.error(result.error || "Failed to detach modifier");
      }
    } catch (error) {
      console.error("Error detaching modifier:", error);
      toast.error("Failed to detach modifier");
    }
  };

  // Create a map of attached modifier IDs for quick lookup
  const attachedModifierIds = new Set(attachedModifiers.map(m => m.id));

  // Get available modifiers that are not attached and filter by search term
  const availableToAttach = allVendorModifiers.filter(m => 
    !attachedModifierIds.has(m.id) && 
    m.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const columns = [
    { key: "name", label: "Name" },
    { key: "type", label: "Type" },
    { key: "priceAdjustment", label: "Price Adjustment" },
    { key: "status", label: "Status" },
    { key: "actions", label: "Actions" }
  ];

  const rows = allVendorModifiers.map(modifier => {
    const isAttached = attachedModifierIds.has(modifier.id);
    const attachedModifier = attachedModifiers.find(am => am.id === modifier.id);

    return {
      id: modifier.id,
      name: modifier.name,
      type: (
        <Chip
          size="sm"
          variant="flat"
          color={
            modifier.type === 'condiment' ? 'primary' :
            modifier.type === 'preparation' ? 'secondary' :
            'success'
          }
        >
          {modifier.type}
        </Chip>
      ),
      priceAdjustment: isAttached && attachedModifier
        ? `KES ${parseFloat(attachedModifier.priceAdjustment).toFixed(2)}`
        : `KES ${parseFloat(modifier.default_price_adjustment?.toString() || '0').toFixed(2)}`,
      status: isAttached ? (
        <Chip size="sm" color="success" variant="flat">Attached</Chip>
      ) : (
        <Chip size="sm" color="default" variant="flat">Available</Chip>
      ),
      actions: isAttached ? (
        <ActionButton
          size="sm"
          variant="outline"
          onClick={() => openDeleteDialog(
            modifier.id,
            modifier.name,
            async () => {
              await handleDetach(modifier.id);
            }
          )}
        >
          <Icon name="icon-[mingcute--delete-2-line]" className="w-4 h-4" />
          Detach
        </ActionButton>
      ) : (
        <ActionButton
          size="sm"
          variant="outline"
          onClick={() => {
            setSelectedModifier(modifier.id);
            handleAttach();
          }}
          disabled={isAttaching}
        >
          <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
          Attach
        </ActionButton>
      )
    };
  });

  return (
    <div className="space-y-6">
      {/* Header with Add Modifiers button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Product Modifiers</h3>
          <p className="text-sm text-gray-600">Manage modifier options for this product</p>
        </div>
        <Link href="/products/modifiers/create">
          <ActionButton color="primary">
            <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
            Add Modifiers
          </ActionButton>
        </Link>
      </div>

      {/* Search and Filter Section */}
      <Card>
        <CardHeader>
          <h4 className="text-md font-medium">Filter Modifiers</h4>
        </CardHeader>
        <CardBody>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <Input
                label="Search Modifiers"
                placeholder="Search available modifiers"
                value={searchTerm}
                onValueChange={setSearchTerm}
                startContent={<Icon name="icon-[mingcute--search-line]" className="w-4 h-4 text-gray-400" />}
              />
            </div>
            <div className="flex-1">
              <Select
                label="Select Modifier to Attach"
                placeholder="Choose a modifier to attach"
                selectedKeys={selectedModifier ? [selectedModifier] : []}
                onSelectionChange={(keys) => {
                  const selected = Array.from(keys)[0] as string;
                  setSelectedModifier(selected || "");
                }}
              >
                {availableToAttach.map((modifier) => (
                  <SelectItem key={modifier.id} value={modifier.id}>
                    {modifier.name} ({modifier.type}) - KES {parseFloat(modifier.default_price_adjustment?.toString() || '0').toFixed(2)}
                  </SelectItem>
                ))}
              </Select>
            </div>
            <ActionButton
              color="primary"
              onClick={handleAttach}
              disabled={!selectedModifier || isAttaching}
              loading={isAttaching}
            >
              <Icon name="icon-[mingcute--add-line]" className="w-4 h-4" />
              Attach
            </ActionButton>
          </div>
        </CardBody>
      </Card>

      {/* Available Modifiers Table */}
      <CustomTable
        title="Available Modifiers"
        data={rows}
        columns={columns}
        loading={loading}
        emptyContent="No modifiers available for this vendor"
      />

      <DeleteConfirmationDialog />
    </div>
  );
}
