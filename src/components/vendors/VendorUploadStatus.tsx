'use client';

import { Card, CardBody, Progress, Button } from '@nextui-org/react';
import { CheckCircle2, XCircle, AlertCircle, RefreshCw } from 'lucide-react';

interface VendorUploadStatusProps {
  status: 'idle' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  error?: string;
  onRetry?: () => void;
}

export default function VendorUploadStatus({
  status,
  progress,
  error,
  onRetry
}: VendorUploadStatusProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="size-6 text-success" />;
      case 'error':
        return <XCircle className="size-6 text-danger" />;
      case 'processing':
        return <RefreshCw className="size-6 text-primary animate-spin" />;
      default:
        return <AlertCircle className="size-6 text-warning" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'idle':
        return 'Ready to upload';
      case 'uploading':
        return 'Uploading file...';
      case 'processing':
        return 'Processing vendors...';
      case 'completed':
        return 'Upload completed successfully';
      case 'error':
        return 'Upload failed';
    }
  };

  // Determine if we should show the progress bar
  const showProgress = status === 'uploading' || status === 'processing';

  return (
    <Card className="w-full">
      <CardBody>
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-3">
            {getStatusIcon()}
            <div className="flex flex-col">
              <h3 className="text-lg font-semibold">{getStatusText()}</h3>
              {error && (
                <p className="text-sm text-danger">{error}</p>
              )}
            </div>
          </div>

          {showProgress && (
            <Progress
              value={progress}
              className="max-w-md"
              color="primary"
            />
          )}

          {status === 'error' && onRetry && (
            <Button
              color="primary"
              variant="flat"
              onPress={onRetry}
              startContent={<RefreshCw className="size-4" />}
            >
              Retry Upload
            </Button>
          )}
        </div>
      </CardBody>
    </Card>
  );
} 