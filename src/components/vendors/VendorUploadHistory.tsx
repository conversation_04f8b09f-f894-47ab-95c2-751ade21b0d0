'use client';

import { useState, useEffect, useCallback } from 'react';
import { Card, CardBody, Button, Chip, Tooltip, Pagination } from '@nextui-org/react';
import { Download, XCircle, RefreshCw } from 'lucide-react';
import { JobStatusResponse } from '@/lib/api';
import { getVendorUploadHistory, downloadUploadResults, cancelUpload } from '@/actions/vendor-uploads';
import toast from 'react-hot-toast';

interface UploadHistoryItem {
  id: string;
  fileName: string;
  uploadedBy: {
    id: string;
    name: string;
  };
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  totalRows: number;
  processedRows: number;
  failedRows: number;
  errors?: Array<{ row: number; message: string }>;
  createdAt: string;
  updatedAt: string;
}

export default function VendorUploadHistory() {
  const [history, setHistory] = useState<UploadHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [perPage] = useState(10);

  const fetchHistory = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await getVendorUploadHistory(page, perPage);
      if (result.history) {
        setHistory(result.history.uploads);
        setTotal(result.history.total);
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error('Error fetching upload history:', error);
      toast.error('Failed to load upload history');
    } finally {
      setIsLoading(false);
    }
  }, [page, perPage]);

  useEffect(() => {
    fetchHistory();
  }, [fetchHistory]);

  const handleDownloadResults = async (jobId: string) => {
    try {
      const result = await downloadUploadResults(jobId);
      if (result.url) {
        // Create a temporary link and trigger download
        const link = document.createElement('a');
        link.href = result.url;
        link.download = `vendor-upload-${jobId}-results.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success('Download started');
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error('Error downloading results:', error);
      toast.error('Failed to download results');
    }
  };

  const handleCancelUpload = async (jobId: string) => {
    try {
      const result = await cancelUpload(jobId);
      if (result.success) {
        toast.success('Upload cancelled');
        fetchHistory();
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error('Error cancelling upload:', error);
      toast.error('Failed to cancel upload');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'processing':
      case 'queued':
        return 'primary';
      case 'failed':
        return 'danger';
      default:
        return 'default';
    }
  };

  return (
    <Card>
      <CardBody>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Upload History</h3>
          <Button
            isIconOnly
            variant="light"
            onPress={fetchHistory}
            isLoading={isLoading}
          >
            <RefreshCw className="size-4" />
          </Button>
        </div>

        {history.length === 0 ? (
          <div className="text-center py-8 text-default-500">
            No upload history available
          </div>
        ) : (
          <div className="space-y-4">
            {history.map((item) => (
              <div
                key={item.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="space-y-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{item.fileName}</span>
                    <Chip
                      size="sm"
                      color={getStatusColor(item.status)}
                      variant="flat"
                    >
                      {item.status}
                    </Chip>
                  </div>
                  <div className="text-sm text-default-500">
                    <span>Uploaded by {item.uploadedBy.name}</span>
                    <span className="mx-2">•</span>
                    <span>{new Date(item.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="text-sm text-default-500">
                    {item.processedRows} of {item.totalRows} rows processed
                    {item.failedRows > 0 && (
                      <span className="text-danger ml-2">
                        ({item.failedRows} failed)
                      </span>
                    )}
                  </div>
                  {item.errors && item.errors.length > 0 && (
                    <div className="text-sm text-danger">
                      {item.errors.length} error(s) found
                    </div>
                  )}
                </div>

                <div className="flex items-center gap-2">
                  {item.status === 'completed' && (
                    <Tooltip content="Download Results">
                      <Button
                        isIconOnly
                        variant="flat"
                        color="primary"
                        onPress={() => handleDownloadResults(item.id)}
                      >
                        <Download className="size-4" />
                      </Button>
                    </Tooltip>
                  )}
                  {(item.status === 'processing' || item.status === 'queued') && (
                    <Tooltip content="Cancel Upload">
                      <Button
                        isIconOnly
                        variant="flat"
                        color="danger"
                        onPress={() => handleCancelUpload(item.id)}
                      >
                        <XCircle className="size-4" />
                      </Button>
                    </Tooltip>
                  )}
                </div>
              </div>
            ))}

            <div className="flex justify-center mt-4">
              <Pagination
                total={Math.ceil(total / perPage)}
                page={page}
                onChange={setPage}
              />
            </div>
          </div>
        )}
      </CardBody>
    </Card>
  );
} 