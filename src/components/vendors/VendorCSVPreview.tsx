'use client';

import { Card, CardBody } from '@nextui-org/react';
import { CheckCircle2, XCircle, AlertCircle } from 'lucide-react';

interface VendorCSVRow {
  name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  tax_id: string;
  status: string;
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

interface VendorCSVPreviewProps {
  data: VendorCSVRow[];
  errors: ValidationError[];
}

export default function VendorCSVPreview({ data, errors }: VendorCSVPreviewProps) {
  const getRowErrors = (rowIndex: number) => {
    return errors.filter(error => error.row === rowIndex + 1);
  };

  const getFieldError = (rowIndex: number, field: string) => {
    return errors.find(error => error.row === rowIndex + 1 && error.field === field);
  };

  const getValidationStatus = (rowIndex: number): 'valid' | 'warning' | 'error' => {
    const rowErrors = getRowErrors(rowIndex);
    if (rowErrors.length === 0) return 'valid';
    if (rowErrors.length > 2) return 'error';
    return 'warning';
  };

  const ValidationIcon = ({ status }: { status: 'valid' | 'warning' | 'error' }) => {
    switch (status) {
      case 'valid':
        return <CheckCircle2 className="size-4 text-success" />;
      case 'warning':
        return <AlertCircle className="size-4 text-warning" />;
      case 'error':
        return <XCircle className="size-4 text-danger" />;
    }
  };

  // Get column headers
  const headers = Object.keys(data[0] || {});

  return (
    <Card className="w-full">
      <CardBody>
        <div className="flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">CSV Preview</h3>
            <div className="flex items-center gap-2 text-sm text-default-500">
              <span>Total Rows: {data.length}</span>
              <span>•</span>
              <span>Errors: {errors.length}</span>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  {headers.map((header) => (
                    <th key={header} scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.map((row, rowIndex) => {
                  const status = getValidationStatus(rowIndex);
                  return (
                    <tr key={rowIndex}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <ValidationIcon status={status} />
                      </td>
                      {headers.map((header) => {
                        const value = row[header as keyof VendorCSVRow];
                        const error = getFieldError(rowIndex, header);
                        return (
                          <td key={header} className={`px-6 py-4 whitespace-nowrap ${error ? 'text-danger' : ''}`}>
                            {value}
                            {error && (
                              <div className="text-xs text-danger">{error.message}</div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </CardBody>
    </Card>
  );
} 