'use client';

import { useState, useCallback, useEffect, useRef } from 'react';
import { Card, CardBody, Button, Progress, Link } from '@nextui-org/react';
import { Upload, Download } from 'lucide-react';
import <PERSON> from 'papaparse';
import { uploadBulkVendorsAction } from '@/actions/vendors';
import { getJobStatusAction } from '@/actions/jobs';
import { JobStatusResponse } from '@/lib/api';
import toast from 'react-hot-toast';

interface VendorCSVRow {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  title?: string;
  gender?: 'Male' | 'Female' | 'Other';
  dob?: string;
  idpass?: string;
  vendor_name: string;
  vendor_kra: string;
  vendor_details?: string;
  vendor_email?: string;
  vendor_phone?: string;
  vendor_reg?: string;
  vendor_permit?: string;
  vendor_category_ids?: string;
  vendor_category_slugs?: string;
  service_id?: string;
  service_slug?: string;
  branch_name?: string;
  branch_location_address?: string;
  branch_location_lat?: number;
  branch_location_lng?: number;
  branch_details?: string;
  branch_email?: string;
  branch_phone?: string;
  staff_identifier?: string;
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

const POLLING_INTERVAL = 3000; // Poll job status every 3 seconds
const MAX_POLLING_ERRORS = 5; // Stop polling after 5 consecutive status fetch errors
const MAX_FILE_SIZE_MB = 10; // Maximum allowed file size in Megabytes
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

const REQUIRED_FIELDS = [
  'first_name',
  'last_name',
  'email',
  'phone',
  'vendor_name',
  'vendor_kra'
];

// Add validation constants
const MAX_NAME_LENGTH = 100;
const MAX_VENDOR_NAME_LENGTH = 255;
const MAX_TITLE_LENGTH = 50;
const MAX_IDPASS_LENGTH = 100;
const MAX_KRA_LENGTH = 20;
const MAX_VENDOR_REG_LENGTH = 100;
const MAX_VENDOR_PERMIT_LENGTH = 100;
const MAX_BRANCH_NAME_LENGTH = 255;
const MAX_STAFF_IDENTIFIER_LENGTH = 100;

// Add regex patterns for validation
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
const PHONE_REGEX = /^\+?254[0-9]{9}$/;
const DATE_REGEX = /^\d{4}-\d{2}-\d{2}$/;
const CATEGORY_IDS_REGEX = /^[\w-,]+$/;
const CATEGORY_SLUGS_REGEX = /^[a-z0-9\-,]+$/;
const SERVICE_ID_REGEX = /^[\w-]+$/;
const SERVICE_SLUG_REGEX = /^[a-z0-9-]+$/;

export default function VendorBulkUploadForm() {
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<VendorCSVRow[]>([]);
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [jobId, setJobId] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<JobStatusResponse | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const pollingErrorCount = useRef(0);

  const clearPolling = () => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
      pollingErrorCount.current = 0;
    }
  };

  useEffect(() => {
    if (!jobId) {
      setJobStatus(null);
      clearPolling();
      return;
    }

    const fetchStatus = async () => {
      if (!jobId) return;
      try {
        const result = await getJobStatusAction(jobId);

        if (result.status) {
          pollingErrorCount.current = 0;
          setJobStatus(result.status);
          if (result.status.status === 'completed' || result.status.status === 'failed') {
            clearPolling();
            if (result.status.status === 'completed') {
              toast.success("Bulk upload completed successfully!");
            } else if (result.status.status === 'failed') {
              toast.error(`Bulk upload failed. ${result.status.errors?.length || 0} rows had issues.`);
            }
          }
        } else if (result.error) {
          pollingErrorCount.current++;
          console.warn(`Failed to fetch status for job ${jobId}: ${result.error}`);
        }
      } catch (error: any) {
        pollingErrorCount.current++;
        console.error("Error calling getJobStatusAction:", error);
      }

      if (pollingErrorCount.current >= MAX_POLLING_ERRORS) {
        console.error(`Stopping polling for job ${jobId} after ${MAX_POLLING_ERRORS} consecutive errors.`);
        toast.error("Could not retrieve job status updates. Please check back later or contact support.", { duration: 5000 });
        clearPolling();
      }
    };

    fetchStatus();
    pollingRef.current = setInterval(fetchStatus, POLLING_INTERVAL);
    return () => clearPolling();
  }, [jobId]);

  const validateRow = (row: VendorCSVRow, index: number): ValidationError[] => {
    const rowErrors: ValidationError[] = [];

    // Check for missing required fields
    REQUIRED_FIELDS.forEach(field => {
      const value = row[field as keyof VendorCSVRow];
      if (value === undefined || value === null || (typeof value === 'string' && value.trim() === '')) {
        rowErrors.push({ row: index + 1, field, message: `${field} is required` });
      }
    });

    // Validate first_name length
    if (row.first_name?.trim() && row.first_name.length > MAX_NAME_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'first_name', message: `First name must not exceed ${MAX_NAME_LENGTH} characters` });
    }

    // Validate last_name length
    if (row.last_name?.trim() && row.last_name.length > MAX_NAME_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'last_name', message: `Last name must not exceed ${MAX_NAME_LENGTH} characters` });
    }

    // Validate email format
    if (row.email?.trim() && !EMAIL_REGEX.test(row.email)) {
      rowErrors.push({ row: index + 1, field: 'email', message: 'Invalid email format' });
    }

    // Validate phone format (Kenyan mobile number starting with 254)
    if (row.phone?.trim() && !PHONE_REGEX.test(row.phone)) {
      rowErrors.push({ row: index + 1, field: 'phone', message: 'Phone must be a valid Kenyan mobile number starting with 254 (with or without +)' });
    }

    // Validate title length if provided
    if (row.title?.trim() && row.title.length > MAX_TITLE_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'title', message: `Title must not exceed ${MAX_TITLE_LENGTH} characters` });
    }

    // Validate gender if provided
    if (row.gender && !['Male', 'Female', 'Other'].includes(row.gender)) {
      rowErrors.push({ row: index + 1, field: 'gender', message: 'Gender must be either "Male", "Female", or "Other"' });
    }

    // Validate date format if provided
    if (row.dob && !DATE_REGEX.test(row.dob)) {
      rowErrors.push({ row: index + 1, field: 'dob', message: 'Date must be in yyyy-MM-dd format' });
    }

    // Validate idpass length if provided
    if (row.idpass?.trim() && row.idpass.length > MAX_IDPASS_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'idpass', message: `ID/Passport must not exceed ${MAX_IDPASS_LENGTH} characters` });
    }

    // Validate vendor_name length
    if (row.vendor_name?.trim() && row.vendor_name.length > MAX_VENDOR_NAME_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'vendor_name', message: `Vendor name must not exceed ${MAX_VENDOR_NAME_LENGTH} characters` });
    }

    // Validate vendor_kra length
    if (row.vendor_kra?.trim() && row.vendor_kra.length > MAX_KRA_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'vendor_kra', message: `KRA PIN must not exceed ${MAX_KRA_LENGTH} characters` });
    }

    // Validate vendor_reg length if provided
    if (row.vendor_reg?.trim() && row.vendor_reg.length > MAX_VENDOR_REG_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'vendor_reg', message: `Vendor registration number must not exceed ${MAX_VENDOR_REG_LENGTH} characters` });
    }

    // Validate vendor_permit length if provided
    if (row.vendor_permit?.trim() && row.vendor_permit.length > MAX_VENDOR_PERMIT_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'vendor_permit', message: `Vendor permit must not exceed ${MAX_VENDOR_PERMIT_LENGTH} characters` });
    }

    // Validate vendor_category_ids format if provided
    if (row.vendor_category_ids?.trim() && !CATEGORY_IDS_REGEX.test(row.vendor_category_ids)) {
      rowErrors.push({ row: index + 1, field: 'vendor_category_ids', message: 'Invalid vendor category IDs format' });
    }

    // Validate vendor_category_slugs format if provided
    if (row.vendor_category_slugs?.trim() && !CATEGORY_SLUGS_REGEX.test(row.vendor_category_slugs)) {
      rowErrors.push({ row: index + 1, field: 'vendor_category_slugs', message: 'Invalid vendor category slugs format' });
    }

    // Validate service_id format if provided
    if (row.service_id?.trim() && !SERVICE_ID_REGEX.test(row.service_id)) {
      rowErrors.push({ row: index + 1, field: 'service_id', message: 'Invalid service ID format' });
    }

    // Validate service_slug format if provided
    if (row.service_slug?.trim() && !SERVICE_SLUG_REGEX.test(row.service_slug)) {
      rowErrors.push({ row: index + 1, field: 'service_slug', message: 'Invalid service slug format' });
    }

    // Validate branch_name length if provided
    if (row.branch_name?.trim() && row.branch_name.length > MAX_BRANCH_NAME_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'branch_name', message: `Branch name must not exceed ${MAX_BRANCH_NAME_LENGTH} characters` });
    }

    // Validate latitude if provided
    if (row.branch_location_lat !== undefined) {
      const lat = parseFloat(row.branch_location_lat.toString());
      if (isNaN(lat) || lat < -90 || lat > 90) {
        rowErrors.push({ row: index + 1, field: 'branch_location_lat', message: 'Latitude must be between -90 and 90' });
      }
    }

    // Validate longitude if provided
    if (row.branch_location_lng !== undefined) {
      const lng = parseFloat(row.branch_location_lng.toString());
      if (isNaN(lng) || lng < -180 || lng > 180) {
        rowErrors.push({ row: index + 1, field: 'branch_location_lng', message: 'Longitude must be between -180 and 180' });
      }
    }

    // Validate vendor email format if provided
    if (row.vendor_email?.trim() && !EMAIL_REGEX.test(row.vendor_email)) {
      rowErrors.push({ row: index + 1, field: 'vendor_email', message: 'Invalid vendor email format' });
    }

    // Validate vendor phone format if provided
    if (row.vendor_phone?.trim() && !PHONE_REGEX.test(row.vendor_phone)) {
      rowErrors.push({ row: index + 1, field: 'vendor_phone', message: 'Vendor phone must be a valid Kenyan mobile number starting with 254 (with or without +)' });
    }

    // Validate branch email format if provided
    if (row.branch_email?.trim() && !EMAIL_REGEX.test(row.branch_email)) {
      rowErrors.push({ row: index + 1, field: 'branch_email', message: 'Invalid branch email format' });
    }

    // Validate branch phone format if provided
    if (row.branch_phone?.trim() && !PHONE_REGEX.test(row.branch_phone)) {
      rowErrors.push({ row: index + 1, field: 'branch_phone', message: 'Branch phone must be a valid Kenyan mobile number starting with 254 (with or without +)' });
    }

    // Validate staff_identifier length if provided
    if (row.staff_identifier?.trim() && row.staff_identifier.length > MAX_STAFF_IDENTIFIER_LENGTH) {
      rowErrors.push({ row: index + 1, field: 'staff_identifier', message: `Staff identifier must not exceed ${MAX_STAFF_IDENTIFIER_LENGTH} characters` });
    }

    return rowErrors;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;

    if (selectedFile.size > MAX_FILE_SIZE_BYTES) {
      toast.error(`File is too large. Maximum size is ${MAX_FILE_SIZE_MB}MB.`);
      return;
    }

    setFile(selectedFile);
    setErrors([]);
    setPreview([]);

    Papa.parse(selectedFile, {
      header: true,
      skipEmptyLines: true, // Skip empty lines in the CSV
      complete: (results) => {
        // Check for missing required columns
        const missingColumns = REQUIRED_FIELDS.filter(
          field => !results.meta.fields?.includes(field)
        );

        if (missingColumns.length > 0) {
          setErrors([{
            row: 0,
            field: 'file',
            message: `Missing required columns: ${missingColumns.join(', ')}`
          }]);
          return;
        }

        // Filter out empty rows before validation
        const nonEmptyRows = results.data.filter((row: any) => {
          // Check if any required field has a value
          return REQUIRED_FIELDS.some(field => {
            const value = row[field];
            return value !== undefined && value !== null && value.toString().trim() !== '';
          });
        });

        if (nonEmptyRows.length === 0) {
          setErrors([{
            row: 0,
            field: 'file',
            message: 'CSV file contains no valid data rows. Please check your file.'
          }]);
          return;
        }

        const validationErrors: ValidationError[] = [];
        nonEmptyRows.forEach((row: any, index) => {
          validationErrors.push(...validateRow(row, index));
        });

        setErrors(validationErrors);
        setPreview(nonEmptyRows as VendorCSVRow[]);
      },
      error: (error) => {
        setErrors([{ row: 0, field: 'file', message: error.message }]);
      }
    });
  };

  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true);
    setUploadProgress(0);
    setJobId(null);
    setJobStatus(null);
    pollingErrorCount.current = 0;
    setDebugInfo(null);
    setErrors([]); // Clear any existing errors

    const formData = new FormData();
    formData.append('vendor_setup_csv', file);

    try {
      console.log("Starting upload process with file:", file.name);
      const result = await uploadBulkVendorsAction(formData);
      console.log("Upload result:", result);

      if (result.jobId) {
        setJobId(result.jobId);
        toast.success("File upload initiated successfully! Processing started.");
      } else if (result.error) {
        console.error("Upload failed with error:", result.error);
        toast.error(`Upload failed: ${result.error}`);
      }
    } catch (error: any) {
      console.error("Error calling upload server action:", error);
      console.error("Response data:", error.response?.data);
      
      // Store basic debug info
      setDebugInfo({
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        message: error.message
      });
      
      // Simplified error handling - just show the error message
      const errorMessage = error.response?.data?.message || error.message || "Upload failed. Please try again.";
      toast.error(`Upload failed: ${errorMessage}`);
    } finally {
      setIsUploading(false);
      setUploadProgress(100);
    }
  };

  return (
    <Card className="w-full">
      <CardBody className="gap-4">
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Upload Vendors</h3>
            <Button
              as="a"
              href="/templates/vendors-template.csv"
              download
              variant="flat"
              color="primary"
              startContent={<Download className="size-4" />}
            >
              Download Template
            </Button>
          </div>
          <p className="text-sm text-default-500">
            Upload a CSV file containing vendor information. Make sure to follow the template format.
          </p>
          
          <div className="flex flex-col gap-2 mt-2">
            <div className="flex items-center gap-2">
              <h4 className="text-sm font-medium">Required fields:</h4>
              <div className="flex flex-wrap gap-1">
              {REQUIRED_FIELDS.map(field => (
                  <span key={field} className="px-2 py-0.5 bg-primary-50 text-primary-700 rounded text-xs">
                    {field}
                  </span>
                ))}
              </div>
            </div>
            
            <details className="text-sm text-default-500">
              <summary className="cursor-pointer font-medium">Validation rules</summary>
              <div className="mt-2 pl-4 grid grid-cols-1 md:grid-cols-2 gap-2">
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Email addresses must be in a valid format</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Phone numbers must be Kenyan mobile numbers starting with 254 (with or without +)</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Dates must be in yyyy-MM-dd format</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Gender must be either &quot;Male&quot;, &quot;Female&quot;, or &quot;Other&quot;</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Latitude must be between -90 and 90</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Longitude must be between -180 and 180</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Category IDs and slugs must follow the correct format</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary-500">•</span>
                  <span>Service IDs and slugs must follow the correct format</span>
                </div>
              </div>
            </details>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-4">
            <Button
              as="label"
              color="primary"
              variant="flat"
              startContent={<Upload className="size-4" />}
              className="cursor-pointer"
            >
              Select File
              <input
                type="file"
                className="hidden"
                accept=".csv"
                onChange={handleFileChange}
              />
            </Button>
            {file && (
              <span className="text-sm text-default-500">
                Selected: {file.name}
              </span>
            )}
          </div>

          {preview.length > 0 && (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-default-200">
                <thead>
                  <tr>
                    {Object.keys(preview[0]).map((header) => (
                      <th
                        key={header}
                        className="px-4 py-2 text-left text-sm font-medium text-default-600"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-default-200">
                  {preview.slice(0, 5).map((row, index) => (
                    <tr key={index}>
                      {Object.values(row).map((value, i) => (
                        <td
                          key={i}
                          className="px-4 py-2 text-sm text-default-700"
                        >
                          {value}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {errors.length > 0 && (
            <div className="rounded-lg bg-danger-50 p-4">
              <h4 className="mb-2 text-sm font-medium text-danger">Validation Errors</h4>
              <ul className="list-inside list-disc text-sm text-danger">
                {errors.map((error, index) => (
                  <li key={index}>
                    Row {error.row}: {error.message}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {debugInfo && (
            <div className="rounded-lg bg-warning-50 p-4 border-2 border-warning-500">
              <h4 className="mb-2 text-sm font-medium text-warning-700">Debug Information</h4>
              <div className="text-xs font-bold mb-1">Status: {debugInfo.status} - {debugInfo.statusText}</div>
              <div className="text-xs font-bold mb-1">Message: {debugInfo.message}</div>
              <div className="text-xs font-bold mb-1">Data:</div>
              <pre className="text-xs overflow-auto max-h-60 bg-warning-100 p-2 rounded border border-warning-300">
                {JSON.stringify(debugInfo.data, null, 2)}
              </pre>
            </div>
          )}

          {isUploading && (
            <Progress
              value={uploadProgress}
              className="max-w-md"
              color="primary"
            />
          )}

          <Button
            color="primary"
            onClick={handleUpload}
            isDisabled={!file || errors.length > 0 || isUploading || (!!jobId && jobStatus?.status !== 'completed' && jobStatus?.status !== 'failed')}
          >
            {isUploading ? 'Uploading...' : (jobId && (jobStatus?.status === 'processing' || jobStatus?.status === 'queued') ? 'Processing...' : 'Upload Vendors')}
          </Button>
        </div>
      </CardBody>
    </Card>
  );
} 