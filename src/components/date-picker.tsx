"use client";

import { useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface DatePickerProps {
  value: Date | null;
  onChange: (date: Date | null) => void;
  className?: string;
}

export default function CustomDatePicker({ value, onChange, className = "" }: DatePickerProps) {
  return (
    <DatePicker
      selected={value}
      onChange={onChange}
      dateFormat="yyyy-MM-dd"
      className={`shadow-sm bg-default-50 border border-default-300 text-default-900 sm:text-sm rounded-lg focus:ring-default-600 focus:border-default-600 block w-full p-3 dark:bg-default-700 dark:border-default-600 dark:placeholder-default-400 dark:text-white dark:focus:ring-default-600 dark:focus:border-default-500 ${className}`}
      placeholderText="Select date"
    />
  );
} 