"use client";

import { ReactNode } from "react";

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  actions?: ReactNode;
  className?: string;
}

export default function PageHeader({ 
  title, 
  subtitle, 
  actions, 
  className = "" 
}: PageHeaderProps) {
  return (
    <div className={`mb-6 flex items-center justify-between ${className}`}>
      <div>
        <h1 className="text-2xl font-bold text-default-900 dark:text-white">
          {title}
        </h1>
        {subtitle && (
          <p className="mt-1 text-default-600 dark:text-default-400">
            {subtitle}
          </p>
        )}
      </div>
      {actions && (
        <div className="flex items-center gap-3">
          {actions}
        </div>
      )}
    </div>
  );
}
