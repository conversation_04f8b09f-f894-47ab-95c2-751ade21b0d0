"use client";

import { useState, useEffect } from "react";
import { CustomTable, useDeleteConfirmation } from "@/components/custom-table";
import { ActionButton } from "@/components/ui/action-button";
import { Icon } from "@/components/icon";
import { PackagingOption } from "@/actions/packaging-options";
import { api } from "@/lib/api";
import toast from "react-hot-toast";
import { Card, CardBody, CardHeader, Chip } from "@nextui-org/react";

interface ProductPackagingManagementProps {
  productId: string;
  vendorId: string;
}

interface ProductPackagingRelationship {
  id: string;
  productId: string;
  packagingOptionId: string;
  name: string;
  description?: string;
  price: string;
  isDefault: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
}

export function ProductPackagingManagement({ productId, vendorId }: ProductPackagingManagementProps) {
  const [attachedPackaging, setAttachedPackaging] = useState<ProductPackagingRelationship[]>([]);
  const [allVendorPackaging, setAllVendorPackaging] = useState<PackagingOption[]>([]);
  const [loading, setLoading] = useState(true);

  const { openDeleteDialog, DeleteConfirmationDialog } = useDeleteConfirmation();

  // Load data
  useEffect(() => {
    loadData();
  }, [productId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadData = async () => {
    try {
      setLoading(true);
      const [attached, allPackaging] = await Promise.all([
        getProductPackaging(productId),
        getAvailablePackaging(vendorId)
      ]);
      setAttachedPackaging(attached);
      setAllVendorPackaging(allPackaging);
    } catch (error) {
      console.error("Error loading packaging options:", error);
      toast.error("Failed to load packaging options");
    } finally {
      setLoading(false);
    }
  };

  const getProductPackaging = async (productId: string): Promise<ProductPackagingRelationship[]> => {
    try {
      const response = await api.get<ProductPackagingRelationship[]>(`v1/products/${productId}/packaging`);
      return response || [];
    } catch (error) {
      console.error('Error fetching product packaging:', error);
      return [];
    }
  };

  const getAvailablePackaging = async (vendorId: string): Promise<PackagingOption[]> => {
    try {
      const response = await api.get<{ data: PackagingOption[] }>(`vendors/${vendorId}/packaging-options`, { active: "true", per: "100" });
      return response?.data || [];
    } catch (error) {
      console.error('Error fetching available packaging:', error);
      return [];
    }
  };

  const handleDetach = async (packagingId: string) => {
    try {
      await api.delete(`v1/products/${productId}/packaging/${packagingId}`);
      toast.success("Packaging option detached successfully");
      await loadData();
    } catch (error) {
      console.error("Error detaching packaging:", error);
      toast.error("Failed to detach packaging option");
    }
  };

  // Create a map of attached packaging IDs for quick lookup
  const attachedPackagingIds = new Set(attachedPackaging.map(p => p.packagingOptionId));

  const columns = [
    { key: "name", label: "Name" },
    { key: "description", label: "Description" },
    { key: "price", label: "Price" },
    { key: "status", label: "Status" },
    { key: "actions", label: "Actions" }
  ];

  const rows = allVendorPackaging.map(packaging => {
    const isAttached = attachedPackagingIds.has(packaging.id);
    const attachedPackagingItem = attachedPackaging.find(ap => ap.packagingOptionId === packaging.id);

    return {
      id: packaging.id,
      name: packaging.name,
      description: packaging.description || "No description",
      price: isAttached && attachedPackagingItem
        ? `KES ${parseFloat(attachedPackagingItem.price).toFixed(2)}`
        : `KES ${parseFloat(packaging.price?.toString() || '0').toFixed(2)}`,
      status: isAttached ? (
        <Chip size="sm" color="success" variant="flat">Attached</Chip>
      ) : (
        <Chip size="sm" color="default" variant="flat">Available</Chip>
      ),
      actions: isAttached ? (
        <ActionButton
          size="sm"
          variant="outline"
          onClick={() => openDeleteDialog(
            packaging.id,
            packaging.name,
            async () => {
              await handleDetach(packaging.id);
            }
          )}
        >
          <Icon name="icon-[mingcute--delete-2-line]" className="w-4 h-4" />
          Detach
        </ActionButton>
      ) : null
    };
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Product Packaging Options</h3>
          <p className="text-sm text-gray-600">Manage packaging options for this product</p>
        </div>
      </div>

      {/* Available Packaging Options Table */}
      <CustomTable
        title="Available Packaging Options"
        data={rows}
        columns={columns}
        loading={loading}
        emptyContent="No packaging options attached to this product"
      />

      <DeleteConfirmationDialog />
    </div>
  );
}
